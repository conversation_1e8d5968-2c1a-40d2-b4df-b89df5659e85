<template>
  <header class="py-4 text-center">
    <div class="mb-4">
      <h3 class="mb-1 text-center text-6">{{ detail.title || '-' }}</h3>
    </div>
    <div class="text-sm c-text-secondary">
      <span>发布时间：{{ detail.time }}</span>
      <span class="ml-4">原文链接：<a :href="detail.url" _blank>{{ detail.url }}</a></span>
    </div>
  </header>

  <div>
    <article class="text-base">
      <div v-if="detail.content">
        <div v-for="(text, index) in detail.content.split('\n')" :key="index" class="indent-2rem line-height-8" :class="index % 2 === 0 ? 'mt-4' : 'mt-2'">
          {{ text }}
        </div>
      </div>
      <div v-else class="c-text-secondary">暂无内容</div>
    </article>
  </div>
</template>

<script lang='ts' setup>
import { useRoute } from 'vue-router'
import aiList from './data/aiList.json'

class Detail {
  title: string = ''
  time: string = ''
  url: string = ''
  content: string = ''
}

const detail = ref(new Detail())

const route = useRoute()

onMounted(() => {
  if (route.query.title) {
    const title = route.query.title
    detail.value = aiList.find(item => item.title === title) as any
  }
})
</script>

<style scope>

</style>
