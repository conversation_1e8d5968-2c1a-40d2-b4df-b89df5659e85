<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: 龙兆柒
-->
<!-- 消息列表组件 -->
<template>
  <a-list class="max-w[300px]" item-layout="vertical" size="small" :data-source="listData" :loading="initLoading">
    <template #renderItem="{ item }">
      <a-list-item key="item.title">
        <template #actions>
          <span>
            <StarOutlined />
            标记
          </span>
          <span>
            <LikeOutlined />
            {{ item.read ? '已读' : '未读' }}
          </span>
          <span>
            <MessageOutlined />
            详情
          </span>
        </template>

        <a-list-item-meta>
          <template #title>
            {{ item.type }}
          </template>
          <template #avatar>
            <a-avatar class="bg-error" :size="42">
              <template #icon>
                <BellOutlined style="color: #fff" />
              </template>
            </a-avatar>
          </template>
          <template #description>
            <span class="content">{{ item.title }}</span>
          </template>
        </a-list-item-meta>
        {{ item.sendDate }}
      </a-list-item>
    </template>
    <template #loadMore>
      <div
        v-if="!initLoading && !loading"
        :style="{ textAlign: 'center', marginTop: '12px', height: '32px', lineHeight: '32px' }"
      >
        <a-button @click="onLoadMore">
          查看更多
        </a-button>
      </div>
    </template>
  </a-list>
</template>

<script setup lang="ts">
import { dateFormat } from '@/utils/util'
import { BellOutlined, LikeOutlined, MessageOutlined, StarOutlined } from '@ant-design/icons-vue'
import { onMounted, reactive, ref } from 'vue'

// import * as signalR from '@microsoft/signalr';

interface ListModel {
  title?: string
  content?: string
  type?: string
  tag?: boolean
  read?: boolean
  sendDate?: string
}

const listData = reactive<ListModel[]>([])
for (let i = 0; i < 5; i++) {
  listData.push({
    title: '这是一条系统通知',
    content:
      '系统通知内容系统通知内容系统通知内容系统通知内容系统通知内容系统通知内容系统通知内容系统通知内容系统通知内容',
    type: '系统通知',
    tag: i % 3 === 0,
    read: i % 4 === 0,
    sendDate: dateFormat(new Date()),
  })
}

const initLoading = ref(false)
const loading = ref(false)

function onLoadMore() {}
onMounted(() => {})
</script>

<style scoped lang="less">
.content {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
:deep(.ant-list-item-meta-title) {
  margin-bottom: 1px;
}
</style>
