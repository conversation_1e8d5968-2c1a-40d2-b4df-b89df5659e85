<template>
  <div>
    <c-pro-table
      ref="tableRef"
      row-key="id" :columns="columns" :api="api.BaseInfoManage.GetPagedAsync"
      immediate show-add-btn operation @add-row="showEditModel()"
    >
      <template #operation="{ record }">
        <a-button type="primary" @click="showEditModel(record)">编辑</a-button>
      </template>
    </c-pro-table>

    <c-modal
      v-model:open="editInfo.open" width="1400px" :title="`编辑${editInfo.data?.description ?? ''}`" ok-text="保存"
      @ok="save()"
    >
      <div class="w-full flex flex-col gap4">
        <c-form layout="inline" class="w-full">
          <c-form-item required label="唯一键">
            <c-input v-model:value="editInfo.data.key!" :disabled="Guid.isNotNull(editInfo.data.id)" placeholder="请填写" />
          </c-form-item>
          <c-form-item required label="描述">
            <c-input v-model:value="editInfo.data.description!" placeholder="请填写描述" />
          </c-form-item>
        </c-form>

        <Child v-model:value="editInfo.jsonData" />
      </div>
    </c-modal>
  </div>
</template>

<script setup lang="ts">
import type { BaseInfo } from '@/api/models'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import type { StandardItem } from './components/useStandardItem'
import * as api from '@/api'
import { Standard } from '@/hooks/useStandard'
import { message } from 'ant-design-vue'
import Child from './components/Child.vue'
import { toSaveData, toStandardEdit } from './components/useStandardItem'

definePage({
  meta: {
    title: '基础数据管理',
    local: true,
  },
})

const compressor = new StandardCompressor()

const tableRef = useTemplateRef('tableRef')

const columns = reactive<ColumnProps<BaseInfo>[]>([
  // { dataIndex: 'id', title: '*数据库主键', key: 'id' },
  { dataIndex: 'key', title: '唯一键', key: 'key' },
  { dataIndex: 'description', title: '描述', key: 'description' },
  // { dataIndex: 'data', title: '*数据', key: 'data' },
  // { dataIndex: 'isHide', title: '*是否隐藏', key: 'isHide', bodyCell: ({ text }) => h('span', text ? '是' : '否') },
])

const editInfo = reactive({
  open: false,
  data: {} as Standard,
  jsonData: [] as StandardItem[],
})

function showEditModel(item?: BaseInfo) {
  if (item) {
    api.BaseInfoManage.GetInfoAsync({ key: item.key! }).then((res) => {
      editInfo.data = res as any
      editInfo.jsonData = toStandardEdit(compressor.decompress(res.data || '{}'), null)
    })
  }
  else {
    editInfo.data = new Standard()
    editInfo.jsonData = []
  }
  editInfo.open = true
}

async function save() {
  if (!editInfo.data.key) {
    message.error('唯一键不能为空')
    return
  }
  await api.BaseInfoManage.Save_PostAsync({
    ...editInfo.data,
    data: compressor.compress(toSaveData(editInfo.jsonData)),
  }).then(() => {
    editInfo.open = false
    tableRef.value?.search()
    message.success('保存成功')
  })
}
</script>

<style scoped></style>
