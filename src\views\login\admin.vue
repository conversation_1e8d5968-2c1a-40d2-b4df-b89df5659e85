<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2025-02-10 11:46:20
 * @LastEditors: 景 彡
-->
<template>
  <div class="gov-login-bg min-h-screen w-full flex items-center justify-center">
    <div class="gov-login-container flex overflow-hidden rounded-2xl shadow-2xl">
      <!-- 左侧登录表单 -->
      <div class="gov-login-form w-96 flex flex-col justify-center bg-white px-16 py-12">
        <div class="text-gov-primary mb-2 text-center text-3xl font-bold">管理后台登录</div>
        <div class="mb-8 text-center text-base text-gray-500 tracking-wide">Admin Console Login</div>
        <a-spin tip="登录中..." :spinning="loading">
          <a-form
            :model="formState" name="basic" :wrapper-col="{ span: 24 }" autocomplete="off"
            @finish="onFinish" @finish-failed="onFinishFailed"
          >
            <a-form-item name="username" :rules="[{ required: true, message: '请输入用户名!' }]">
              <c-input v-model:value="formState.username" size="large" placeholder="用户名">
                <template #prefix>
                  <c-icon-user-outlined />
                </template>
              </c-input>
            </a-form-item>
            <a-form-item name="password" :rules="[{ required: true, message: '请输入密码!' }]">
              <c-input-password v-model:value="formState.password" size="large" placeholder="密码">
                <template #prefix>
                  <c-icon-lock-outlined />
                </template>
              </c-input-password>
            </a-form-item>
            <a-form-item>
              <a-button
                type="primary" html-type="submit" class="gov-login-btn"
              >
                登录
              </a-button>
            </a-form-item>
          </a-form>
        </a-spin>
        <div class="mt-2 flex justify-between text-xs text-gray-400 text-center">
          <span>技术支持：{{ COPYRIGHT }}</span>
        </div>
      </div>
      <!-- 右侧LOGO与介绍 -->
      <div class="gov-login-side bg-gov-primary w-96 flex flex-col items-center justify-center px-16 py-12 text-white">
        <img src="@/assets/images/login-icon.png" class="mb-6 h-24 w-24 border-4 border-white rounded-full shadow-lg" alt="logo">
        <div class="mb-2 text-2xl font-bold tracking-wide">管理后台</div>
        <div class="mb-6 text-center text-base opacity-80">为管理人员提供安全、便捷、高效的后台管理服务</div>
        <div class="mt-auto text-xs opacity-60">© {{ dayjs().year() }} 东盟大数据平台</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { onMounted } from 'vue'
import { useLogin } from './hook/useLogin'

const { formState, onFinish, onFinishFailed, loading } = useLogin()
const COPYRIGHT = import.meta.env.VITE_APP_COPYRIGHT

onMounted(() => { })
</script>

<style scoped lang="less">
.gov-login-bg {
  min-height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #1a2a6c 0%, #27408b 60%, #00b4db 100%);
  background-repeat: no-repeat;
  background-size: cover;
}
.gov-login-container {
  min-width: 720px;
  min-height: 480px;
  background: rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
}
.gov-login-form {
  min-width: 380px;
  background: #fff;
}
.gov-login-side {
  min-width: 320px;
  background: linear-gradient(135deg, #1a2a6c 0%, #27408b 60%, #00b4db 100%);
}
.gov-login-btn {
  width: 100%;
  height: 44px;
  border-radius: 24px;
  font-size: 18px;
  font-weight: bold;
  background: linear-gradient(90deg, #27408b 0%, #09b388 100%);
  border: none;
}
.text-gov-primary {
  color: #27408b;
}
</style>
