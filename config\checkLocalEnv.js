/*
 * @Description: ^_^
 * @Author: sharebravery
 * @Date: 2022-11-01 17:02:57
 */
import path from 'node:path'
import process from 'node:process'
import fs from 'fs-extra'

/**
 *检查文件是否存在于根目录中
 *
 * @param {*} filename
 * @return {*}
 */
function checkFileExistInRoot(filename) {
  return new Promise((resolve) => {
    const root = process.cwd()
    const filePath = path.join(root, filename)
    fs.access(filePath, (err) => {
      if (err)
        resolve(false)
      else
        resolve(true)
    })
  })
}

const FILE_NAME = '.env.dev.local'

async function main() {
  try {
    const exist = await checkFileExistInRoot(FILE_NAME)

    if (!exist) {
      const content = `###本地开发运行环境配置 会被git忽略

#本地采集端
#VITE_APP_PROXY_TARGET = http://localhost:5035
#服务端
#VITE_APP_PROXY_TARGET = https://localhost:44352
`

      fs.writeFile(FILE_NAME, content, (err) => {
        if (err)
          console.error(err)
      })
    }
  }
  catch (error) {
    console.log('%c [ error ]-40', 'font-size:13px; background:pink; color:#bf2c9f;', error)
  }
}

main()
