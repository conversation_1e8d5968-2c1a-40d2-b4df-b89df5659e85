<template>
  <c-input-number
    v-model:value="value"
    :min="0.01"
    :max="1"
    :step="0.001"
    :formatter="formatValue"
    :parser="parseValue"
  />
</template>

<script setup lang="ts">
import { Decimal } from 'decimal.js'

const value = defineModel('value', {
  type: Number,
  default: 0.1,
})

function formatValue(value: number) {
  return `${new Decimal(value).times(100).toFixed(2)}%`
}
function parseValue(value: string) {
  return new Decimal(value.replace('%', '')).dividedBy(100).toNumber()
}
</script>

<style scoped>

</style>
