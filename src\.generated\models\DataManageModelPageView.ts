import { DataType } from "./DataType";
import { UserTagRes } from "./UserTagRes";
export class DataManageModelPageView {
  /**id*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
  /**文章类型*/
  dataType: DataType = 0;
  /**AI分析结果*/
  ai?: string | null | undefined = null;
  /**情感分析分数（0~100 有小数）*/
  emotion?: number | null | undefined = null;
  /**链接地址*/
  link?: string | null | undefined = null;
  /**作者*/
  author?: string | null | undefined = null;
  /**来源*/
  source?: string | null | undefined = null;
  /**时间*/
  time?: Dayjs | null | undefined = null;
  /**标题*/
  title: string = "";
  /**中文标题*/
  titleCn?: string | null | undefined = null;
  /**是否涉我*/
  isChina: boolean = false;
  /**标签*/
  tag?: string[] | null | undefined = [];
  /**领域分类*/
  domain?: string[] | null | undefined = [];
  /**涉及地区*/
  region?: string[] | null | undefined = [];
  hotTags?: string[] | null | undefined = [];
  /**收藏夹*/
  isFavorite: boolean = false;
  /**阅读状态*/
  isRead: boolean = false;
  /**用户自定义标签*/
  userTags?: UserTagRes[] | null | undefined = [];
  /**笔记内容*/
  notes?: string | null | undefined = null;
}
