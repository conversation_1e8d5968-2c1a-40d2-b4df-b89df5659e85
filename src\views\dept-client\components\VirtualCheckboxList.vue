<template>
  <div
    ref="containerRef"
    class="virtual-scroll-container relative overflow-y-auto"
    :style="{ height: `${containerHeight}px` }"
    @scroll="onScroll"
  >
    <div
      class="virtual-scroll-content absolute w-full"
      :style="{ height: `${totalHeight}px` }"
    >
      <div
        class="virtual-scroll-items absolute w-full"
        :style="{ transform: `translateY(${startOffset}px)` }"
      >
        <div
          v-for="(item, index) in visibleItems"
          :key="getItemKey(item, index)"
          class="border-t border-gray-200"
        >
          <div class="w-full flex items-center justify-between">
            <a-checkbox
              :value="item.name"
              :checked="isItemSelected(item)"
              @change="() => handleCheckboxChange(item)"
            >
              <span class="text-base">{{ item.name || 'Unnamed' }}</span>
            </a-checkbox>
            <span class="pr-2 text-sm text-gray-500">{{ item.count }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { KeyValue } from '@/api/models'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

interface Props {
  items: KeyValue[]
  selectedValues: string[]
  itemHeight?: number
  containerHeight?: number
  bufferSize?: number
  onChange?: (values: string[]) => void
}

const props = withDefaults(defineProps<Props>(), {
  itemHeight: 24,
  containerHeight: 400,
  bufferSize: 5,
})

const emit = defineEmits(['update:selectedValues', 'change'])

const containerRef = ref<HTMLElement | null>(null)
const scrollTop = ref(0)
const selectedItems = ref<string[]>([...props.selectedValues])

// Calculate total height of all items
const totalHeight = computed(() => props.items.length * props.itemHeight)

// Calculate visible item indices
const visibleRange = computed(() => {
  const start = Math.floor(scrollTop.value / props.itemHeight)
  const visibleCount = Math.ceil(props.containerHeight / props.itemHeight)
  const startIndex = Math.max(0, start - props.bufferSize)
  const endIndex = Math.min(props.items.length, start + visibleCount + props.bufferSize)

  return { startIndex, endIndex }
})

// Calculate offset for the first visible item
const startOffset = computed(() => {
  return visibleRange.value.startIndex * props.itemHeight
})

// Get visible items based on the calculated range
const visibleItems = computed(() => {
  return props.items.slice(visibleRange.value.startIndex, visibleRange.value.endIndex)
})

// Generate a unique key for each item since there's no id
function getItemKey(item: KeyValue, index: number): string {
  // Use name if available, otherwise use index as fallback
  return item.name ? `item-${item.name}` : `item-index-${index}`
}

// Check if an item is selected
function isItemSelected(item: KeyValue): boolean {
  return item.name ? selectedItems.value.includes(item.name) : false
}

// Handle scroll events
function onScroll(_event: Event) {
  if (containerRef.value) {
    scrollTop.value = containerRef.value.scrollTop
  }
}

// Handle checkbox change
function handleCheckboxChange(item: KeyValue) {
  if (!item.name)
    return // Skip items without a name

  const index = selectedItems.value.indexOf(item.name)

  if (index === -1) {
    selectedItems.value.push(item.name)
  }
  else {
    selectedItems.value.splice(index, 1)
  }

  emit('update:selectedValues', selectedItems.value)
  emit('change', selectedItems.value)

  if (props.onChange) {
    props.onChange(selectedItems.value)
  }
}

// Watch for changes in the selected values from parent
watch(() => props.selectedValues, (newValues) => {
  selectedItems.value = [...newValues]
}, { deep: true })

// Set up resizing and cleanup
onMounted(() => {
  window.addEventListener('resize', onScroll)
})

onUnmounted(() => {
  window.removeEventListener('resize', onScroll)
})
</script>

<style scoped lang="less">
.virtual-scroll-container {
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  &::-webkit-scrollbar {
    width: 2px; /* 设置垂直滚动条的宽度 */
  }
}
</style>
