import type { SortModel } from '@/api/models'
import type { SorterResult } from 'ant-design-vue/es/table/interface'
import type { DataApi, PageParams } from 'ch2-components/types/global.types'
import type ProTable from 'ch2-components/types/pro-table/src/ProTable.vue'
import type { ColumnProps, ProTableProps } from 'ch2-components/types/pro-table/types'
import type { ComponentExposed } from 'vue-component-type-helpers'
import { QueryFormat, QueryModel, QueryType, SearchCriteria, SortType } from '@/api/models'
import { keyBy, reduce } from 'lodash-es'

type Params<T> = PageParams & Record<keyof T, any>

export function tableSearch<T extends Record<string, any>>(api: DataApi<T>, params: Partial<Params<T>>[], sorter?: SortModel[], columns?: ColumnProps<T>[], select: Array<keyof T> = []) {
  const columnsMap = keyBy(columns, 'dataIndex')

  const search = new SearchCriteria()

  const _params = reduce(params, (accumulator, current) => {
    return { ...accumulator, ...current }
  }, {} as Params<T>)

  const { offset = 0, limit = 10 } = _params

  /** 如果存在columns且没有select，使用columns的key作为寻找字段的方式，返回指定的字段以减小请求资源大小 */
  if (!select && columns) {
    select = columns.map(v => v.key).filter(Boolean) as any
  }

  search.get = {
    offset,
    limit,
    notCounted: false,
    select: select as any,
  }

  search.query = { ...new QueryModel(), format: QueryFormat.AND }

  search.query.subqueries = []

  let key: keyof T
  for (key in _params) {
    if (key === 'offset' || key === 'limit' || _params[key] == null)
      continue
    search.query.subqueries!.push({
      propertyName: key,
      type: columnsMap[key]?.queryType ?? QueryType.Eq,
      obj: _params[key],
      format: QueryFormat.Query,
      subqueries: [],
    })
  }

  if (search.query.subqueries.length === 0) {
    delete search.query
  }
  else {
    search.query.propertyName = search.query.subqueries[0]?.propertyName
  }

  search.sort = sorter ?? [{
    propertyName: 'id',
    type: SortType.Desc,
  }]

  return api(search)
}

export function useTableSearch<T extends Record<string, any>, Col>(refKey: string, api: DataApi<T>, columns: Col) {
  const tableRef = useTemplateRef<ComponentExposed<typeof ProTable>>(refKey)

  const querySorter = ref<SortModel[] | undefined>()

  const queryBind = computed<Partial<ProTableProps<T, DataApi<T>>>>(() => ({
    api: (...args: any) => tableSearch(api, args, querySorter.value, columns as any) as any,
    columns: columns as ColumnProps<T>[],
    onChange(_pagination, _filters, sorter) {
      sorterChange(sorter)
    },
  }))

  function sorterChange(sorter: SorterResult<T> | SorterResult<T>[]) {
    let _sorter: SorterResult<T>[] | null = null
    if (!Array.isArray(sorter)) {
      if (sorter.column != null) {
        _sorter = [sorter]
      }
    }
    else {
      _sorter = sorter
    }

    querySorter.value = _sorter?.sort((a: any, b: any) => {
      return a.column?.sorter?.multiple - b.column?.sorter?.multiple
    }).map((v) => {
      return {
        propertyName: v.column!.dataIndex!,
        type: v.order === 'ascend' ? SortType.Asc : SortType.Desc,
      } as SortModel
    })

    tableRef.value?.search()
  }

  const rowSelection = reactive({
    selectedRowKeys: [] as string[],
    selectedRows: [] as T[],
    onChange: (selectedRowKeys: any, rows: any) => {
      rowSelection.selectedRowKeys = selectedRowKeys
      rowSelection.selectedRows = rows
    },
  })

  return {
    tableRef,
    queryBind: queryBind as any as Omit<ProTableProps<T, DataApi<T>>, 'rowKey'>,
    rowSelection,
  }
}
