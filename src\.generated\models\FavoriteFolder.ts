import { FolderType } from "./FolderType";
import { User } from "./User";
import { FavoriteData } from "./FavoriteData";
/**收藏夹*/
export class FavoriteFolder {
  /**名称*/
  name: string = "";
  /**备注*/
  remark?: string | null | undefined = null;
  /**类型*/
  type: FolderType = 0;
  /**创建时间*/
  createdAt: Dayjs = dayjs();
  /**所属用户*/
  clientUserId: GUID = "00000000-0000-0000-0000-000000000000";
  /**用户*/
  clientUser?: User | null | undefined = null;
  /**父级*/
  parentId?: GUID = null;
  /**收藏夹*/
  parent?: FavoriteFolder | null | undefined = null;
  /**二级收藏夹*/
  children?: FavoriteFolder[] | null | undefined = [];
  /**文章数据*/
  datas?: FavoriteData[] | null | undefined = [];
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
