import { ref } from 'vue'
import type { ChartDatasetCell } from '@/.generated/models/ChartDatasetCell'
import dayjs from 'dayjs'
import { NavigationGuardNextCallback } from 'vue-router'
import { ChartFieldDefinition } from '@/api/models'

// 空值占位符常量
export const EMPTY_PLACEHOLDER = '__EMPTY_INDICATOR_VALUE__'

export const INDICATOR_FIELDS = [Object.assign(new ChartFieldDefinition(), {
  name: '国家',
  type: 0, // String
  unit: null,
  isEdit: false,
  isIndicator: false,
  description: '国家或地区名称',
}), Object.assign(new ChartFieldDefinition(), {
  name: '指标值',
  type: 1, // Number
  unit: null,
  isEdit: true,
  isIndicator: true,
  description: '具体的指标数值',
}), Object.assign(new ChartFieldDefinition(), {
  name: '透视值',
  type: 0, // String
  unit: null,
  isEdit: true,
  isIndicator: false,
  description: '用于透视的维度值，如年份、季度等',
})]

export interface IndicatorDetailData {
  country: string
  pivotValue: string
  currentValue: string
  currentVia: string
  lastUpdated: string | null
  updateType: string
  url: string
  remarks: string
  history: ChartDatasetCell[]
}


export function getViaColor(via?: string | NavigationGuardNextCallback): string {
  switch (via) {
    case '官方数据': return 'green'
    case '非官方数据': return 'orange'
    case '后台计算': return 'blue'
    default: return 'default'
  }
}

export function useIndicatorData(
  records: Ref<ChartDatasetCell[][][]>,
  fieldsJson: Ref<ChartFieldDefinition[]>,
  predefinedCountries: Ref<string[]>,
  isIndicatorMode: Ref<boolean>,
) {


  // 透视表列定义
  const pivotTableColumns = computed(() => {
    if (!isIndicatorMode.value) {
      return []
    }

    const columns: any[] = [
      {
        title: '指标',
        dataIndex: '0',
        key: '0',
        width: 120,
        fixed: 'center',
      },
    ]

    // 使用预定义的国家列表
    predefinedCountries.value.forEach((country, index) => {
      columns.push({
        title: country,
        dataIndex: String(index + 1),
        key: String(index + 1),
        width: 120,
      })
    })

    return columns
  })

  const countryFieldIndex = computed(() => fieldsJson.value.findIndex(f => f.name === '国家' && !f.isIndicator))
  const indicatorFieldIndex = computed(() => fieldsJson.value.findIndex(f => f.isIndicator))
  const pivotValueIndex = computed(() => [1, 2, 0].find(n => n !== indicatorFieldIndex.value && n !== countryFieldIndex.value)!)

  // 透视表数据
  const pivotTableData = computed(() => {
    if (!isIndicatorMode.value) {
      return []
    }

    // 收集所有透视值
    const pivotValues = new Set<string>()
    const dataMap = new Map<string, Map<string, any>>()


    records.value.forEach((record, index) => {
      const country = getCellValue(record[countryFieldIndex.value]) // 国家
      const indicatorValue = record[indicatorFieldIndex.value] // 指标值（ChartDatasetCell数组）
      const pivotValue = getCellValue(record[pivotValueIndex.value]) // 透视值

      if (country && pivotValue) {
        pivotValues.add(pivotValue)

        if (!dataMap.has(pivotValue)) {
          dataMap.set(pivotValue, new Map())
        }
        dataMap.get(pivotValue)!.set(country, { value: indicatorValue, rowIndex: index })
      }
    })

    // 生成透视表数据
    const pivotData: any[] = []
    Array.from(pivotValues).forEach((pivotValue, index) => {
      const rowData: any = {
        index: `pivot_${index}`,
        pivotValue,
        0: pivotValue, // 第一列显示透视值
      }

      // 为所有预定义国家生成列数据
      predefinedCountries.value.forEach((country, countryIndex) => {
        const cellData = dataMap.get(pivotValue)?.get(country)
        rowData[String(countryIndex + 1)] = cellData ? getCellValue(cellData.value) : ''
        // 保存原始数据索引用于编辑
        if (cellData) {
          rowData[`_meta_${countryIndex + 1}`] = {
            rowIndex: cellData.rowIndex,
            fieldIndex: 1, // 指标值字段索引
          }
        }
      })

      pivotData.push(rowData)
    })
    return pivotData
  })

  // 指标详情模态框相关
  const showIndicatorDetailModal = ref(false)
  const indicatorDetailData = ref<IndicatorDetailData>({
    country: '',
    pivotValue: '',
    currentValue: '',
    currentVia: '',
    lastUpdated: '',
    updateType: '',
    url: '',
    remarks: '',
    history: []
  })

  // 获取单元格值的辅助函数
  function getCellValue(cellArray: ChartDatasetCell[] | undefined): string {
    if (!cellArray || cellArray.length === 0) return ''
    const sortedCells = [...cellArray].sort((a, b) =>
      dayjs(b.updatedAt).valueOf() - dayjs(a.updatedAt).valueOf()
    )
    const value = sortedCells[0]?.value ?? ''
    // 过滤掉空占位符
    return value === EMPTY_PLACEHOLDER ? '' : value
  }

  // 获取有效的历史记录（过滤掉空占位符）
  function getValidHistory(cellArray: ChartDatasetCell[]): ChartDatasetCell[] {
    return cellArray
      .filter(cell => cell.value !== EMPTY_PLACEHOLDER)
      .sort((a, b) => dayjs(b.updatedAt).valueOf() - dayjs(a.updatedAt).valueOf())
  }

  // 获取指标历史记录
  function getCellHistory(rowIndex: number, fieldIndex: number): ChartDatasetCell[] {
    const cellArray = records.value[rowIndex]?.[fieldIndex] || []
    return getValidHistory(cellArray)
  }

  // 获取当前指标值的via信息
  function getCurrentCellVia(rowIndex: number, fieldIndex: number): string {
    const history = getCellHistory(rowIndex, fieldIndex)
    return history[0]?.via || ''
  }

  // 获取透视表单元格的历史记录
  function getPivotCellHistory(record: any, column: any): ChartDatasetCell[] {
    const columnIndex = Number(column.dataIndex)
    if (columnIndex === 0) return []

    const countryName = predefinedCountries.value[columnIndex - 1]
    const pivotValue = record.pivotValue

    const targetRecord = records.value.find((row) => {
      const country = getCellValue(row[countryFieldIndex.value])
      const pivot = getCellValue(row[pivotValueIndex.value])
      return country === countryName && pivot === pivotValue
    })

    if (targetRecord && targetRecord[1]) {
      return targetRecord[1].sort((a, b) => dayjs(b.updatedAt).valueOf() - dayjs(a.updatedAt).valueOf())
    }

    return []
  }

  // 获取透视表单元格的via信息
  function getPivotCellVia(record: any, column: any): string {
    const history = getPivotCellHistory(record, column)
    return history[0]?.via || ''
  }

  // 显示普通模式指标详情
  function showIndicatorDetail(rowIndex: number, fieldIndex: number) {
    const record = records.value[rowIndex]
    if (!record || !record[fieldIndex]) return

    const history = getCellHistory(rowIndex, fieldIndex)
    if (history.length === 0) return

    const currentCell = history[0]
    if (!currentCell) return

    // 获取国家和透视值信息
    const country = getCellValue(record[0]) || '未知'
    const pivotValue = record.length > 2 ? getCellValue(record[2]) : '未知'

    // 填充详情数据
    indicatorDetailData.value = {
      country,
      pivotValue,
      currentValue: currentCell.value || '',
      currentVia: currentCell.via || '',
      lastUpdated: dateTime(currentCell.updatedAt),
      updateType: currentCell.updateType || '',
      url: currentCell.url || '',
      remarks: currentCell.remarks || '',
      history: history
    }

    showIndicatorDetailModal.value = true
  }

  // 显示特定记录的指标详情
  function showSpecificIndicatorDetail(rowIndex: number, fieldIndex: number, cell: ChartDatasetCell) {
    const record = records.value[rowIndex]
    if (!record || !cell) return

    // 获取国家和透视值信息
    const country = getCellValue(record[0]) || '未知'
    const pivotValue = record.length > 2 ? getCellValue(record[2]) : '未知'

    // 填充详情数据，以指定的cell为当前版本
    const history = getCellHistory(rowIndex, fieldIndex)

    indicatorDetailData.value = {
      country,
      pivotValue,
      currentValue: cell.value || '',
      currentVia: cell.via || '',
      lastUpdated: dateTime(cell.updatedAt),
      updateType: cell.updateType || '',
      url: cell.url || '',
      remarks: cell.remarks || '',
      history: history
    }

    showIndicatorDetailModal.value = true
  }

  // 显示透视表指标详情
  function showPivotIndicatorDetail(record: any, column: any) {
    const columnIndex = Number(column.dataIndex)
    if (columnIndex === 0) return

    const countryName = predefinedCountries.value[columnIndex - 1]
    if (!countryName) return

    const pivotValue = record.pivotValue
    const history = getPivotCellHistory(record, column)

    if (history.length === 0) return

    const currentCell = history[0]
    if (!currentCell) return

    // 填充详情数据
    indicatorDetailData.value = {
      country: countryName,
      pivotValue,
      currentValue: currentCell.value || '',
      currentVia: currentCell.via || '',
      lastUpdated: dateTime(currentCell.updatedAt),
      updateType: currentCell.updateType || '',
      url: currentCell.url || '',
      remarks: currentCell.remarks || '',
      history: history
    }

    showIndicatorDetailModal.value = true
  }

  // 显示透视表特定记录的指标详情
  function showSpecificPivotIndicatorDetail(record: any, column: any, cell: ChartDatasetCell) {
    console.log(record, column, cell)

    const columnIndex = Number(column.dataIndex)
    if (columnIndex === 0) return

    const countryName = predefinedCountries.value[columnIndex - 1]
    if (!countryName || !cell) return

    const pivotValue = record.pivotValue
    const history = getPivotCellHistory(record, column)

    // 填充详情数据，以指定的cell为当前版本
    indicatorDetailData.value = {
      country: countryName,
      pivotValue,
      currentValue: cell.value || '',
      currentVia: cell.via || '',
      lastUpdated: dateTime(cell.updatedAt),
      updateType: cell.updateType || '',
      url: cell.url || '',
      remarks: cell.remarks || '',
      history: history
    }

    showIndicatorDetailModal.value = true
  }

  // 根据via类型返回对应的颜色
  function getViaColor(via: string): string {
    switch (via) {
      case '官方数据': return 'green'
      case '非官方数据': return 'orange'
      case '后台计算': return 'blue'
      default: return 'default'
    }
  }

  // 获取默认更新内容
  function getDefaultUpdateContent(cell: ChartDatasetCell): string {
    if (cell.updateType) {
      return cell.updateType
    }
    if (cell.value !== undefined && cell.value !== null) {
      return `更新数值为 ${cell.value}`
    }
    return '数据更新'
  }

  return {
    // 状态
    showIndicatorDetailModal,
    indicatorDetailData,
    pivotTableData,
    pivotTableColumns,
    // 方法
    getCellValue,
    getCellHistory,
    getCurrentCellVia,
    getPivotCellHistory,
    getPivotCellVia,
    showIndicatorDetail,
    showSpecificIndicatorDetail,
    showPivotIndicatorDetail,
    showSpecificPivotIndicatorDetail,
    getViaColor,
    getDefaultUpdateContent,
    pivotValueIndex,
    countryFieldIndex,
    indicatorFieldIndex,
  }
}
