/*
 * @Author: Ztq
 * @Date: 2022-04-25 10:27:53
 * @LastEditors: luckymiaow
 * @LastEditTime: 2023-09-25 15:09:46
 * @Description:
 * @FilePath: \ch2-template-vue\src\utils\rules.ts
 */
import type { RuleObject, StoreValue } from 'ant-design-vue/lib/form/interface'
import * as regExp from './regExp'

const rulesNameMap: Record<(keyof typeof regExp) | string, string> = {
  isCardReg: '身份证号不正确',
  phoneReg: '手机号不正确',
  emailReg: '邮箱不正确',
  isLandlinePhoneReg: '座机号不正确',
  isPasswordReg: '该密码为弱密码请按要求修改密码 :至少两种组合(数字、字母、特殊字符), 长度为8-16位字符',
  idCardReg: '身份证号不正确',
  numberReg: '只能输入数字',
  guidReg: '不能为空',
}

export function FormValidator(
  validator: ((v: string | undefined | null) => boolean) | (keyof typeof regExp) | RegExp, // 来自于rules的验证方法
  message?: string,
): (rule: RuleObject, value: StoreValue) => void {
  let m: string | null = ''
  if (!message) {
    if (typeof validator === 'function')
      m = rulesNameMap[validator.name]
    else if (typeof validator === 'string')
      m = rulesNameMap[validator]
    message = m ? `${m}！` : '请输入正确内容！'
  }

  let fn: any

  if (typeof validator === 'string') {
    fn = (value: string | undefined | null): boolean => {
      if (value == null)
        return false
      return ((regExp as any)[validator as any]).test(value)
    }
  }

  if (typeof validator === 'object') {
    fn = (value: string | undefined | null): boolean => {
      if (value == null)
        return false
      return validator.test(value)
    }
  }

  return (rule: RuleObject, value: StoreValue) => {
    return new Promise((resolve, reject) => {
      if (!value) {
        if (rule.required)
          reject(m ? `${m}不能为空` : message)
        else
          resolve('success')
      }
      else if (fn(value) === false) {
        reject(message)
      }
      else {
        resolve('success')
      }
    })
  }
}
