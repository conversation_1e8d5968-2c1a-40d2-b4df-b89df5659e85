import type { CustomIconComponentProps } from '@ant-design/icons-vue/lib/components/Icon'

function icon(props: CustomIconComponentProps) {
  return (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 56.054 66.35" class="ckbox-doc-icon__icon">
      <g transform="translate(-412.5 -297.644)">
        <g transform="translate(368.745 265.397)">
          <path d="M82.585,33.746H53.6a8.342,8.342,0,0,0-8.342,8.342V88.754A8.342,8.342,0,0,0,53.6,97.1H89.966a8.342,8.342,0,0,0,8.342-8.342V49.469Z" fill="#fff" stroke="#891fa8" stroke-miterlimit="10" stroke-width="3"></path>
          <path d="M204.77,33.746v9.866a7.156,7.156,0,0,0,7.156,7.156h9.866Z" transform="translate(-123.189)" fill="#891fa8"></path>
        </g>
        <g transform="translate(435.02 313.808)">
          <g transform="translate(3.765)">
            <rect width="5.04" height="3.26" rx="1.63" transform="translate(3.26) rotate(90)" fill="#891fa8"></rect>
            <rect width="5.04" height="3.26" rx="1.63" transform="translate(3.26 6.155) rotate(90)" fill="#891fa8"></rect>
            <rect width="5.04" height="3.26" rx="1.63" transform="translate(3.26 12.31) rotate(90)" fill="#891fa8"></rect>
          </g>
          <path d="M0,9.67A6.031,6.031,0,0,1,2.219,4.906L3.406,0H6.984L8.238,5.015A6.057,6.057,0,0,1,10.316,9.67c0,3.207-2.307,5.8-5.156,5.8S0,12.877,0,9.67Z" transform="translate(0 18.548)" fill="#891fa8"></path>
          <ellipse cx="2.5" cy="2" rx="2.5" ry="2" transform="translate(2.658 26.285)" fill="#fff"></ellipse>
        </g>
      </g>
    </svg>
  )
}
export default {
  icon,
  color: '#F4E9F5',
}
