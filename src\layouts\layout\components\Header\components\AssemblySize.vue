<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: luckymiaow
-->
<!-- 配置字体大小 -->
<template>
  <div>
    <a-dropdown placement="bottom">
      <FontSizeOutlined />
      <template #overlay>
        <a-menu>
          <a-menu-item
            v-for="item in assemblySizeList"
            :key="item"
            :disabled="assemblySize === item"
            :command="item"
            @click="setAssemblySize(item)"
          >
            {{ assemblySizeListCh[item] }}
          </a-menu-item>
        </a-menu>
        {{ assemblySize }}
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
import type { AssemblySizeType } from '@/types/interfaces'
import { useAppStore } from '@/stores'
import { FontSizeOutlined } from '@ant-design/icons-vue'
import { computed, reactive } from 'vue'

const globalStore = useAppStore()
const assemblySize = computed(() => globalStore.assemblySize)

const assemblySizeList: AssemblySizeType[] = ['default', 'large', 'small']

const assemblySizeListCh = reactive<{ [key: string]: string }>({
  default: '默认',
  small: '小型',
  large: '大型',
})

function setAssemblySize(item: AssemblySizeType) {
  console.log(item)
  if (assemblySize.value === item)
    return
  globalStore.setAssemblySizeSize(item)
}
</script>

<style scoped></style>
