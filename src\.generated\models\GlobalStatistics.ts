import { Chart } from "./Chart";
export class GlobalStatistics {
  /**更新时间*/
  time: Dayjs = dayjs();
  /**数据总量*/
  total: number = 0;
  /**数据总量-近七天增长量（正数表示增长，负数表示减少）*/
  totalIncrement: number = 0;
  /**涉我信息*/
  sw: number = 0;
  /**涉我信息-近七天增长量（正数表示增长，负数表示减少）*/
  swIncrement: number = 0;
  /**今日更新*/
  todayUp: number = 0;
  /**较昨日增长百分比（小数点后四位）*/
  growthRate: number = 0;
  /**海外智库*/
  hwzk?: Chart | null | undefined = null;
  /**东盟新闻*/
  dmxw?: Chart | null | undefined = null;
  /**东盟AI发展*/
  ai?: Chart | null | undefined = null;
  /**其他*/
  qt?: Chart | null | undefined = null;
  /**涉我新闻分布*/
  swfb?: Chart[] | null | undefined = [];
  /**情感分析*/
  qgfx?: Chart[] | null | undefined = [];
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
