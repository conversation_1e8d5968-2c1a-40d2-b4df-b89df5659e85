import type { RemoveColorPrefix, ThemeColor, ThemeGlobalColor } from '@/types/interfaces'
import { theme } from 'ant-design-vue'
import dayjs from 'dayjs'
import { kebabCase } from 'lodash-es'

const { defaultAlgorithm, defaultSeed } = theme

const mapToken = defaultAlgorithm(defaultSeed)

export function getThemeAttrKey(key: string) {
  return `--ch2-${kebabCase(key)}`
}

export function wordColor(val: keyof ThemeColor) {
  const words = {
    colorPrimary: '主色',
    colorInfo: '提示色',
    colorSuccess: '成功色',
    processingColor: '处理色',
    colorError: '错误色',
    colorWarning: '警告色',
    colorBgBase: '背景色',
    colorTextBase: '文字色',
  }

  return words[val]
}

const THEME_KEY = `${dayjs().valueOf()}-dynamic-theme`

export function updateCss(options: ThemeGlobalColor) {
  const ignore: string[] = []

  const theme: string[] = []

  const keys = Object.keys(options) as unknown as Array<keyof ThemeGlobalColor>

  keys.forEach((key) => {
    if (ignore.includes(key))
      return
    theme.push(`${getThemeAttrKey(key)}:${options[key]}`)
  })

  const dom = document.querySelector('head') || document.body

  const styleNode = document.getElementById(THEME_KEY) || document.createElement('style')

  if (theme.length === 0)
    return styleNode.remove()

  styleNode.setAttribute('id', THEME_KEY)

  styleNode.innerHTML = `html,:root  {\n${theme.join(';\n')}\n}`

  dom.appendChild(styleNode)
}

export function getColors() {
  const data = Object.keys(mapToken).filter(key => key.startsWith('color')).reduce((a, key) => {
    (a as any)[kebabCase(key.replace(/^color/, ''))] = `var(${getThemeAttrKey(key)})`
    return a
  }, {} as RemoveColorPrefix<ThemeGlobalColor>)

  return data
}

export function getLessModifyVars() {
  const data = Object.keys(mapToken).reduce((a, key) => {
    (a as any)[key] = `var(${getThemeAttrKey(key)})`
    return a
  }, {} as ThemeGlobalColor)
  return data
}
