<template>
  <div class="p-6">
    <div class="mb-4 flex items-center justify-between">
      <h2 class="text-xl font-bold">全球事件数据库</h2>
      <a-button type="primary" @click="showDemandModal = true">添加数据采集需求</a-button>
    </div>

    <!-- 关键词组和时间范围 -->
    <div class="mb-4">
      <div v-for="(group, gIdx) in keywordGroups" :key="gIdx" class="mb-2 flex items-center">
        <a-input
          v-model:value="keywordGroups[gIdx]"
          placeholder="请输入关键词组（多个关键词用逗号分隔）"
          style="width: 320px"
        />
        <a-button v-if="keywordGroups.length > 1" type="link" @click="removeGroup(gIdx)">移除组</a-button>
      </div>
      <a-button type="primary" @click="search">查询</a-button>
    </div>

    <!-- 查询结果 -->
    <a-table
      :columns="columns"
      :data-source="results"
      row-key="id"
      :loading="loading"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'url'">
          <a :href="record.url" target="_blank">{{ record.url }}</a>
        </template>
        <template v-else>
          {{ record[column.dataIndex] }}
        </template>
      </template>
    </a-table>

    <!-- 添加数据采集需求弹窗 -->
    <a-modal v-model:visible="showDemandModal" title="添加数据采集需求" @ok="handleAddDemand">
      <a-form layout="vertical">
        <a-form-item label="需求描述">
          <a-textarea v-model:value="demandDesc" placeholder="请填写采集需求" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { ref } from 'vue'

const keywordGroups = ref([''])
const results = ref<any[]>([
  {
    id: '20250419120724330',
    title: 'Von Platz eins auf fünf gefallen Apple verliert massiv Marktanteile in China',
    url: 'https://www.n-tv.de/wirtschaft/Apple-verliert-massiv-Marktanteile-in-China-article25713925.html',
  },
  {
    id: '20250419120714860',
    title: 'Trumps Dekret entfacht Antimon-Boom – Wird Global Tactical Amerikas Antwort auf Chinas',
    url: 'https://www.wallstreet-online.de/nachricht/19249764-trumps-dekret-entfacht-antimon-boom-global-tactical-amerikas-antwort-chinas-rohstoffdominanz',
  },
  {
    id: '20250419120708829',
    title: 'Experte zu Handelskrieg mit China Was Trump trotz "überschaubarem Intellekt" verstanden hat',
    url: 'https://www.n-tv.de/mediathek/videos/wirtschaft/Was-Trump-trotz-ueberschaubarem-Intellekt-verstanden-hat-article25712864.html',
  },
  {
    id: '20250419120676562',
    title: 'Trump se abre a negociar con China un acuerdo con los aranceles',
    url: 'https://www.marca.com/tiramillas/television/2025/04/18/montoya-manuel-vuelven-discutir-conocerse-nominados-supervivientes-playa-tienes-cara-aqui.html',
  },
  {
    id: '20250419120663184',
    title: 'Warum China den Wettlauf fördert Bei diesem Halbmarathon verlieren Roboter gegen Menschen',
    url: 'https://www.n-tv.de/wissen/Bei-diesem-Halbmarathon-verlieren-Roboter-gegen-Menschen-article25714629.html',
  },
  {
    id: '20250419120659991',
    title: 'Exporthürden für seltene Erden Chinas Superwaffe im Handelskrieg zielt auf die ganze Welt',
    url: 'https://www.n-tv.de/wirtschaft/Chinas-Superwaffe-im-Handelskrieg-zielt-auf-die-ganze-Welt-article25705865.html',
  },
  {
    id: '20250419120643824',
    title: 'La web de la Casa Blanca culpa a China de supuesta "fuga del laboratorio" del covid-19',
    url: 'https://www.elmundo.es/ciencia-y-salud/ciencia/2025/04/19/68036e2efdddffa0278b4589.html',
  },
  {
    id: '20250419120630065',
    title: 'China interrompe importações de gás liquefeito dos EUA',
    url: 'http://www.jornaldenegocios.pt/economia/mundo/detalhe/china-interrompe-importacoes-de-gas-liquefeito-dos-eua',
  },
  {
    id: '20250419120618644',
    title: 'China: Roboter laufen Halbmarathon gegen Menschen',
    url: 'https://wz-net.de/weltweit/panorama/china-roboter-laufen-halbmarathon-gegen-menschen-id253591.html',
  },
  {
    id: '20250419120618045',
    title: 'Rota marítima direta entre Brasil e China reduzirá transporte em 30 dias',
    url: 'https://www.agoramt.com.br/2025/04/rota-maritima-direta-entre-brasil-e-china-reduzira-transporte-em-30-dias/',
  },
  {
    id: '20250419120613382',
    title: 'Boeing recusados na China iniciam voos de regresso aos EUA',
    url: 'https://eco.sapo.pt/2025/04/19/tarifas-boeing-recusados-por-companhias-aereas-chinesas-iniciam-voos-de-regresso-aos-eua/',
  },
  {
    id: '20250419120601184',
    title: 'Noch AufholbedarfRoboter versus Mensch bei Halbmarathon in China',
    url: 'https://www.krone.at/3761223',
  },
  {
    id: '20250419120589910',
    title: 'Designed in US, made in China: Why Apple is stuck',
    url: 'https://www.bbc.com/news/articles/czx17361pw1o?xtor=AL-72-%5Bpartner%5D-%5Binforadio%5D-%5Bheadline%5D-%5Bnews%5D-%5Bbizdev%5D-%5Bisapi%5D',
  },
  {
    id: '20250419120579258',
    title: 'Carney says China is a foreign interference, geopolitical threat for Canada',
    url: 'https://ottawa.ctvnews.ca/politics/article/carney-says-china-is-a-foreign-interference-geopolitical-threat-for-canada/',
  },
  {
    id: '20250419120575946',
    title: 'BYD Daftarkan SUV Sealion 06 di China, Ada Versi PHEV dan BEV',
    url: 'https://www.liputan6.com/otomotif/read/5998769/byd-daftarkan-suv-sealion-06-di-china-ada-versi-phev-dan-bev',
  },
  {
    id: '20250419120569682',
    title: 'China lleva la ventaja',
    url: 'https://diario.mx/opinion/2025/apr/18/china-lleva-la-ventaja-1062494.html',
  },
  {
    id: '20250419120556352',
    title: 'A China testa o apetite europeu e a Europa tem fome',
    url: 'http://www.jornaldenegocios.pt/opiniao/detalhe/a-china-testa-o-apetite-europeu-e-a-europa-tem-fome',
  },
  {
    id: '20250419120530710',
    title: 'Dozens of humanoid robots joining a half marathon in Beijing showcasing China\'s drive to lead the',
    url: 'https://www.news-shield.com/news/national/image_badd5a92-a17a-5933-a749-ef9b5592ce5b.html',
  },
  {
    id: '20250419120517169',
    title: 'CEO da Nvidia garante a Pequim que vai "aprofundar presença" na China',
    url: 'http://www.jornaldenegocios.pt/empresas/detalhe/ceo-da-nvidia-garante-a-pequim-que-vai-aprofundar-presenca-na-china',
  },
  {
    id: '20250419120503754',
    title: 'La Casa Blanca culpa a China por supuesta fuga de laboratorio que dio origen al Covid-19',
    url: 'http://www.proceso.com.mx/internacional/2025/4/18/la-casa-blanca-culpa-china-por-supuesta-fuga-de-laboratorio-que-dio-origen-al-covid-19-349653.html',
  },
  {
    id: '20250419120724330',
    title: 'Von Platz eins auf fünf gefallen Apple verliert massiv Marktanteile in China',
    url: 'https://www.n-tv.de/wirtschaft/Apple-verliert-massiv-Marktanteile-in-China-article25713925.html',
    content: '<p>Apple hat im ersten Quartal 2025 in China deutliche Marktanteile eingebüßt. Während der Marktanteil bei 20 Prozent lag, sank dieser auf 15 Prozent. Grund seien starke lokale Konkurrenten wie Xiaomi oder Huawei.</p>',
  },
  {
    id: '20250419120714860',
    title: 'Trumps Dekret entfacht Antimon-Boom – Wird Global Tactical Amerikas Antwort auf Chinas',
    url: 'https://www.wallstreet-online.de/nachricht/19249764-trumps-dekret-entfacht-antimon-boom-global-tactical-amerikas-antwort-chinas-rohstoffdominanz',
    content: '<p>Ein Erlass von Donald Trump hat zu einem sprunghaften Anstieg des Interesses an Antimon geführt. Das Unternehmen Global Tactical steht dabei im Fokus der Investoren. China ist bislang führend in der Antimon-Förderung.</p>',
  },
  {
    id: '20250419120708829',
    title: 'Experte zu Handelskrieg mit China Was Trump trotz "überschaubarem Intellekt" verstanden hat',
    url: 'https://www.n-tv.de/mediathek/videos/wirtschaft/Was-Trump-trotz-ueberschaubarem-Intellekt-verstanden-hat-article25712864.html',
    content: '<p>Ein Wirtschaftsexperte analysiert Trumps Verständnis für die ökonomischen Folgen des Handelskriegs mit China. Trotz seiner Limitierungen habe Trump zentrale Aspekte richtig eingeschätzt.</p>',
  },
  {
    id: '20250419120676562',
    title: 'Trump se abre a negociar con China un acuerdo con los aranceles',
    url: 'https://www.marca.com/tiramillas/television/2025/04/18/montoya-manuel-vuelven-discutir-conocerse-nominados-supervivientes-playa-tienes-cara-aqui.html',
    content: '<p>Donald Trump estaría dispuesto a revisar los aranceles impuestos a China si se logra un acuerdo beneficioso para ambas partes, según fuentes cercanas al expresidente.</p>',
  },
  {
    id: '20250419120663184',
    title: 'Warum China den Wettlauf fördert Bei diesem Halbmarathon verlieren Roboter gegen Menschen',
    url: 'https://www.n-tv.de/wissen/Bei-diesem-Halbmarathon-verlieren-Roboter-gegen-Menschen-article25714629.html',
    content: '<p>In China traten humanoide Roboter in einem Halbmarathon gegen Menschen an. Die Roboter zeigten zwar Potenzial, konnten aber das Tempo der menschlichen Läufer nicht mithalten.</p>',
  },
  {
    id: '20250419120654431',
    title: 'Biden verbietet neue Datenübertragungen in einige Länder, darunter auch China',
    url: 'https://www.heise.de/news/Biden-verbietet-neue-Datenuebertragungen-in-einige-Laender-darunter-auch-China-9713827.html',
    content: '<p>US-Präsident Joe Biden hat neue Regelungen erlassen, die bestimmte Datenübertragungen in Länder wie China untersagen. Die Maßnahme soll sensible Daten amerikanischer Bürger schützen.</p>',
  },
  {
    id: '20250419120642057',
    title: 'Biontech-Börsenbeben zeigt: China-Risiken werden neu bewertet',
    url: 'https://www.n-tv.de/wirtschaft/Biontech-Boersenbeben-zeigt-China-Risiken-werden-neu-bewertet-article25712893.html',
    content: '<p>Der Einbruch der Biontech-Aktie verdeutlicht, wie Anleger die Risiken chinesischer Marktabhängigkeit neu bewerten. Das betrifft nicht nur Pharma, sondern viele andere Sektoren.</p>',
  },
  {
    id: '20250419120628128',
    title: 'Chinas Wirtschaft wuchs im ersten Quartal überraschend stark',
    url: 'https://www.faz.net/aktuell/wirtschaft/chinas-wirtschaft-waechst-im-ersten-quartal-2024-ueberraschend-stark-19773833.html',
    content: '<p>Die chinesische Wirtschaft ist im ersten Quartal 2025 um 5,3 Prozent gewachsen und hat damit die Erwartungen übertroffen. Starker Export und Inlandsnachfrage trugen zum Wachstum bei.</p>',
  },
  {
    id: '20250419120620852',
    title: 'Taiwans Präsidentin: Wenn Taiwan sicher ist, ist die Welt sicher',
    url: 'https://www.dw.com/de/taiwans-pr%C3%A4sidentin-wenn-taiwan-sicher-ist-ist-die-welt-sicher/a-68708861',
    content: '<p>Tsai Ing-wen betont die Bedeutung Taiwans für die globale Stabilität. Angesichts der Spannungen mit China appelliert sie an die internationale Gemeinschaft zur Unterstützung.</p>',
  },
  {
    id: '20250419120615547',
    title: 'China will sich gegen neue US-Zölle zur Wehr setzen',
    url: 'https://www.tagesschau.de/wirtschaft/weltwirtschaft/china-us-strafzoelle-100.html',
    content: '<p>China kündigte an, auf neue US-Strafzölle reagieren zu wollen. Die Maßnahmen seien protektionistisch und verstießen gegen WTO-Regeln, so das chinesische Handelsministerium.</p>',
  },
  {
    id: '20250419120558831',
    title: 'Streit mit China: Japan verbietet Chip-Export',
    url: 'https://www.tagesschau.de/wirtschaft/technologie/japan-chip-exportverbot-100.html',
    content: '<p>Japan hat ein Exportverbot für bestimmte Halbleitertechnologien nach China verhängt. Die Entscheidung folgt auf Druck aus den USA und betrifft zahlreiche japanische Hersteller.</p>',
  },
  {
    id: '20250419120553266',
    title: 'USA verschärfen Regeln für chinesische Investoren',
    url: 'https://www.zeit.de/wirtschaft/2025-04/usa-china-investoren-regeln',
    content: '<p>Die US-Regierung hat neue Vorschriften erlassen, um Investitionen chinesischer Unternehmen in sensible Industrien zu beschränken. Ziel sei es, nationale Sicherheitsinteressen zu schützen.</p>',
  },
  {
    id: '20250419120545617',
    title: 'Chinas Autoexporte überholen erstmals Deutschland',
    url: 'https://www.n-tv.de/wirtschaft/Chinas-Autoexporte-ueberholen-erstmals-Deutschland-article25712855.html',
    content: '<p>China hat Deutschland beim Autoexport überholt und ist nun weltweit führend. Besonders Elektroautos trugen zum Anstieg der Ausfuhren bei.</p>',
  },
  {
    id: '20250419120540295',
    title: 'Handelskrieg spitzt sich zu: USA erhöhen Druck auf chinesische Firmen',
    url: 'https://www.tagesschau.de/wirtschaft/unternehmen/usa-china-handelspolitik-100.html',
    content: '<p>Die US-Regierung verschärft den Druck auf chinesische Unternehmen durch neue Sanktionen und Handelsbarrieren. Ziel ist es, unfaire Wettbewerbspraktiken einzudämmen.</p>',
  },
  {
    id: '20250419120530762',
    title: 'China warnt USA vor "riskanter Eskalation" im Handelskonflikt',
    url: 'https://www.dw.com/de/china-warnt-usa-vor-riskanter-eskalation-im-handelskonflikt/a-68708857',
    content: '<p>China hat die USA vor einer weiteren Eskalation im Handelskonflikt gewarnt. Peking fordert eine Rückkehr zum Dialog und warnt vor den globalen Folgen der Spannungen.</p>',
  },
  {
    id: '20250419120525237',
    title: 'Deutschland sucht Alternativen zu China als Handelspartner',
    url: 'https://www.handelsblatt.com/politik/deutschland-sucht-alternativen-zu-china-als-handelspartner/1000000000000.html',
    content: '<p>Die Bundesregierung prüft verstärkt den Ausbau wirtschaftlicher Beziehungen zu Indien, Südostasien und Afrika, um die Abhängigkeit von China zu verringern.</p>',
  },
  {
    id: '20250419120519851',
    title: 'Chinas Investitionen in Europa sinken auf Rekordtief',
    url: 'https://www.faz.net/aktuell/wirtschaft/chinas-investitionen-in-europa-sinken-auf-rekordtief-19774021.html',
    content: '<p>Die chinesischen Direktinvestitionen in Europa haben ein neues Tief erreicht. Gründe sind geopolitische Spannungen, regulatorische Hürden und ein schwaches Investitionsklima.</p>',
  },
  {
    id: '20250419120513557',
    title: 'EU untersucht Subventionen chinesischer Windkraft-Hersteller',
    url: 'https://www.n-tv.de/wirtschaft/EU-untersucht-Subventionen-chinesischer-Windkraft-Hersteller-article25713911.html',
    content: '<p>Die EU-Kommission hat ein Verfahren gegen chinesische Windkraftunternehmen eingeleitet. Untersucht werden mögliche Marktverzerrungen durch staatliche Subventionen.</p>',
  },
  {
    id: '20250419120507986',
    title: 'Chinas Chip-Industrie boomt trotz US-Sanktionen',
    url: 'https://www.tagesspiegel.de/wirtschaft/chinas-chip-industrie-boomt-trotz-us-sanktionen-10722755.html',
    content: '<p>Chinas Halbleiterbranche wächst trotz internationaler Sanktionen weiter. Unterstützt durch staatliche Förderprogramme verzeichnet die Branche zweistellige Wachstumsraten.</p>',
  },
  {
    id: '20250419120501164',
    title: 'Alibaba trennt sich von Cloud-Sparte',
    url: 'https://www.sueddeutsche.de/wirtschaft/alibaba-cloud-abspaltung-china-1.5678571',
    content: '<p>Der chinesische Tech-Konzern Alibaba will seine Cloud-Sparte ausgliedern. Ziel ist es, sich stärker auf das Kerngeschäft zu konzentrieren und regulatorische Risiken zu minimieren.</p>',
  },
],
)
const loading = ref(false)
const showDemandModal = ref(false)
const demandDesc = ref('')

// 表格列定义
const columns = [
  { title: 'ID', dataIndex: 'id', key: 'id' },
  { title: '标题', dataIndex: 'title', key: 'title' },
  { title: '网址', dataIndex: 'url', key: 'url' },
]

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: (total: number) => `共 ${total} 条`,
  onChange: (page: number, pageSize: number) => {
    pagination.value.current = page
    pagination.value.pageSize = pageSize
    search()
  },
})

// 移除关键词组
function removeGroup(idx: number) {
  keywordGroups.value.splice(idx, 1)
}

// 查询
async function search() {

}

// 添加数据采集需求
function handleAddDemand() {
  if (!demandDesc.value.trim()) {
    message.warning('请填写采集需求')
    return
  }
  // 实际应提交到后端
  message.success('采集需求已提交')
  showDemandModal.value = false
  demandDesc.value = ''
}
</script>
