<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: 龙兆柒
-->
<!--  -->
<template>
  <div class="layouts-id">
    <Vertical v-if="isVertical" />
    <Transverse v-if="isTransverse" />
    <a-back-top :visibility-height="20" />
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores'
import { computed } from 'vue'
import Transverse from './Transverse/index.vue'
import Vertical from './Vertical/index.vue'

const globalStore = useAppStore()

const isVertical = computed(() => globalStore.themeConfig.layout === 'vertical')

const isTransverse = computed(() => globalStore.themeConfig.layout === 'transverse')
</script>

<style lang="less">
.layouts-id {
  .table-search {
    background-color: @colorBgContainer;
    border-radius: calc(@borderRadiusLG * 1px);
  }

  .pro-table {
    .table-search {
      background-color: @colorBgContainer;
      border-radius: calc(@borderRadiusLG * 1px);
    }

    .table-main {
      background-color: @colorBgContainer;
      position: relative;
      text-align: center;
      border: none;
      border-radius: calc(@borderRadiusLG * 1px);
      margin-top: calc(@size * 1px);

      .ant-pagination .ant-table-pagination .ant-table-pagination-right {
        position: absolute;
        bottom: 0;
      }
    }
  }
}
</style>
