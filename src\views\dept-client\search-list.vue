<template>
  <div class="mb-4"><a-input-search v-model:value="keyword" size="large" placeholder="搜索智库文章、新闻、政策等内容..." enter-button @search="onSearch" /></div>
  <a-spin :spinning="spinning">
    <div v-if="articles.length > 0">
      <div>
        <ListItem
          v-for="(article, index) in articles"
          :key="index"
          :article="article"
          @on-collect-success="getData"
        />
      </div>
      <div class="flex justify-end">
        <a-pagination
          v-model:current="pageData.current"
          v-model:page-size="pageData.pageSize"
          show-size-changer
          :show-total="(total) => `总 ${total} 条`"
          :total="pageData.total"
          @change="pageChange"
        />
      </div>
    </div>
    <a-empty v-else />
  </a-spin>
</template>

<script lang='ts' setup>
import type { DataManageModelPageView } from '@/api/models'
import * as api from '@/api'
import { message } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import ListItem from './components/listItem.vue'

const spinning = ref(false)

const keyword = ref('')

const articles = ref<DataManageModelPageView[]>([])

const pageData = ref({
  current: 1,
  pageSize: 20,
  total: 0,
})

async function getData() {
  spinning.value = true
  const offset = (pageData.value.current - 1) * pageData.value.pageSize
  try {
    const res = await api.DataManageModels.Query_PostAsync(
      { offset, limit: pageData.value.pageSize },
      { keyword: keyword.value },
    )
    articles.value = res.items || []
    pageData.value.total = res.totals || 0
    spinning.value = false
  }
  catch (error: any) {
    message.error(`获取数据失败${error.message}`)
    spinning.value = false
  }
}

function onSearch() {
  pageData.value.current = 1
  getData()
}

function pageChange(page: number, pageSize: number) {
  pageData.value.current = page
  pageData.value.pageSize = pageSize
  getData()
}

const route = useRoute()

onMounted(() => {
  if (route.query?.keyword) {
    keyword.value = route.query.keyword as string
  }
  getData()
})
</script>

<style scoped>

</style>
