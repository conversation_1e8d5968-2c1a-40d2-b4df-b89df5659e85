import type { UploadFileInfo } from '@/api/models'
/* eslint-disable regexp/no-empty-capturing-group */
import type { Editor, EditorConfig, FileLoader, UploadAdapter, UploadResponse, Writer } from 'ckeditor5'
import { FileType } from '@/api/models'
import { uploadFile } from '@/components/FileManager/uilt'
import axios from 'axios'
import {
  Alignment,
  Autoformat,
  AutoImage,
  AutoLink,
  Base64UploadAdapter,
  BlockQuote,
  Bold,
  ButtonView,
  CloudServices,
  Code,
  CodeBlock,
  Essentials,
  FileRepository,
  FindAndReplace,
  Font,
  GeneralHtmlSupport,
  Heading,
  Highlight,
  HorizontalLine,
  HtmlEmbed,
  Image,
  ImageCaption,
  ImageInsert,
  ImageResize,
  ImageStyle,
  ImageToolbar,
  ImageUpload,
  Indent,
  IndentBlock,
  Italic,
  Link,
  LinkImage,
  List,
  ListProperties,
  MediaEmbed,
  Mention,
  MultiRootEditor,
  PageBreak,
  Paragraph,
  PasteFromOffice,
  PictureEditing,
  Plugin,
  RemoveFormat,
  SpecialCharacters,
  SpecialCharactersEssentials,
  Strikethrough,
  Style,
  Subscript,
  Superscript,
  Table,
  TableCaption,
  TableCellProperties,
  TableColumnResize,
  TableProperties,
  TableToolbar,
  TextPartLanguage,
  TextTransformation,
  TodoList,
  Underline,
  WordCount,
} from 'ckeditor5'
import coreTranslations from 'ckeditor5/translations/zh-cn.js'
import { merge } from 'lodash-es'
import { onUnmounted, ref, shallowRef } from 'vue'
import { useFileMangerModal } from './useFileMangerModal'
import 'ckeditor5/ckeditor5.css'

const REDUCED_MATERIAL_COLORS = [
  { label: 'Red 50', color: '#ffebee' },
  { label: 'Purple 50', color: '#f3e5f5' },
  { label: 'Indigo 50', color: '#e8eaf6' },
  { label: 'Blue 50', color: '#e3f2fd' },
  { label: 'Cyan 50', color: '#e0f7fa' },
  { label: 'Teal 50', color: '#e0f2f1' },
  { label: 'Light green 50', color: '#f1f8e9' },
  { label: 'Lime 50', color: '#f9fbe7' },
  { label: 'Amber 50', color: '#fff8e1' },
  { label: 'Orange 50', color: '#fff3e0' },
  { label: 'Grey 50', color: '#fafafa' },
  { label: 'Blue grey 50', color: '#eceff1' },
  { label: 'Red 100', color: '#ffcdd2' },
  { label: 'Purple 100', color: '#e1bee7' },
  { label: 'Indigo 100', color: '#c5cae9' },
  { label: 'Blue 100', color: '#bbdefb' },
  { label: 'Cyan 100', color: '#b2ebf2' },
  { label: 'Teal 100', color: '#b2dfdb' },
  { label: 'Light green 100', color: '#dcedc8' },
  { label: 'Lime 100', color: '#f0f4c3' },
  { label: 'Amber 100', color: '#ffecb3' },
  { label: 'Orange 100', color: '#ffe0b2' },
  { label: 'Grey 100', color: '#f5f5f5' },
  { label: 'Blue grey 100', color: '#cfd8dc' },
  { label: 'Red 200', color: '#ef9a9a' },
  { label: 'Purple 200', color: '#ce93d8' },
  { label: 'Indigo 200', color: '#9fa8da' },
  { label: 'Blue 200', color: '#90caf9' },
  { label: 'Cyan 200', color: '#80deea' },
  { label: 'Teal 200', color: '#80cbc4' },
  { label: 'Light green 200', color: '#c5e1a5' },
  { label: 'Lime 200', color: '#e6ee9c' },
  { label: 'Amber 200', color: '#ffe082' },
  { label: 'Orange 200', color: '#ffcc80' },
  { label: 'Grey 200', color: '#eeeeee' },
  { label: 'Blue grey 200', color: '#b0bec5' },
  { label: 'Red 300', color: '#e57373' },
  { label: 'Purple 300', color: '#ba68c8' },
  { label: 'Indigo 300', color: '#7986cb' },
  { label: 'Blue 300', color: '#64b5f6' },
  { label: 'Cyan 300', color: '#4dd0e1' },
  { label: 'Teal 300', color: '#4db6ac' },
  { label: 'Light green 300', color: '#aed581' },
  { label: 'Lime 300', color: '#dce775' },
  { label: 'Amber 300', color: '#ffd54f' },
  { label: 'Orange 300', color: '#ffb74d' },
  { label: 'Grey 300', color: '#e0e0e0' },
  { label: 'Blue grey 300', color: '#90a4ae' },
  { label: 'Red 400', color: '#ef5350' },
  { label: 'Purple 400', color: '#ab47bc' },
  { label: 'Indigo 400', color: '#5c6bc0' },
  { label: 'Blue 400', color: '#42a5f5' },
  { label: 'Cyan 400', color: '#26c6da' },
  { label: 'Teal 400', color: '#26a69a' },
  { label: 'Light green 400', color: '#9ccc65' },
  { label: 'Lime 400', color: '#d4e157' },
  { label: 'Amber 400', color: '#ffca28' },
  { label: 'Orange 400', color: '#ffa726' },
  { label: 'Grey 400', color: '#bdbdbd' },
  { label: 'Blue grey 400', color: '#78909c' },
  { label: 'Red 500', color: '#f44336' },
  { label: 'Purple 500', color: '#9c27b0' },
  { label: 'Indigo 500', color: '#3f51b5' },
  { label: 'Blue 500', color: '#2196f3' },
  { label: 'Cyan 500', color: '#00bcd4' },
  { label: 'Teal 500', color: '#009688' },
  { label: 'Light green 500', color: '#8bc34a' },
  { label: 'Lime 500', color: '#cddc39' },
  { label: 'Amber 500', color: '#ffc107' },
  { label: 'Orange 500', color: '#ff9800' },
  { label: 'Grey 500', color: '#9e9e9e' },
  { label: 'Blue grey 500', color: '#607d8b' },
  { label: 'Red 600', color: '#e53935' },
  { label: 'Purple 600', color: '#8e24aa' },
  { label: 'Indigo 600', color: '#3949ab' },
  { label: 'Blue 600', color: '#1e88e5' },
  { label: 'Cyan 600', color: '#00acc1' },
  { label: 'Teal 600', color: '#00897b' },
  { label: 'Light green 600', color: '#7cb342' },
  { label: 'Lime 600', color: '#c0ca33' },
  { label: 'Amber 600', color: '#ffb300' },
  { label: 'Orange 600', color: '#fb8c00' },
  { label: 'Grey 600', color: '#757575' },
  { label: 'Blue grey 600', color: '#546e7a' },
  { label: 'Red 700', color: '#d32f2f' },
  { label: 'Purple 700', color: '#7b1fa2' },
  { label: 'Indigo 700', color: '#303f9f' },
  { label: 'Blue 700', color: '#1976d2' },
  { label: 'Cyan 700', color: '#0097a7' },
  { label: 'Teal 700', color: '#00796b' },
  { label: 'Light green 700', color: '#689f38' },
  { label: 'Lime 700', color: '#afb42b' },
  { label: 'Amber 700', color: '#ffa000' },
  { label: 'Orange 700', color: '#f57c00' },
  { label: 'Grey 700', color: '#616161' },
  { label: 'Blue grey 700', color: '#455a64' },
  { label: 'Red 800', color: '#c62828' },
  { label: 'Purple 800', color: '#6a1b9a' },
  { label: 'Indigo 800', color: '#283593' },
  { label: 'Blue 800', color: '#1565c0' },
  { label: 'Cyan 800', color: '#00838f' },
  { label: 'Teal 800', color: '#00695c' },
  { label: 'Light green 800', color: '#558b2f' },
  { label: 'Lime 800', color: '#9e9d24' },
  { label: 'Amber 800', color: '#ff8f00' },
  { label: 'Orange 800', color: '#ef6c00' },
  { label: 'Grey 800', color: '#424242' },
  { label: 'Blue grey 800', color: '#37474f' },
  { label: 'Red 900', color: '#b71c1c' },
  { label: 'Purple 900', color: '#4a148c' },
  { label: 'Indigo 900', color: '#1a237e' },
  { label: 'Blue 900', color: '#0d47a1' },
  { label: 'Cyan 900', color: '#006064' },
  { label: 'Teal 900', color: '#004d40' },
  { label: 'Light green 900', color: '#33691e' },
  { label: 'Lime 900', color: '#827717' },
  { label: 'Amber 900', color: '#ff6f00' },
  { label: 'Orange 900', color: '#e65100' },
  { label: 'Grey 900', color: '#212121' },
  { label: 'Blue grey 900', color: '#263238' },
]

export function useEditor(config: EditorConfig = {}) {
  // const headerRef = ref<HTMLElement>();

  const contentRef = ref<HTMLElement>()

  const toolbar = ref<HTMLElement>()

  const editor = shallowRef<MultiRootEditor>()

  const _defaultConfig: EditorConfig = {
    plugins: [
      FileRepository,
      Alignment,
      HtmlEmbed,
      Autoformat,
      AutoImage,
      AutoLink,
      BlockQuote,
      Bold,
      CloudServices,
      Code,
      CodeBlock,
      Essentials,
      FindAndReplace,
      Font,
      GeneralHtmlSupport,
      Heading,
      Highlight,
      HorizontalLine,
      Image,
      ImageCaption,
      ImageInsert,
      ImageResize,
      ImageStyle,
      ImageToolbar,
      ImageUpload,
      Base64UploadAdapter,
      Indent,
      IndentBlock,
      Italic,
      Link,
      LinkImage,
      List,
      ListProperties,
      MediaEmbed,
      Mention,
      PageBreak,
      Paragraph,
      PasteFromOffice,
      PictureEditing,
      RemoveFormat,
      SpecialCharacters,
      SpecialCharactersEssentials,
      Strikethrough,
      Style,
      Subscript,
      Superscript,
      Table,
      TableCaption,
      TableCellProperties,
      TableColumnResize,
      TableProperties,
      TableToolbar,
      TextPartLanguage,
      TextTransformation,
      TodoList,
      Underline,
      WordCount,
      MyAssetManagerPlugin,
    ],
    htmlEmbed: { showPreviews: true },
    toolbar: {
      shouldNotGroupWhenFull: true,
      items: [
        // --- Document-wide tools ----------------------------------------------------------------------
        'undo',
        'redo',

        '|',
        'findAndReplace',
        'selectAll',
        '|',

        // --- "Insertables" ----------------------------------------------------------------------------

        'link',
        'insertImage',
        'assetManager',
        'insertTable',
        'blockQuote',
        'codeBlock',
        'pageBreak',
        'horizontalLine',
        'specialCharacters',
        'htmlEmbed',

        // --- Block-level formatting -------------------------------------------------------------------
        'heading',
        'style',
        '|',

        // --- Basic styles, font and inline formatting -------------------------------------------------------
        'bold',
        'italic',
        'underline',
        'strikethrough',
        {
          label: 'Basic styles',
          icon: 'text',
          items: [
            'fontSize',
            'fontFamily',
            'fontColor',
            'fontBackgroundColor',
            'highlight',
            'superscript',
            'subscript',
            'code',
            '|',
            'textPartLanguage',
            '|',
          ],
        },
        'removeFormat',
        '|',

        // --- Text alignment ---------------------------------------------------------------------------
        'alignment',
        '|',

        // --- Lists and indentation --------------------------------------------------------------------
        'bulletedList',
        'numberedList',
        'todoList',
        '|',
        'outdent',
        'indent',
      ],
    },
    translations: [
      coreTranslations,
    ],
    fontFamily: {
      supportAllValues: true,
    },
    fontSize: {
      options: [10, 12, 14, 'default', 18, 20, 22],
      supportAllValues: true,
    },
    fontColor: {
      columns: 12,
      colors: REDUCED_MATERIAL_COLORS,
    },
    fontBackgroundColor: {
      columns: 12,
      colors: REDUCED_MATERIAL_COLORS,
    },
    heading: {
      options: [
        { model: 'paragraph', title: '段落', class: 'ck-heading_paragraph' },
        {
          model: 'heading1',
          view: 'h1',
          title: '标题 1',
          class: 'ck-heading_heading1',
        },
        {
          model: 'heading2',
          view: 'h2',
          title: '标题 2',
          class: 'ck-heading_heading2',
        },
        {
          model: 'heading3',
          view: 'h3',
          title: '标题 3',
          class: 'ck-heading_heading3',
        },
        {
          model: 'heading4',
          view: 'h4',
          title: '标题 4',
          class: 'ck-heading_heading4',
        },
        {
          model: 'heading5',
          view: 'h5',
          title: '标题 5',
          class: 'ck-heading_heading5',
        },
        {
          model: 'heading6',
          view: 'h6',
          title: '标题 6',
          class: 'ck-heading_heading6',
        },
      ],
    },
    htmlSupport: {
      allow: [
        // Enables all HTML features.
        {
          name: /.*/,
          attributes: true,
          classes: true,
          styles: true,
        },
      ],
      disallow: [
        {
          attributes: [
            { key: /^on(.*)/i, value: true },
            {
              key: /.*/,
              value: /(\b)(on\S+)(\s*)=|javascript:|(<\s*)(\/*)script/i,
            },
            { key: /.*/, value: /data:(?!image\/(png|jpeg|gif|webp))/i },
          ],
        },
        { name: 'script' },
      ],
    },
    image: {
      resizeOptions: [
        {
          name: 'resizeImage:original',
          label: '默认图片宽度',
          value: null,
        },
        {
          name: 'resizeImage:50',
          label: '50% 页面宽度',
          value: '50',
        },
        {
          name: 'resizeImage:75',
          label: '75% 页面宽度',
          value: '75',
        },
      ],
      toolbar: [
        'imageTextAlternative',
        'toggleImageCaption',
        '|',
        'imageStyle:inline',
        'imageStyle:wrapText',
        'imageStyle:breakText',
        '|',
        'resizeImage',
      ],
      insert: {
        integrations: ['upload', 'assetManager', 'url'],
      },
    },
    list: {
      properties: {
        styles: true,
        startIndex: true,
        reversed: true,
      },
    },
    link: {
      decorators: {
        toggleDownloadable: {
          mode: 'manual',
          label: '可下载',
          attributes: {
            download: 'file',
          },
        },
        openInNewTab: {
          mode: 'manual',
          label: '在新标签页打开',
          defaultValue: true, // This option will be selected by default.
          attributes: {
            target: '_blank',
            rel: 'noopener noreferrer',
          },
        },
      },
      addTargetToExternalLinks: true,
      defaultProtocol: 'https://',

    },

    placeholder: '在此输入或粘贴您的内容!',
    style: {
      definitions: [
        {
          name: '标题',
          element: 'h1',
          classes: ['document-title'],
        },
        {
          name: '副标题',
          element: 'h2',
          classes: ['document-subtitle'],
        },
        {
          name: '提示',
          element: 'p',
          classes: ['callout'],
        },
        {
          name: '侧边引用',
          element: 'blockquote',
          classes: ['side-quote'],
        },
        {
          name: '需要澄清',
          element: 'span',
          classes: ['needs-clarification'],
        },
        {
          name: '宽间距',
          element: 'span',
          classes: ['wide-spacing'],
        },
        {
          name: '小型大写',
          element: 'span',
          classes: ['small-caps'],
        },
        {
          name: '代码（暗色）',
          element: 'pre',
          classes: ['stylish-code', 'stylish-code-dark'],
        },
        {
          name: '代码（亮色）',
          element: 'pre',
          classes: ['stylish-code', 'stylish-code-bright'],
        },
      ],
    },
    table: {
      contentToolbar: [
        'tableColumn',
        'tableRow',
        'mergeTableCells',
        'tableProperties',
        'tableCellProperties',
        'toggleTableCaption',
      ],
    },
  }

  const onAttrs = {
    change: () => {
    },
  }

  const setOnAttrs = () => {
    editor.value?.model.document.on('change:data', () => {
      onAttrs.change()
    })
  }

  const install = async () => {
    try {
      if (!toolbar.value || !contentRef.value)
        throw new Error('无法获取到组件挂载dom')

      editor.value = await MultiRootEditor
        .create(
          {
            // header: headerRef.value,
            content: contentRef.value,
          },
          merge(_defaultConfig, config),
        )

      MyCustomUploadAdapterPlugin(editor.value)
      setOnAttrs()
      toolbar.value.appendChild(editor.value.ui.view.toolbar.element!)
    }
    catch (err: any) {
      console.log(err)
    }
  }

  function destroy() {
    editor.value?.destroy()
    editor.value = undefined
  }

  onUnmounted(() => {
    destroy()
  })

  return { toolbar, contentRef, install, editor, onAttrs, destroy }
}

function MyCustomUploadAdapterPlugin(editor: Editor) {
  editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
    return new MyUploadAdapter(loader)
  }
}

class MyUploadAdapter implements UploadAdapter {
  loader: FileLoader
  abort = () => { }
  constructor(loader: FileLoader) {
    this.loader = loader
  }

  upload() {
    return this.loader.file
      .then(file => new Promise<UploadResponse>((resolve, reject) => {
        if (!file)
          return reject(new Error('文件不存在'))
        uploadFile(file, undefined, new axios.CancelToken(() => {
          // this.abort = c; // 保存取消函数
        })).then(({ success, error }) => {
          if (error.length)
            return reject(new Error(error[0]!.messge || '上传失败'))
          const item = success[0]!
          const path = joinFilePathById(item.id)
          resolve({ default: path })
        }).then(reject)
      }))
  }
}

class MyAssetManagerPlugin extends Plugin {
  init() {
    const editor = this.editor

    // 添加按钮到编辑器工具栏
    editor.ui.componentFactory.add('assetManager', (locale) => {
      const view = new ButtonView(locale)

      view.set({
        label: 'Insert Image',
        icon: imageIcon(),
        tooltip: true,
      })

      // 点击按钮时，打开自定义资产管理器
      view.on('execute', () => {
        this.openAssetManager(editor)
      })

      return view
    })
  }

  openAssetManager(editor: Editor) {
    // 自定义资产管理器的逻辑，可以是一个 Modal 或弹窗
    // 在此实现打开资产管理器的逻辑，比如你可以使用 Vue 或任何前端库
    // 例如，打开一个 Modal 并从中选择图片

    const insertLink = (file: UploadFileInfo, _writer: Writer) => {
      const position = editor.model.document.selection.getFirstPosition()
      editor.model.insertContent(_writer.createText(file.originalName || file.fileName!, {
        linkHref: joinFilePathById(file.id!),
      }), position)
    }

    useFileMangerModal((files, type) => {
      editor.model.change((_writer) => {
        if (type === FileType.图片) {
          editor.execute('insertImage', {
            source: files.map(v => ({
              src: joinFilePathById(v.id!),
              sourceId: v.id,
            })),
          })
        }
        else
          if (type === FileType.视频) {
            files.forEach((v) => {
              if (v.contentType === 'video/mp4')
                editor.execute('htmlEmbed', `<video width="100%" height="360" controls> <source src="${joinFilePathById(v.id)}" source-id="${v.id}" type="${v.contentType}"> 您的浏览器不支持视频标签。</video>`)
              else insertLink(v, _writer)
            })
          }
          else {
            files.forEach((v) => {
              insertLink(v, _writer)
            })
          }
      })
    })
  }
}

function imageIcon() {
  return '<svg t="1726799055285" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15384" width="200" height="200"><path d="M482.133333 853.333333 42.666667 853.333333c-25.6 0-42.666667-17.066667-42.666667-42.666667L0 42.666667c0-25.6 17.066667-42.666667 42.666667-42.666667l298.666667 0c25.6 0 42.666667 17.066667 42.666667 42.666667l0 42.666667 469.333333 0c25.6 0 42.666667 17.066667 42.666667 42.666667l0 170.666667c0 25.6-17.066667 42.666667-42.666667 42.666667s-42.666667-17.066667-42.666667-42.666667L810.666667 170.666667 341.333333 170.666667C315.733333 170.666667 298.666667 153.6 298.666667 128L298.666667 85.333333 85.333333 85.333333l0 682.666667 396.8 0c25.6 0 42.666667 17.066667 42.666667 42.666667S507.733333 853.333333 482.133333 853.333333z" p-id="15385"></path><path d="M469.333333 853.333333 42.666667 853.333333c-12.8 0-25.6-4.266667-34.133333-17.066667C0 827.733333 0 814.933333 0 802.133333l128-512C132.266667 268.8 149.333333 256 170.666667 256l810.666667 0c12.8 0 25.6 4.266667 34.133333 17.066667C1024 281.6 1024 298.666667 1024 311.466667l-72.533333 251.733333c-4.266667 21.333333-29.866667 34.133333-51.2 29.866667-21.333333-4.266667-34.133333-29.866667-29.866667-51.2L925.866667 341.333333 204.8 341.333333 98.133333 768 469.333333 768c25.6 0 42.666667 17.066667 42.666667 42.666667S494.933333 853.333333 469.333333 853.333333z" p-id="15386"></path><path d="M853.333333 768l-256 0c-25.6 0-42.666667-17.066667-42.666667-42.666667s17.066667-42.666667 42.666667-42.666667l256 0c25.6 0 42.666667 17.066667 42.666667 42.666667S878.933333 768 853.333333 768z" p-id="15387"></path><path d="M725.333333 896c-25.6 0-42.666667-17.066667-42.666667-42.666667l0-256c0-25.6 17.066667-42.666667 42.666667-42.666667s42.666667 17.066667 42.666667 42.666667l0 256C768 878.933333 750.933333 896 725.333333 896z" p-id="15388"></path><path d="M725.333333 1024c-166.4 0-298.666667-132.266667-298.666667-298.666667s132.266667-298.666667 298.666667-298.666667 298.666667 132.266667 298.666667 298.666667S891.733333 1024 725.333333 1024zM725.333333 512c-119.466667 0-213.333333 93.866667-213.333333 213.333333s93.866667 213.333333 213.333333 213.333333 213.333333-93.866667 213.333333-213.333333S844.8 512 725.333333 512z" p-id="15389"></path></svg>'
}
