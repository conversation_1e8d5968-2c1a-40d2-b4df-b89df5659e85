
import type { ProxyOptions } from 'vite'

function proxy(env: Record<string, string>): Record<string, string | ProxyOptions> {
  const proxyTarget = env.VITE_APP_PROXY_TARGET
  return {
    '/api/': {
      target: proxyTarget,
      changeOrigin: true,
      secure: false,
    },
    '/connect/': {
      target: proxyTarget,
      changeOrigin: true,
      secure: false,
    },
    '/files/': {
      target: proxyTarget,
      changeOrigin: true,
      secure: false,
    },
    '/fileExplorer/': {
      target: proxyTarget,
      changeOrigin: true,
      secure: false,
    },
    '/uploaded-images/': {
      target: proxyTarget,
      changeOrigin: true,
      secure: false,
    },
    '/msgHub/': {
      target: proxyTarget,
      changeOrigin: true,
      secure: false,
    },
    '/api/msgHub/': {
      target: proxyTarget,
      changeOrigin: true,
      secure: false,
    },
  }
}

export default proxy
