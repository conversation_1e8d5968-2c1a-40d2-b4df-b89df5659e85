import type { Plugin } from 'vite'

/**
 * 阻止所有的刷新页面操作，请手动刷新
 * @returns
 */
export default function preventFullReloadPlugin(): Plugin {
  return {
    name: 'vite-plugin-prevent-full-reload',
    configureServer(server) {
      const originalSend = server.ws.send

      server.ws.send = function (payload, ...args) {
        if (payload.type === 'full-reload') {
          // 阻止 full-reload 消息
          return
        }
        return originalSend.call(this, payload, ...args)
      }
    },
  }
}
