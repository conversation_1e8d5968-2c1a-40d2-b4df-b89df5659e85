<!-- 东盟国情概况 -->
<template>
  <div class="mx-auto max-w-7xl min-h-screen bg-[#f5f7fa] p-6 md:p-10">
    <div class="flex gap-8">
      <!-- 左侧书籍列表 -->
      <div class="w-80 flex-shrink-0">
        <div class="sticky top-6">
          <div class="overflow-hidden border border-[#e5e7eb] rounded-2xl bg-white shadow-lg">
            <div class="from-[#1976d2] to-[#1565c0] bg-gradient-to-r p-6">
              <h3 class="text-xl text-white font-bold">东盟传播刊物</h3>
              <p class="mt-1 text-sm text-blue-100">{{ booksList.length }} 本刊物</p>
            </div>
            <div class="max-h-[calc(100vh-200px)] overflow-y-auto">
              <div
                v-for="(book) in booksList"
                :key="book.id"
                class="group relative cursor-pointer border-b border-gray-100 p-4 transition-all duration-300 hover:bg-blue-50"
                :class="{ 'bg-blue-50 border-l-4 border-l-[#1976d2]': currentBookId === book.id }"
                @click="selectBook(book.id)"
              >
                <div class="flex gap-3">
                  <!-- 封面或生成的标题封面 -->
                  <div class="h-20 w-16 overflow-hidden border border-gray-200 rounded-lg shadow-sm">
                    <img
                      v-if="book.cover"
                      class="h-full w-full object-cover"
                      :src="book.cover"
                      :alt="book.title"
                    >
                    <div
                      v-else
                      class="h-full w-full flex items-center justify-center p-2 text-center text-xs text-white font-bold leading-tight"
                      :style="{ background: generateGradient(book.title) }"
                    >
                      {{ getTitleForCover(book.title) }}
                    </div>
                  </div>
                  <div class="min-w-0 flex-1">
                    <h4 class="line-clamp-2 mb-1 text-sm text-gray-800 font-semibold leading-tight">
                      {{ book.title }}
                    </h4>
                    <p class="mb-2 text-xs text-gray-500">{{ book.author }}</p>
                    <div class="flex flex-wrap gap-1">
                      <span
                        v-for="tag in book.tags.slice(0, 2)"
                        :key="tag"
                        class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-600"
                      >
                        {{ tag }}
                      </span>
                      <span v-if="book.tags.length > 2" class="text-xs text-gray-400">
                        +{{ book.tags.length - 2 }}
                      </span>
                    </div>
                  </div>
                </div>
                <!-- 选中指示器 -->
                <div
                  v-if="currentBookId === book.id"
                  class="absolute right-3 top-1/2 h-2 w-2 transform rounded-full bg-[#1976d2] -translate-y-1/2"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧详情内容 -->
      <div class="min-w-0 flex-1">
        <div v-if="currentBook" class="space-y-8">
          <!-- 基本信息卡片 -->
          <div class="border border-[#e5e7eb] rounded-2xl bg-white p-8 shadow-lg">
            <div class="flex items-start gap-8">
              <div class="group relative">
                <!-- 大封面或生成的标题封面 -->
                <div class="h-64 w-48 overflow-hidden border border-gray-200 rounded-xl shadow-lg transition-transform duration-300 group-hover:scale-105">
                  <img
                    v-if="currentBook.cover"
                    class="h-full w-full object-cover"
                    :src="currentBook.cover"
                    :alt="currentBook.title"
                  >
                  <div
                    v-else
                    class="h-full w-full flex items-center justify-center p-6 text-center text-lg text-white font-bold leading-tight"
                    :style="{ background: generateGradient(currentBook.title) }"
                  >
                    {{ (currentBook.title) }}
                  </div>
                </div>
                <div class="absolute inset-0 rounded-xl bg-black/0 transition-all duration-300 group-hover:bg-black/10" />
              </div>
              <div class="flex-1 space-y-6">
                <div>
                  <h1 class="mb-4 text-3xl text-[#1a237e] font-bold leading-tight md:text-4xl">
                    {{ currentBook.title }}
                  </h1>
                  <p class="text-lg text-[#374151] leading-relaxed">
                    {{ currentBook.description }}
                  </p>
                </div>

                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div class="flex items-center gap-3">
                    <div class="h-10 w-10 flex items-center justify-center rounded-full bg-[#e3eafc]">
                      <svg class="h-5 w-5 text-[#1976d2]" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500">作者/主办方</p>
                      <p class="text-gray-800 font-semibold">{{ currentBook.author }}</p>
                    </div>
                  </div>

                  <div class="flex items-center gap-3">
                    <div class="h-10 w-10 flex items-center justify-center rounded-full bg-[#e3eafc]">
                      <svg class="h-5 w-5 text-[#1976d2]" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1z" />
                      </svg>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500">出版/举办时间</p>
                      <p class="text-gray-800 font-semibold">{{ currentBook.publishTime }}</p>
                    </div>
                  </div>
                </div>

                <div class="flex flex-wrap gap-2">
                  <span
                    v-for="tag in currentBook.tags"
                    :key="tag"
                    class="rounded-full from-[#fbeee6] to-[#f3e8ff] bg-gradient-to-r px-4 py-2 text-sm text-[#b26a00] font-medium shadow-sm"
                  >
                    {{ tag }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 评论与新闻区 -->
          <div class="border border-[#e5e7eb] rounded-2xl bg-white p-8 shadow-lg">
            <div class="mb-8 flex items-center justify-between">
              <h2 class="flex items-center gap-3 text-2xl text-[#1a237e] font-bold">
                <div class="h-8 w-1 rounded-full from-[#1976d2] to-[#1565c0] bg-gradient-to-b" />
                相关评论与新闻
              </h2>
              <span class="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-600">
                {{ currentComments.length }} 条
              </span>
            </div>

            <div class="space-y-4">
              <div
                v-for="comment in currentComments"
                :key="comment.id"
                class="group border border-[#e3eafc] rounded-xl from-[#f8fafc] to-[#f1f5f9] bg-gradient-to-r p-6 transition-all duration-300 hover:scale-[1.02] hover:shadow-md"
              >
                <div class="flex flex-col gap-4 md:flex-row md:items-center">
                  <div class="flex items-center gap-3 md:w-40">
                    <div class="h-8 w-8 flex items-center justify-center rounded-full from-[#1976d2] to-[#1565c0] bg-gradient-to-r">
                      <span class="text-sm text-white font-bold">
                        {{ comment.author.charAt(0) }}
                      </span>
                    </div>
                    <div>
                      <p class="text-sm text-[#1976d2] font-bold">{{ comment.author }}</p>
                      <p class="text-xs text-gray-500">{{ comment.type === 'news' ? '新闻' : '用户' }}</p>
                    </div>
                  </div>
                  <div class="flex-1 text-[#374151] leading-relaxed">
                    {{ comment.content }}
                  </div>
                  <div class="text-right text-xs text-[#90a4ae] md:w-24">
                    {{ comment.date }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 书籍/论坛基本信息接口
interface BookInfo {
  id: string
  title: string
  description: string
  cover?: string // 可选的封面
  author: string
  publishTime: string
  tags: string[]
  comment: Comment[]
}

// 评论/新闻接口
interface Comment {
  id: string
  author: string
  content: string
  date: string
  type: 'user' | 'news'
  bookId: string
}

definePage({ meta: { title: '我对东盟传播' } })

// 当前选中的书籍ID
const currentBookId = ref('book_1')

// 书籍列表数据
const booksList = ref<BookInfo[]>([
  {
    id: 'book_1',
    title: '商业进行时——商业周刊精选必读（全10册）',
    description: '包含《东南亚——「新大陆」？》等专题电子书，分析东盟与中国经济关系、美国拉拢东盟加入TPP等议题，涵盖东南亚各国经济、政治、文化多维度内容',
    author: '商业周刊/中文版',
    publishTime: '2016',
    tags: ['东盟经济', 'TPP', '东南亚发展', '商业分析'],
    comment: [
      {
        id: 'comment_1',
        author: '商业周刊',
        content: '面对经济发展和自身安全的考量，东盟对中国究竟存在何种矛盾心态？美国为何要撇下中国拉拢东盟国家加入TPP？',
        date: 'Unknown',
        type: 'news',
        bookId: 'book_1',
      },
    ],
  },
  {
    id: 'book_2',
    title: '2025中国—东盟图书影响力报告',
    description: '以147家中国出版机构对东盟输出版权和引进版权的图书为线索，分析近5年中国与东盟图书交流现状、成果与挑战，揭示从数量提升转化为影响力提升的趋势',
    author: '中国—东盟图书文化周组委会、广西出版传媒集团与百道网',
    publishTime: '2025-07-05',
    tags: ['图书交流', '版权贸易', '文化传播', '影响力指数'],
    comment: [
      {
        id: 'comment_2',
        author: '百道网',
        content: '2020-2024年，中国出版机构向东盟各国共输出图书3395种，引进东盟图书186种。2024年输出品种数较2020年增长4.2%，影响力指数增长183%',
        date: '2025-07-05',
        type: 'news',
        bookId: 'book_2',
      },
      {
        id: 'comment_3',
        author: '邬书林',
        content: '文明因交流而多彩，文明因互鉴而丰富，希望图书文化交流能够进一步架起中国与东盟民众心灵沟通的桥梁',
        date: '2025-07-05',
        type: 'news',
        bookId: 'book_2',
      },
    ],
  },
  {
    id: 'book_3',
    title: '数字「一带一路」框架下中国—东盟数字贸易发展问题研究',
    description: '研究中国—东盟数字贸易合作现状与挑战，提出从跨境电商、数字服务贸易合作、数字贸易基础建设等方面推进合作的建议',
    author: '肖宇，梁威',
    publishTime: '2023-09-18',
    tags: ['数字贸易', '一带一路', '跨境电商', '规则谈判'],
    comment: [
      {
        id: 'comment_4',
        author: '北京工业大学学报',
        content: '东盟可成为中国在数字「一带一路」框架下与沿线国家发展数字贸易的突破口，双方合作互补性强但面临治理体系滞后等制约',
        date: '2023-09-18',
        type: 'news',
        bookId: 'book_3',
      },
    ],
  },
])

// 计算属性：当前选中的书籍
const currentBook = computed(() =>
  booksList.value.find(book => book.id === currentBookId.value),
)

// 计算属性：当前书籍的评论
const currentComments = computed(() =>
  currentBook.value?.comment || [],
)

// 选择书籍
function selectBook(bookId: string) {
  currentBookId.value = bookId
}

// 生成渐变色背景
function generateGradient(title: string): string {
  const gradients = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)',
    'linear-gradient(135deg, #fad0c4 0%, #ffd1ff 100%)',
  ]

  // 根据标题生成一个稳定的索引
  let hash = 0
  for (let i = 0; i < title.length; i++) {
    const char = title.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }

  return gradients[Math.abs(hash) % gradients.length]
}

// 获取用于封面显示的标题（截取前几个字符）
function getTitleForCover(title: string): string {
  // 移除标点符号，取前8个字符
  const cleanTitle = title.replace(/[《》「」【】（）()—-]/g, '')
  return cleanTitle.length > 8 ? `${cleanTitle.substring(0, 8)}...` : cleanTitle
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 支持换行显示 */
.w-full.h-full.flex.items-center.justify-center {
  white-space: pre-line;
}
</style>
