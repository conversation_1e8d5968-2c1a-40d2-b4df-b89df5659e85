import type { App } from 'vue'

const directivesList: Record<string, { default: () => void }> = import.meta.glob('./*.ts', { eager: true })

const directives = {
  install(app: App<Element>) {
    Object.keys(directivesList).forEach((key) => {
      // 注册所有自定义指令
      const p = key.split('/')
      const name = p[p.length - 1]?.split('.')[0]
      if (name)
        app.directive(name, directivesList[key]!.default)
    })
  },
}

export default directives
