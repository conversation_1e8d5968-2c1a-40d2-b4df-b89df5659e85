<!--
 * @Author: 龙兆柒 <EMAIL>
 * @Date: 2023-03-20 14:55:32
 * @LastEditors: luckymiaow
 * @LastEditTime: 2023-05-31 17:07:13
 * @FilePath: \ch2-template-vue\src\layouts\layout\components\Main\Maximize.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div v-if="themeConfig.maximize" class="maximize-box" @click="exitMaximize">
    <div class="maximize" />
    <c-icon-close-outlined class="absolute right-4px top-0px z-24 cursor-pointer text-16px c-red" />
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores'
import { computed } from 'vue'

const globalStore = useAppStore()

const themeConfig = computed(() => globalStore.themeConfig)

function exitMaximize() {
  globalStore.setThemeConfig({ ...themeConfig.value, maximize: false })
}
</script>

<style scoped lang="less">
.maximize-box {
  cursor: pointer;
  position: relative;
  .maximize {
    position: absolute;
    top: -40px;
    right: -30px;
    width: 70px;
    height: 70px;
    background-color: rgba(128, 128, 128, 0.5);
    opacity: 0.5;
    border-radius: 50%;
    z-index: 2;
    &:hover {
      background-color: rgba(128, 128, 128, 0.7);
    }
  }
}
</style>
