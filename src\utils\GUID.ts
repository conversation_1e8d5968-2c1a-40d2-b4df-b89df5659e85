export class Guid {
  static readonly empty = '00000000-0000-0000-0000-000000000000'

  /**
   * 检查给定的 GUID 是否为空
   * @param guid - 要检查的 GUID 值
   * @returns 如果 GUID 为空则返回 true，否则返回 false
   */
  static isNull(guid?: GUID) {
    return !guid || guid === Guid.empty
  }

  /**
   * 检查给定的 GUID 是否非空
   * @param guid - 要检查的 GUID 值
   * @returns 如果 GUID 非空则返回 true，否则返回 false
   */
  static isNotNull(guid?: GUID) {
    return !!guid && guid !== Guid.empty
  }
}
