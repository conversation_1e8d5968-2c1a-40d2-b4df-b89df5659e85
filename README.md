<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2022-04-15 16:15:09
 * @LastEditors: luckymiaow
-->

# ch2-template-vue

## api生成，配置文件api.config.ts
```
 pnpm gen_api 

```

## 约定式路由

[Unplugin Vue Router](https://uvr.esm.is/guide/file-based-routing.html)

安装这个插件，模板内的样式颜色使用less变量。安装插件后键入@会有变量提示
[vue less vars][https://](https://marketplace.visualstudio.com/items?itemName=luckymiaow.vue-less-vars)]

- 🎨 [UnoCSS](https://github.com/antfu/unocss) - the instant on-demand atomic CSS engine

- 😃 [Use icons from any icon sets with classes](https://github.com/antfu/unocss/tree/main/packages/preset-icons)

## 主题

### 1. 使用 LESS 变量控制主题

本项目利用 LESS 作为 CSS 预处理器，通过定义一系列的变量来控制主题样式。这些变量参考了 Ant Design Vue 的官方主题定制文档。以下是一些关键的实现细节：

变量定义：我们定义了一系列的 LESS 变量，这些变量对应于 [.Ant Design Vue ](https://www.antdv.com/docs/vue/customize-theme-cn#api)文档中提到的主题定制 API。
样式应用：在项目的各个组件和样式表中，这些变量被用来指定颜色、间距、字体大小等属性，从而实现整体的视觉一致性。

### 2. 使用 UnoCSS 获取颜色

我们还集成了 UnoCSS，这是一个功能强大的原子 CSS 引擎。通过 UnoCSS，我们可以直接在 HTML 元素上使用类名来引用定义的颜色变量。这些变量同样来源于 [.Ant Design Vue ](https://www.antdv.com/docs/vue/customize-theme-cn#api) 的官方主题定制文档，主要关注以 "color" 开头的 API。以下是一个示例：

```html
<div class="c-primary bg-primary-bg hover:c-primary-hover hover:bg-error hover:c-success">这是一段文字</div>

```

## Project setup

```
pnpm config set registry http://172.22.1.75/
```

```
pnpm install
```

### Compiles and hot-reloads for development

```
pnpm run serve
```

### Compiles and minifies for production

```
pnpm run build
```

### Lints and fixes files

```
pnpm run lint
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).
