import type { TabsMenuProps, TabsState } from '@/types/interfaces'
import type { RouteLocationRaw, Router } from 'vue-router'
import { HomeRoute } from '@/router/utils/useRouteConfig'
/*
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: 龙兆柒
 */
import { defineStore } from 'pinia'

// TabsStore
export const useTabsStore = defineStore({
  id: 'router-tabs',
  state: (): TabsState => ({
    tabsMenuList: [],
    activeKey: '/home',
    userId: '',
  }),

  actions: {
    // Add Tabs
    routerPush(router: Router, to: RouteLocationRaw, title?: string): void {
      const route = router.resolve(to)
      const tabsParams = {
        icon: route.meta?.icon,
        title: title || route.meta?.title,
        path: route.fullPath,
        name: route.meta?.title,
        close: !route.meta?.isAffix,
      } as any
      this.addTabs(tabsParams, tabsParams.path)
      router.push(route)
    },
    async addTabs(tabItem: TabsMenuProps, activeKey?: string) {
      if (!tabItem.name)
        return
      if (this.tabsMenuList.every((item: { path: string }) => item.path !== tabItem.path))
        this.tabsMenuList.push(tabItem)

      if (activeKey)
        this.activeKey = activeKey
      else this.activeKey = tabItem.path
    },
    // Remove Tabs
    async removeTabs(tabPath: string, router: Router, isCurrent = true) {
      const { tabsMenuList } = this
      if (isCurrent) {
        tabsMenuList.forEach((item: { path: string }, index: number) => {
          if (item.path !== tabPath)
            return
          const nextTab = tabsMenuList[index + 1] || tabsMenuList[index - 1]
          if (!nextTab)
            router.push(HomeRoute.path)
          else router.push(nextTab.path)
          this.activeKey = nextTab?.path || HomeRoute.path
        })
      }

      this.tabsMenuList = tabsMenuList.filter((item: { path: string }) => item.path !== tabPath)
    },
    // Close MultipleTab
    async closeMultipleTab(tabsMenuValue?: string) {
      this.tabsMenuList = this.tabsMenuList.filter((item: { path: string | undefined, close: any }) => {
        return item.path === tabsMenuValue || !item.close
      })
    },
    // Set Tabs
    async setTabs(tabsMenuList: TabsMenuProps[]) {
      this.tabsMenuList = tabsMenuList
    },
    // Set Tabs Title
    async setTabsTitle(title: string) {
      const nowFullPath = location.hash.substring(1)
      this.tabsMenuList.forEach((item) => {
        if (item.path === nowFullPath)
          item.title = title
      })
    },
  },

  persist: {
    storage: sessionStorage,
  },
})
