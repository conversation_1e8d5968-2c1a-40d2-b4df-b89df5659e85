import type { StandardItem as IStandardItem } from 'ch2-components/lib/Standard/types'
import { FieldType } from 'ch2-components/lib/Standard/types'

/*
 * @Description:
 * @Author: luckymiaow
 * @Date: 2022-05-23 10:48:54
 * @LastEditors: lin
 */
import { assign } from 'lodash-es'
import { ref } from 'vue'

let key = 0

export class StandardItem implements IStandardItem {
  key: string

  isEdit: boolean

  parent: StandardItem | null = null

  order = 0

  value = ''

  isDefault = false

  label = ''

  children: StandardItem[] = []

  type: FieldType = 0

  remark: string | null = null

  title?: string

  public constructor(data: IStandardItem, parent: StandardItem | null) {
    assign(this, data)
    this.key = (key++).toString()
    this.isEdit = false
    this.parent = parent
    this.title = data.label
  }
}

export function toStandardEdit(items: IStandardItem[], parent: StandardItem | null) {
  return items.map<StandardItem>((e) => {
    const data = new StandardItem(e, parent)
    if (e.children?.length)
      data.children = toStandardEdit(e.children, data)
    return data
  })
}

export function toSaveData(items: StandardItem[]): IStandardItem[] {
  return items.map<IStandardItem>((item) => {
    return {
      order: item.order,
      value: item.value,
      isDefault: item.isDefault,
      label: item.label,
      children: toSaveData(item.children),
      type: item.type,
      remark: item.remark,
    }
  })
}

export function useStandardItem(standardItems: Ref<StandardItem[]>) {
  const expandedRowKeys = ref<string[]>([])

  function addItem(value: StandardItem[], parent: StandardItem | null) {
    value.push({
      order: value.length || 0,
      value: '',
      isDefault: false,
      label: '',
      children: [],
      remark: null,
      type: FieldType.文本,
      parent,
      key: (key++).toString(),
      isEdit: true,
    })
    if (parent) {
      expandedRowKeys.value.push(parent.key)
      expandedRowKeys.value = Array.from(new Set(expandedRowKeys.value))
    }
  }

  function deleteItem(parent: StandardItem, k: string) {
    if (parent)
      parent.children = parent.children.filter(e => e.key !== k)
    else
      standardItems.value = standardItems.value.filter(e => e.key !== k)
  }

  const expandedRowsChange = (keys: string[]) => {
    expandedRowKeys.value = keys
  }

  const labelChange = (e: { target: { value: string } }, record: StandardItem) => {
    if (record.isEdit && record.value === record.label)
      record.value = e.target.value
    record.label = e.target.value
  }

  return {
    standardItems,
    toSaveData,
    deleteItem,
    addItem,
    toStandardEdit,
    expandedRowKeys,
    expandedRowsChange,
    labelChange,
  }
}
