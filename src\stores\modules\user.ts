import type { UserRoleViewModel } from '@/api/models'
import { setHomeRouteByRoles } from '@/router/utils/useRouteConfig'
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: (): IUserState => ({
    info_token: null,
    refresh_token: null,
    expires_time: null,
    userList: [],
  }),
  getters: {
    active(): User {
      return this.userList.find(user => user.info_token === this.token) || new User()
    },

    token(): string | null {
      if (this.info_token && this.expires_time && new Date() > new Date(this.expires_time))
        return null

      return this.info_token
    },
    isSimulation(): boolean {
      return this.userList.length > 1
    },
  },
  actions: {
    setToken(obj: { refresh_token: string, expires_time: string, token: string }) {
      this.refresh_token = obj.refresh_token
      this.expires_time = obj.expires_time
      this.info_token = obj.token
    },
    async setUser(data: User, type: 'overlay' | 'add' = 'overlay') {
      if (type === 'add')
        this.userList.push(data)
      else
        this.userList = [data]
      setHomeRouteByRoles(this.active.roles)
    },
    async updateUser(data: User) {
      Object.assign(this.active, data)
      setHomeRouteByRoles(this.active.roles)
    },
    clear() {
      this.$reset()
    },

  },
})

export class Token {
  // 为了和外层的token区分
  info_token: string | null = ''

  refresh_token: string | null = ''

  expires_time: string | null = null
}

export class User extends Token {
  remember = false // 记住密码，false => 关闭浏览器清除缓存

  /** 姓名 */
  name = ''

  /** 用户名（登录名） */
  userName = ''

  userId: string | null = null

  roles: UserRoleViewModel[] = []

  info: any = {}
}

export interface IUserState extends Token {

  userList: User[]
}
