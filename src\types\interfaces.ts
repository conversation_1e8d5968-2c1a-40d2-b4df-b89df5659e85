import type { GlobalToken } from 'ant-design-vue/es/theme'
import type { SeedToken } from 'ant-design-vue/es/theme/internal'
import type { RuleObject, StoreValue } from 'ant-design-vue/lib/form/interface'
import type { RouteRecordName, RouteRecordRaw } from 'vue-router'

export interface RouterItem extends Omit<RouteRecordRaw, 'children'> {
  children?: RouterItem[]
}

export interface LayoutStructureInterface {
  collapsed: boolean
}

/**
 * @description: 适用于antdv表单模块
 * @param {*}
 * @return {*}
 * @author: luckymiaow
 */
export class FormRule {
  required?: boolean

  message?: string = '请输入'

  trigger?: 'blur' | 'change' | ['change', 'blur'] | ['change'] | ['blur'] = 'blur'

  validator?: (rule: RuleObject, value: StoreValue) => void
}

export type FormRules = Record<string, FormRule | FormRule[]>

export type AssemblySizeType = 'default' | 'small' | 'large'
export type LayoutType = 'vertical' | 'classic' | 'transverse' | 'columns'

export interface GlobalState {
  token: string
  userInfo: any
  assemblySize: AssemblySizeType
  language: string
  themeConfig: ThemeConfigProps
}

type ExtractColorProps<T> = {
  [K in keyof T as K extends `color${string}` ? K : never]: T[K]
}

export type ThemeColor = ExtractColorProps<SeedToken>

export type ThemeColorKey = keyof ThemeColor

export type ThemeGlobalColor = ExtractColorProps<GlobalToken>

export type RemoveColorSuffix<T> = {
  [K in keyof T as K extends `${infer Prefix}color` ? Prefix : K]: T[K];
}

export type RemoveColorPrefix<T> = {
  [K in keyof T as K extends `color${infer Prefix}` ? Prefix : K]: T[K];
}

export interface ThemeConfigProps {
  maximize: boolean
  layout: LayoutType
  themeColor: ThemeColor
  darkMenu: boolean
  isDark: boolean
  isDarkBySystem: boolean
  isGrey: boolean
  isCollapse: boolean
  isWeak: boolean
  breadcrumb: boolean
  breadcrumbIcon: boolean
  tabs: boolean
  tabsIcon: boolean
  footer: boolean
}
/* tabsMenuProps */
export interface TabsMenuProps {
  icon?: string | null | undefined
  title?: string
  path: string
  name?: RouteRecordName
  close: boolean
  cached?: boolean
}

/* TabsState */
export interface TabsState {
  tabsMenuList: TabsMenuProps[]
  activeKey: string
  userId: string
}

/* AuthState */
export interface AuthState {
  routeName: string
  routeTitle: string
  authButtonList: {
    [key: string]: string[]
  }
  authMenuList: RouterItem[] | any
}

/* keepAliveState */
export interface keepAliveState {
  list: KeepAliveItem[]
}

export interface KeepAliveItem {
  path: string
  name?: string
}
