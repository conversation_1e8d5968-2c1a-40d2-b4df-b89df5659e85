<template>
  <div class="special-subject-container">
    <!-- 新闻列表页 -->
    <div v-if="!selectedArticle" class="news-list">
      <div class="header">
        <h1 class="title">涉我传播专题</h1>
        <p class="subtitle">关注涉及我国的重要新闻报道</p>
      </div>

      <div class="filters">
        <div class="filter-group">
          <label>站点筛选：</label>
          <select v-model="selectedSite" @change="filterArticles">
            <option value="">全部站点</option>
            <option v-for="site in uniqueSites" :key="site" :value="site">
              {{ site }}
            </option>
          </select>
        </div>

        <div class="filter-group">
          <label>搜索：</label>
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索标题或内容..."
            class="search-input"
            @input="filterArticles"
          >
        </div>
      </div>

      <div class="news-grid">
        <div
          v-for="article in filteredArticles"
          :key="article.文章链接"
          class="news-card"
          @click="selectArticle(article)"
        >
          <div class="card-header">
            <span class="site-tag">{{ article.站点 }}</span>
            <span class="publish-time">{{ formatTime(article.发布时间) }}</span>
          </div>

          <h3 class="article-title">{{ article.标题 }}</h3>

          <div class="article-meta">
            <span v-if="article.作者" class="author">作者：{{ article.作者 }}</span>
          </div>

          <div class="article-preview">
            {{ getPreview(article.内容) }}
          </div>

          <div class="card-footer">
            <button class="read-more-btn">阅读全文</button>
          </div>
        </div>
      </div>

      <div v-if="filteredArticles.length === 0" class="no-results">
        <p>没有找到相关文章</p>
      </div>
    </div>

    <!-- 文章详情页 -->
    <div v-else class="article-detail">
      <div class="detail-header">
        <button class="back-btn" @click="goBack">
          ← 返回列表
        </button>

        <div class="article-info">
          <h1 class="detail-title">{{ selectedArticle.标题 }}</h1>

          <div class="detail-meta">
            <span class="site">{{ selectedArticle.站点 }}</span>
            <span v-if="selectedArticle.作者" class="author">{{ selectedArticle.作者 }}</span>
            <span class="time">{{ formatTime(selectedArticle.发布时间) }}</span>
          </div>

          <div class="external-link">
            <a :href="selectedArticle.文章链接" target="_blank" class="source-link">
              查看原文 ↗
            </a>
          </div>
        </div>
      </div>

      <div class="detail-content">
        <div class="content-text" v-html="formatContent(selectedArticle.内容)" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import data from './data/涉我专题.json'

// 定义文章类型
interface Article {
  站点: string
  站点网址: string
  文章链接: string
  发布时间: number
  标题: string
  作者?: string
  内容: string
}

definePage({
  meta: {
    title: '涉我传播专题',
  },
})

// 响应式数据
const selectedArticle = ref<Article | null>(null)
const selectedSite = ref('')
const searchKeyword = ref('')

// 计算属性
const uniqueSites = computed(() => {
  const sites = [...new Set(data.map((article: Article) => article.站点))]
  return sites.sort()
})

const filteredArticles = ref<Article[]>(data as Article[])

// 方法
function filterArticles() {
  let filtered = data as Article[]

  // 按站点筛选
  if (selectedSite.value) {
    filtered = filtered.filter(article => article.站点 === selectedSite.value)
  }

  // 按关键词搜索
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(article =>
      article.标题.toLowerCase().includes(keyword)
      || article.内容.toLowerCase().includes(keyword)
      || (article.作者 && article.作者.toLowerCase().includes(keyword)),
    )
  }

  filteredArticles.value = filtered
}

function selectArticle(article: Article) {
  selectedArticle.value = article
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

function goBack() {
  selectedArticle.value = null
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

function formatTime(timestamp: number): string {
  // 假设时间戳是Excel格式的数字，需要转换
  // Excel时间戳从1900年1月1日开始计算
  const excelEpoch = new Date(1900, 0, 1)
  const date = new Date(excelEpoch.getTime() + (timestamp - 1) * 24 * 60 * 60 * 1000)

  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

function getPreview(content: string): string {
  // 移除HTML标签和特殊字符，获取前150个字符作为预览
  const cleanContent = content.replace(/<[^>]*>/g, '').replace(/\r\n/g, ' ')
  return cleanContent.length > 150 ? `${cleanContent.substring(0, 150)}...` : cleanContent
}

function formatContent(content: string): string {
  // 将换行符转换为HTML换行
  return content.replace(/\r\n/g, '<br>').replace(/\n/g, '<br>')
}

// 初始化
onMounted(() => {
  filterArticles()
})
</script>

<style scoped>
.special-subject-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 新闻列表页样式 */
.news-list {
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

.filters {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: 600;
  color: #495057;
  white-space: nowrap;
}

.filter-group select,
.search-input {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  min-width: 200px;
  transition: border-color 0.2s ease;
}

.filter-group select:focus,
.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.news-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.news-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.site-tag {
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.publish-time {
  color: #6c757d;
  font-size: 12px;
}

.article-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #212529;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  margin-bottom: 12px;
}

.author {
  color: #6c757d;
  font-size: 14px;
}

.article-preview {
  color: #495057;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
}

.read-more-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.read-more-btn:hover {
  background: #5a6fd8;
}

.no-results {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.no-results p {
  font-size: 1.1rem;
  margin: 0;
}

/* 文章详情页样式 */
.article-detail {
  min-height: 100vh;
}

.detail-header {
  margin-bottom: 30px;
}

.back-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  margin-bottom: 20px;
  transition: background-color 0.2s ease;
}

.back-btn:hover {
  background: #5a6268;
}

.article-info {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.detail-title {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 20px 0;
  color: #212529;
  line-height: 1.3;
}

.detail-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.detail-meta .site {
  background: #667eea;
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.detail-meta .author {
  color: #495057;
  font-size: 14px;
  font-weight: 500;
}

.detail-meta .time {
  color: #6c757d;
  font-size: 14px;
}

.external-link {
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
}

.source-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: color 0.2s ease;
}

.source-link:hover {
  color: #5a6fd8;
  text-decoration: underline;
}

.detail-content {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.content-text {
  font-size: 16px;
  line-height: 1.8;
  color: #212529;
  max-width: none;
}

.content-text :deep(br) {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .special-subject-container {
    padding: 15px;
  }

  .title {
    font-size: 2rem;
  }

  .filters {
    flex-direction: column;
    gap: 15px;
  }

  .filter-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .filter-group select,
  .search-input {
    min-width: 100%;
  }

  .news-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .detail-title {
    font-size: 1.5rem;
  }

  .detail-meta {
    flex-direction: column;
    gap: 10px;
  }

  .article-info,
  .detail-content {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 30px 20px;
  }

  .title {
    font-size: 1.8rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .news-card {
    padding: 15px;
  }

  .article-info,
  .detail-content {
    padding: 15px;
  }

  .content-text {
    font-size: 15px;
  }
}
</style>
