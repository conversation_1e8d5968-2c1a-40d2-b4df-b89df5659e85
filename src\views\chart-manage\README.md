# Chart管理模块

## 概述

Chart管理模块提供了完整的图表管理功能，包括图表模板管理、数据集管理和图表实例管理。该模块基于ECharts图表库，支持动态配置和预览。

## 模块结构

### 1. 图表模板管理 (template.vue)
- **功能**: 管理图表类型模板，包含ECharts配置模板和字段映射
- **主要特性**:
  - 支持Scriban语法的配置模板
  - 字段映射配置（支持树形结构）
  - 模板预览功能
  - 支持字段映射和样式配置两种类型

### 2. 数据集管理 (dataset.vue)
- **功能**: 管理图表数据源
- **主要特性**:
  - 支持手动录入数据
  - Excel文件导入
  - 字段定义管理
  - 数据预览和编辑

### 3. 图表实例管理 (chart-instance.vue)
- **功能**: 创建和管理具体的图表实例
- **主要特性**:
  - 选择图表模板和数据集
  - 字段映射配置
  - 样式配置
  - 实时预览
  - 图表导出

## API接口

### ChartStatistices类相关接口

#### 图表实例相关
- `GetChartsAsync()` - 分页查询图表实例
- `GetChartAsync(id)` - 获取单个图表实例详情
- `CreateChart_PostAsync(data)` - 创建新图表实例
- `UpdateChart_PostAsync(id, data)` - 更新图表实例
- `DeleteChart_PostAsync(id)` - 删除图表实例
- `PreviewChart_GetAsync(id)` - 预览图表配置

#### 图表模板相关
- `GetTemplatesAsync()` - 分页查询图表模板
- `GetTemplateAsync(id)` - 获取单个图表模板详情
- `CreateTemplate_PostAsync(data)` - 创建新图表模板
- `UpdateTemplate_PostAsync(id, data)` - 更新图表模板
- `DeleteTemplate_PostAsync(id)` - 删除图表模板

#### 数据集相关
- `GetDatasetsAsync()` - 分页查询数据集
- `GetDatasetAsync(id)` - 获取单个数据集详情
- `CreateDataset_PostAsync(data)` - 创建新数据集
- `UpdateDataset_PostAsync(id, data)` - 更新数据集
- `DeleteDataset_PostAsync(id)` - 删除数据集

## 数据模型

### ChartChartManagement (图表实例)
```typescript
{
  title: string;                    // 图表标题
  chartTypeId: GUID;               // 图表模板ID
  chartType: ChartTemplate;        // 图表模板对象
  fieldConfigJson: ChartConfig[];  // 字段映射配置
  styleConfigJson: ChartConfig[];  // 样式配置
  chartDatasetId: GUID;           // 数据集ID
  dataset: ChartDataset;          // 数据集对象
  updatedAt: Dayjs;               // 更新时间
  id: GUID;                       // 主键
}
```

### ChartTemplate (图表模板)
```typescript
{
  name: string;                    // 模板名称
  optionTemplate: string;          // ECharts配置模板
  fieldMappings: FieldMapping[];   // 字段映射配置
  updatedAt: Dayjs;               // 更新时间
  id: GUID;                       // 主键
}
```

### ChartDataset (数据集)
```typescript
{
  name: string;                           // 数据集名称
  fieldsJson: ChartFieldDefinition[];     // 字段定义
  records: any[][];                       // 数据记录
  sourceType: DatasetSourceType;          // 数据来源类型
  updatedAt: Dayjs;                      // 更新时间
  describe: string;                       // 描述
  id: GUID;                              // 主键
}
```

### ChartConfig (配置项)
```typescript
{
  name: string;    // 配置字段名称
  value: any;      // 配置值
}
```

## 使用流程

1. **创建图表模板**: 在模板管理中定义图表类型、ECharts配置模板和字段映射
2. **准备数据集**: 在数据集管理中创建或导入数据
3. **创建图表实例**: 选择模板和数据集，配置字段映射和样式
4. **预览和调整**: 使用预览功能查看效果，调整配置
5. **保存和使用**: 保存图表实例，可在其他地方引用

## 技术特性

- **响应式设计**: 支持各种屏幕尺寸
- **实时预览**: 配置变更后可立即预览效果
- **类型安全**: 使用TypeScript确保类型安全
- **组件化**: 基于Vue 3 Composition API
- **国际化**: 支持中文界面

## 注意事项

1. 图表模板的配置模板需要使用Scriban语法
2. 字段映射需要与数据集的字段定义匹配
3. 预览功能需要图表实例已保存后才能使用
4. 删除操作不可逆，请谨慎操作
