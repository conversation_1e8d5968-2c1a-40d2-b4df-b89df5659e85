<!--
 * @Author: 龙兆柒 <EMAIL>
 * @Date: 2023-03-24 09:44:28
 * @LastEditors: 龙兆柒 <EMAIL>
 * @LastEditTime: 2023-04-25 16:26:38
 * @FilePath: \ch2-template-vue\src\views\components\import\ExcelImport.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--  -->
<template>
  <a-modal v-model:open="visible" :title="title" style="width: 600px" @ok="handleOk">
    <a-form>
      <a-form-item label="模板下载">
        <a-button type="primary" @click="downloadTemp">
          <DownloadOutlined />
          下载模板
        </a-button>
      </a-form-item>

      <a-form-item label="文件上传" style="width: 85%">
        <c-upload-dragger
          :accept="accept"
          :upload-now="true"
          :api="uploadApi"
          :hidden-list="true"
          @remove="handleRemove"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import type { UploadProps } from 'ant-design-vue'
import type { UploadApi } from 'ch2-components/lib/upload/types'
import { DownloadOutlined } from '@ant-design/icons-vue'
import { ref, watch } from 'vue'

const props = defineProps<{
  uploadApi: UploadApi
  tempApi?: string
  tempTitle?: string
  visible: boolean
  title: string
}>()

const emit = defineEmits(['update:visible'])
defineExpose({
  visible: ref(props.visible),
  title: ref(props.title),
})

const accept = '.xlsx'
const uploadRef = ref()
const fileList = ref<UploadProps['fileList']>()

function handleOk() {
  // 关闭弹窗和重置上传
  emit('update:visible', false)
  uploadRef.value && (uploadRef.value.startUpload = true)
  fileList.value = []
}

function downloadTemp() {
  if (!props.tempApi)
    return
  const a = document.createElement('a')
  a.href = props.tempApi
  a.download = props.tempTitle || '模板.xlsx'
  a.click()
}
function handleRemove(_unused: any) {
  // 可扩展：移除文件时的处理
}

const visible = ref(props.visible)
const title = ref(props.title)

watch(() => props.visible, v => visible.value = v)
watch(() => props.title, v => title.value = v)
</script>

<style scoped></style>
