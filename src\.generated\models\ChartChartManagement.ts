import { ChartTemplate } from "./ChartTemplate";
import { ChartConfig } from "./ChartConfig";
import { ChartDataset } from "./ChartDataset";
import { PivotConfig } from "./PivotConfig";
/**图表实体（引用数据集）*/
export class ChartChartManagement {
  /**目录 /aa/bb/cc*/
  title?: string | null | undefined = null;
  chartTypeId: GUID = "00000000-0000-0000-0000-000000000000";
  /**图表模板类型（包含配置模板和字段映射）*/
  chartType?: ChartTemplate | null | undefined = null;
  /**图表数据映射*/
  fieldConfigJson?: ChartConfig[] | null | undefined = [];
  /**图表样式配置（如颜色、字体、布局等）*/
  styleConfigJson?: ChartConfig[] | null | undefined = [];
  /**关联的数据集（而非直接数据）*/
  chartDatasetId: GUID = "00000000-0000-0000-0000-000000000000";
  /**图表数据集（数据源）*/
  dataset?: ChartDataset | null | undefined = null;
  /**数据透视配置，存储行维度、列维度、指标、筛选条件等信息*/
  pivotConfig?: PivotConfig | null | undefined = null;
  updatedAt: Dayjs = dayjs();
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
