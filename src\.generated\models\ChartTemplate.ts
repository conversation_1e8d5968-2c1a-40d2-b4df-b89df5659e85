import { FieldMapping } from "./FieldMapping";
/**图表模板类型（包含配置模板和字段映射）*/
export class ChartTemplate {
  /**图表类型名称，如“折线图”*/
  name?: string | null | undefined = null;
  /**echarts配置模板（Scriban格式），包含占位符*/
  optionTemplate?: string | null | undefined = null;
  /**字段映射配置，描述该类型需要的配置字段（在模板中替换使用）*/
  fieldMappings?: FieldMapping[] | null | undefined = [];
  updatedAt: Dayjs = dayjs();
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
