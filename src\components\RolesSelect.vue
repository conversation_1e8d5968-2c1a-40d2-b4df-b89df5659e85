<!--
 * @Description: 角色下拉框
 * @Author: xiaoquexing
 * @Date: 2022-05-09 11:25:38
 * @LastEditors: Ztq
 * @LastEditTime: 2022-11-29 11:51:04
-->
<template>
  <c-select
    style="width: 200px"
    show-search
    :api="api.Roles.Find_GetAsync"
    :params="{ limit: 10, offset: 0 }"
    :field-names="{ label: 'name', value: 'id' }"
    mode="multiple"
  />
</template>

<script setup lang="ts">
// import { ref } from 'vue';

import * as api from '@/api'

// import * as models from '@/api/models';

// const params = ref({ limit: 10, offset: 0 });
</script>
