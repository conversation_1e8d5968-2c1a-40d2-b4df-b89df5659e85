<template>
  <Admin />
</template>

<script setup lang="ts">
import * as api from '@/api'
import { setMenuBadge } from '@/router'
import { onMounted, onUnmounted } from 'vue'
import Admin from './layout/Index.vue'

let badgeInterval: any = null

async function updateFeedbackBadge() {
  try {
    const res = await api.Feedbacks.GetFeedbackCountAsync()
    setMenuBadge('/feedback-manage/', { count: res })
  }
  catch {
    setMenuBadge('/feedback-manage/', { count: 0 })
  }
}

onMounted(() => {
  updateFeedbackBadge()
  badgeInterval = setInterval(updateFeedbackBadge, 60000)
})

onUnmounted(() => {
  if (badgeInterval)
    clearInterval(badgeInterval)
})
</script>
