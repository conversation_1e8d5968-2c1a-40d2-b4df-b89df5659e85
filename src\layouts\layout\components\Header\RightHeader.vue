<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: lin
-->

<template>
  <div class="tool-bar-ri">
    <a-button
      v-if="UserModule.isSimulation" type="primary" class="mr16px" danger size="small"
      @click="stopSimulation"
    >
      停止模拟
    </a-button>
    <div class="header-icon">
      <AssemblySize v-if="app.showAssemblySize" />
      <Language v-if="app.showLanguage" />
      <SearchMenu />
      <ThemeSetting v-if="app.showThemeConfig" />
      <!-- <Message /> -->
      <Fullscreen />
    </div>
    <span class="username">{{ name }}</span>
    <Avatar />
  </div>
</template>

<script setup lang="ts">
import { UserService } from '@/api/user'
import { dynamicRouter } from '@/router'
import { useAppStore, useUserStore } from '@/stores'
import { computed } from 'vue'
import AssemblySize from './components/AssemblySize.vue'

import Avatar from './components/Avatar.vue'
import Fullscreen from './components/Fullscreen.vue'
import Language from './components/Language.vue'
// import Message from './components/Message.vue'
import SearchMenu from './components/SearchMenu.vue'
import ThemeSetting from './components/ThemeSetting.vue'

const UserModule = useUserStore()

const app = useAppStore()

const name = computed(() => UserModule.active.name)

async function stopSimulation() {
  await UserService.stopSimulation()
  await dynamicRouter.Register()
}
</script>

<style lang="less">
.tool-bar-ri {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20px 0 0;
  max-height: 80px;
  position: absolute;
  right: 10px;

  .header-icon {
    display: flex;
    align-items: center;
    font-size: 1.6em;
    cursor: pointer;

    & > *:not(:first-child) {
      margin-left: 16px;
    }

    & > div:hover {
      // color: @primary-color;
    }
  }

  .username {
    margin: 0 20px;
    font-size: 1em;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
}
</style>
