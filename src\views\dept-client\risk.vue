<template>
  <div class="m-auto min-h-[calc(100vh-160px)] flex gap-5 p5 container">
    <a-affix :offset-top="80">
      <div class="h-[calc(100vh-160px)] w-80 rounded bg-bg-container p6 pr1 shadow-md">
        <div class="flex items-center justify-between pr4">
          <h3>风险预警</h3>
          <a-dropdown>
            <c-icon-dash-outlined />
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <a href="javascript:;" @click="onGroupAdd()">新增分组</a>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
        <div v-for="item of riskData" :key="item.id" class="ml-4 mt6 pr4">
          <div class="flex items-center justify-between">
            <h4>{{ item.name }}</h4>
            <a-dropdown>
              <c-icon-dash-outlined />
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a href="javascript:;" @click="onAdd(item)">新增标签</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;" @click="onEdit()">编辑</a>
                  </a-menu-item>
                  <a-menu-item>
                    <a href="javascript:;" class="c-error" @click="onGroupDel(item.id)">删除分组</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div v-for="(p, index) of item.riskWarnings" :key="index" class="ml-4 mt4">
            <div class="group relative mt2 flex cursor-pointer justify-between text-xs c-gray-600 hover:text-primary-active" @click="getArticlesData(p.id)">
              <div class="w-full flex justify-between"><span>{{ p.queryTag }}</span><span>{{ p.cachedDataCount }} 条</span></div>

              <div class="absolute right-0 top-0 hidden bg-#fff group-hover:block">
                <c-icon-edit-outlined class="mr-4 c-primary" @click.stop="onEdit()" />
                <c-icon-close-outlined class="c-error" @click.stop="onTagDel(p.id)" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-affix>

    <div class="w-full rounded bg-bg-container p-4 shadow-md">
      <div class="w-100%">
        <a-spin :spinning="rightSpinning">
          <div v-if="articles.length > 0">
            <div>
              <ListItem
                v-for="(article, index) in articles"
                :key="index"
                :article="article"
                @on-collect-success="getArticlesList"
              />
            </div>
            <div class="flex justify-end">
              <a-pagination
                v-model:current="pageData.current"
                v-model:page-size="pageData.pageSize"
                show-size-changer
                :show-total="(total) => `总 ${total} 条`"
                :total="pageData.total"
                @change="pageChange"
              />
            </div>
          </div>
          <a-empty v-else />
        </a-spin>
      </div>
    </div>
  </div>

  <a-drawer
    v-model:open="groupOpen"
    title="新建分组"
    placement="left"
    size="large"
    destroy-on-close
  >
    <c-pro-form
      v-model:value="groupForm"
      :descriptions="{ column: 1, bordered: true }"
      :fields="groupFields"
      :label-col="{ style: { width: '100px' } }"
      layout="inline"
      @finish="onGroupSave"
    >
      <template #footer>
        <a-descriptions-item label="操作">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-descriptions-item>
      </template>
    </c-pro-form>
  </a-drawer>

  <a-drawer
    v-model:open="editOpen"
    title="新建标签"
    placement="left"
    size="large"
    destroy-on-close
  >
    <c-pro-form
      v-model:value="editForm"
      :descriptions="{ column: 1, bordered: true }"
      :fields="editFields"
      :label-col="{ style: { width: '100px' } }"
      layout="inline"
      @finish="onEditSave"
    >
      <template #footer>
        <a-descriptions-item label="操作">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-descriptions-item>
      </template>
    </c-pro-form>
  </a-drawer>
</template>

<script setup lang="ts">
import type { DataManageModelPageView, RiskWarningGroupViewModel } from '@/api/models'
import type { FormField } from 'ch2-components/lib/pro-form/types'
import * as api from '@/api'
import { DataQueryParameterEditModel } from '@/api/models'
import { message } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import ListItem from './components/listItem.vue'

const { riskData, editOpen, editForm, onAdd, onEdit, onTagDel, editFields, onEditSave } = useTagsHook()

const { groupOpen, groupForm, groupFields, onGroupSave, onGroupAdd, onGroupDel } = useGroupHook()

const pageData = ref({
  current: 1,
  pageSize: 10,
  total: 0,
})

const currentId = ref('')

const rightSpinning = ref(false)

const articles = ref<DataManageModelPageView[]>([])

function pageChange(page: number, pageSize: number) {
  pageData.value.current = page
  pageData.value.pageSize = pageSize
  getArticlesList()
}

function getArticlesData(id: string) {
  currentId.value = id!
  pageData.value.current = 1
  getArticlesList()
}

async function getArticlesList() {
  rightSpinning.value = true
  const offset = (pageData.value.current - 1) * pageData.value.pageSize
  try {
    const res = await api.RiskWarning.FindDataByQuery_PostAsync({ id: currentId.value, offset, limit: pageData.value.pageSize })
    articles.value = res.items
    pageData.value.total = res.totals || 0
    rightSpinning.value = false
    console.log('%c [ res ]-75', 'font-size:13px; background:pink; color:#bf2c9f;', res)
  }
  catch (error: any) {
    message.error(error.message)
    rightSpinning.value = false
  }
}

async function getRiskData() {
  try {
    riskData.value = await api.RiskWarning.GetUserGroupsWithQueriesAsync()
  }
  catch (error: any) {
    message.error(error.message)
  }
}

const route = useRoute()

onMounted(() => {
  getRiskData()
  if (route.query?.tagId) {
    console.log('%c [ route.query?.tagId ]-196', 'font-size:13px; background:pink; color:#bf2c9f;', route.query?.tagId)
    currentId.value = route.query?.tagId
    getArticlesList()
  }
})

function useTagsHook() {
  const riskData = ref<RiskWarningGroupViewModel[]>([])

  const editOpen = ref(false)

  const editForm = ref({ queryTag: '', content: '', groupId: '' })

  function onAdd(row: RiskWarningGroupViewModel) {
    editForm.value.groupId = row.id!
    editOpen.value = true
  }

  function onEdit() {}

  async function onTagDel(id: string | GUID) {
    try {
      await api.RiskWarning.DeleteRiskWarning_GetAsync({ id })
      getRiskData()
      message.success('删除成功')
    }
    catch (error: any) {
      message.error(error.message)
    }
  }

  const editFields = ref<FormField<{
    queryTag?: string
    content?: string
  }>[]>([
    {
      label: '分组',
      prop: 'groupId',
      el: 'select',
      attrs: {
        api: api.RiskWarning.GetUserGroupsWithQueriesAsync,
        fieldNames: { label: 'name', value: 'id' },
        immediate: true,
      },
    },
    {
      label: '标签',
      prop: 'queryTag',
      el: 'input',
      attrs: {},
    },
    {
      label: '描述',
      prop: 'content',
      el: 'input',
      attrs: {},
    },
  ])

  async function onEditSave() {
    try {
      await api.RiskWarning.CreateRiskWarning_PostAsync(editForm.value, new DataQueryParameterEditModel())
      editOpen.value = false
      getRiskData()
      message.success('保存成功')
    }
    catch (error: any) {
      message.error(`保存失败${error.message}`)
    }
  }

  return { riskData, editOpen, editForm, onAdd, onEdit, onTagDel, editFields, onEditSave }
}

function useGroupHook() {
  const groupOpen = ref(false)

  const groupForm = ref({ groupName: '' })

  const groupFields = ref<FormField<{
    groupName?: string
  }>[]>([
    {
      label: '分组名称',
      prop: 'groupName',
      el: 'input',
      attrs: {},
    },
  ])

  function onGroupAdd() {
    groupOpen.value = true
  }

  async function onGroupSave() {
    try {
      await api.RiskWarning.CreateGroup_GetAsync(groupForm.value)
      message.success('保存成功')
      groupOpen.value = false
      getRiskData()
    }
    catch (error: any) {
      message.error(error.message)
    }
  }

  async function onGroupDel(id: string | GUID) {
    try {
      await api.RiskWarning.DeleteGroup_GetAsync({ groupId: id })
      getRiskData()
      message.success('删除成功')
    }
    catch (error: any) {
      message.error(error.message)
    }
  }

  return { groupOpen, groupForm, groupFields, onGroupSave, onGroupAdd, onGroupDel }
}
</script>
