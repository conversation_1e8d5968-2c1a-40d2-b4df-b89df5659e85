<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: luckymiaow
-->
<!-- 头像 -->
<template>
  <a-dropdown placement="bottomLeft" :arrow="{ pointAtCenter: true }">
    <a-avatar :size="44">
      <template #icon>
        <c-icon-user-outlined />
      </template>
    </a-avatar>
    <template #overlay>
      <a-menu>
        <a-menu-item key="0">
          <router-link :to="{ path: '/user-center/', query: { activeKey: '个人信息' }, replace: true }">
            <c-icon-user-outlined />
            个人信息
          </router-link>
        </a-menu-item>
        <a-menu-item key="1">
          <router-link :to="{ path: '/user-center/', query: { activeKey: '安全设置' }, replace: true }">
            <KeyOutlined />
            修改密码
          </router-link>
        </a-menu-item>
        <a-menu-divider />
        <a-menu-item key="3" @click="logout()">
          <ExportOutlined />
          退出登录
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import { ExportOutlined, KeyOutlined } from '@ant-design/icons-vue'
// import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
function logout() {
  router.push('/logoutReload?api=true')
}
</script>

<style lang="less" scoped>
:deep(.ant-dropdown-menu-item) {
  white-space: nowrap;
}
</style>
