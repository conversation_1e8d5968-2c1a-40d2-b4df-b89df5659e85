<!doctype html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
    <link rel="icon" href="favicon.ico" />
    <!-- <title><%= VITE_APP_TITLE %></title> -->
    <style>
      .empty-loading {
        background-color: rgba(170, 170, 170, 0.075);
        background-size: 100% 100%;
        -webkit-backdrop-filter: blur(4px);
        backdrop-filter: blur(4px);
        transition: opacity 0.3s;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        min-height: 380px;

        position: absolute;
        --speed-of-animation: 0.9s;
        --gap: 6px;
        --first-color: #4c86f9;
        --second-color: #49a84c;
        --third-color: #f6bb02;
        --fourth-color: #f6bb02;
        --fifth-color: #2196f3;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 6px;
        z-index: 2;
      }

      .empty-loading span {
        width: 4px;
        height: 50px;
        background: var(--first-color);
        animation: scale var(--speed-of-animation) ease-in-out infinite;
      }

      .empty-loading span:nth-child(2) {
        background: var(--second-color);
        animation-delay: -0.8s;
      }

      .empty-loading span:nth-child(3) {
        background: var(--third-color);
        animation-delay: -0.7s;
      }

      .empty-loading span:nth-child(4) {
        background: var(--fourth-color);
        animation-delay: -0.6s;
      }

      .empty-loading span:nth-child(5) {
        background: var(--fifth-color);
        animation-delay: -0.5s;
      }

      @keyframes scale {
        0%,
        40%,
        100% {
          transform: scaleY(0.05);
        }

        20% {
          transform: scaleY(1);
        }
      }
    </style>
    <script>
      setTimeout(() => {
        const loading = document.querySelector('#root-empty-loading')
        if (loading) {
          const button = document.createElement('button')
          button.innerText = '程序好像出错了，点击清除缓存重试'
          button.setAttribute('style', 'margin: auto; ')
          button.onclick = () => {
            sessionStorage.clear()
            localStorage.clear()
            location.reload()
          }
          loading.remove()
          document.querySelector('#app').appendChild(button)
        }
      }, 360000)
    </script>
  </head>

  <body>
    <div id="app">
      <div id="root-empty-loading" class="empty-loading">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
    <!-- built files will be auto injected -->
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
