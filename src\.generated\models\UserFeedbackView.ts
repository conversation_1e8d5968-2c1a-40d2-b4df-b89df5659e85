import { DataManageModel } from "./DataManageModel";
import { FeedbackType } from "./FeedbackType";
import { FeedbackStatus } from "./FeedbackStatus";
export class UserFeedbackView {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  feedbackUserId: GUID = "00000000-0000-0000-0000-000000000000";
  feedbackUserName?: string | null | undefined = null;
  /**智库文章*/
  dataManageModel?: DataManageModel | null | undefined = null;
  /**反馈类型*/
  type: FeedbackType = 0;
  /**问题描述*/
  description?: string | null | undefined = null;
  /**创建时间*/
  createdTime: Dayjs = dayjs();
  /**处理状态*/
  status: FeedbackStatus = 0;
  /**反馈回复*/
  responseId?: GUID = null;
  response?: string | null | undefined = null;
}
