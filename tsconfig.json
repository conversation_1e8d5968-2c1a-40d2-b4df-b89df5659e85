{
  "$schema": "https://json.schemastore.org/tsconfig",
  "extends": "@tsconfig/node-lts-strictest-esm/tsconfig.json",
  "compilerOptions": {
    "target": "ES2022",
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "lib": [
      "DOM", // 浏览器环境必需
      "DOM.Iterable", // 增强 DOM 类型支持
      "ES2022", // 对齐 target
      "ESNext.AsyncIterable" // 支持异步迭代
    ],
    // 模块系统配置
    "module": "ESNext",
    "moduleResolution": "Bundler", // 更适合 Vite/Webpack 场景
    "baseUrl": "./",
    "paths": {
      "@/*": [
        "src/*"
      ]
    },
    // 类型解析配置
    "types": [
      "vite/client",
      "vite-plugin-vue-layouts/client",
      "@vue/runtime-core", // Vue 3 类型增强
    ],
    // 严格性配置（平衡严格性和开发体验）
    "strict": true,
    "strictNullChecks": true,
    "exactOptionalPropertyTypes": false, // 允许更灵活的 optional 属性
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    // 输出配置（应用项目建议关闭声明文件）
    "declaration": false, // 关闭 .d.ts 生成（库项目才需要）
    "sourceMap": true, // 启用源码映射
    "outDir": "dist", // 编译输出目录
    // 模块互操作性
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true, // Vite 必需
    "resolveJsonModule": true,
    // 性能优化
    "skipLibCheck": true, // 跳过库类型检查
    "ignoreDeprecations": "5.0",
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "src/**/*.d.ts",
  ],
  "exclude": [
    "dist",
    "node_modules",
    "**/__mocks__/*",
    "**/*.test.ts" // 测试文件单独处理
  ],
  "ts-node": {
    "esm": true,
    "experimentalSpecifierResolution": "node" // 增强 ESM 解析
  }
}