import type { RouterItem } from '../types/interfaces'
import _dayjs from 'dayjs'
/*
 * @Description:
 * @Author: luckymiaow
 * @Date: 2022-04-15 16:15:09
 * @LastEditors: luckymiaow
 */
import { camelCase, cloneDeep, upperFirst } from 'lodash-es'

export function deepCopy<T>(origin: T): T {
  return cloneDeep(origin) // 把新的target 返回出去
}

/**
 *将viewModel转换为editModel ，会直接修改editModel
 *
 * @export
 * @param {object} viewModel
 * @param {object} editModel
 */
export function viewModelToEditModel<T>(viewModel: T & any, editModel: T): T {
  if (!viewModel)
    return editModel
  if (!editModel)
    return editModel
  for (const key in editModel) {
    if (viewModel[key] === undefined)
      continue
    editModel[key] = viewModel[key]
  }
  return editModel
}

/**
 * @description: 将小驼峰命名法的字符串转换成短横线命名法的字符串
 * @eg allowClear ----> allow-clear
 * @param {string} str
 * @return {*}
 */
export function getKebabCase(str: string) {
  return str.replace(/[A-Z]/g, (item: any) => {
    return `-${item.toLowerCase()}`
  })
}

/**
 * @description: InputPassword ----> c-input-passwprd
 * @param {string} str
 * @return {*}
 */
export function toCTagCase(str: any) {
  const arr = str.split('')

  const result = arr.map((item: any) => {
    if (item.toUpperCase() === item)
      return `-${item.toLowerCase()}`

    else return item
  })
  result.unshift('c')
  return result.join('')
}

/**
 * @description 处理 prop，当 prop 为多级嵌套时 ==> 返回最后一级 prop
 * @param {string} prop 当前 prop
 * @return string
 */
export function handleProp(prop: string) {
  const propArr = prop.split('.')
  if (propArr.length === 1)
    return prop
  return propArr[propArr.length - 1]
}

/**
 * @description 根据枚举列表查询当需要的数据（如果指定了 label 和 value 的 key值，会自动识别格式化）
 * @param {string} callValue 当前单元格值
 * @param {Array} enumData 字典列表
 * @param {{ label: string, value: string }} fieldNames 指定 label && value 的 key 值
 * @param {string} type 过滤类型（目前只有 tag）
 * @return string
 */
export function filterEnum(
  callValue: any,
  enumData: any[] | undefined,
  fieldNames?: { label: string, value: string },
  type?: string,
): string {
  const value = fieldNames?.value || 'value'
  const label = fieldNames?.label || 'label'
  let filterData: { [key: string]: any } = {}
  if (Array.isArray(enumData))
    filterData = enumData.find((item: any) => item[value] === callValue)
  if (type === 'tag')
    return filterData?.tagType ? filterData.tagType : ''
  return filterData ? filterData[label] : '--'
}

/**
 * @description 处理无数据情况
 * @param {string} callValue 需要处理的值
 * @return string
 */
export function formatValue(callValue: any) {
  // 如果当前值为数组,使用 / 拼接（根据需求自定义）
  if (Array.isArray(callValue))
    return callValue.length ? callValue.join(' / ') : '--'
  return callValue || '--'
}

/**
 * @description 处理 prop 为多级嵌套的情况(列如: prop:user.name)
 * @param {object} row 当前行数据
 * @param {string} prop 当前 prop
 * @return any
 */
export function handleRowAccordingToProp(row: { [key: string]: any }, prop: string) {
  if (!prop.includes('.'))
    return row[prop] || '--'
  prop.split('.').forEach(item => (row = row[item] || '--'))
  return row
}

/**
 * @description 获取浏览器默认语言
 * @return string
 */
export function getBrowserLang() {
  const browserLang = navigator.language ? navigator.language : navigator.browserLanguage
  let defaultBrowserLang = ''
  if (
    browserLang.toLowerCase() === 'cn'
    || browserLang.toLowerCase() === 'zh'
    || browserLang.toLowerCase() === 'zh-cn'
  ) {
    defaultBrowserLang = 'zh'
  }
  else {
    defaultBrowserLang = 'en'
  }

  return defaultBrowserLang
}

/**
 * @description 扁平化数组对象(主要用来处理路由菜单)
 * @param {Array} menuList 所有菜单列表
 * @return array
 */
export function getFlatArr(menuList: RouterItem[]) {
  const newMenuList: RouterItem[] = JSON.parse(JSON.stringify(menuList))
  return newMenuList.reduce((pre: RouterItem[], current: RouterItem) => {
    let flatArr = [...pre, current]
    if (current.children)
      flatArr = [...flatArr, ...getFlatArr(current.children)]
    return flatArr
  }, [])
}

/**
 * 方法：处理时间格式
 * @param date 日期时间
 * @returns 转格式后的日期时间字符串
 */
export function dateFormat(date: Date) {
  date = new Date(date)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  const formattedTime = `${year}-${month}-${day} ${hour}:${minute}:${second > 10 ? second : `0${second}`}`
  return formattedTime
}

export function getComponentName(path: string, prefix?: string): string {
  if (prefix)
    return upperFirst(prefix + upperFirst(camelCase(path)))
  return upperFirst(camelCase(path))
}

export function flattenTree<T extends { children?: T[] } & Record<any, any>>(tree: T[]) {
  let flatArray: T[] = []
  tree.forEach((node) => {
    flatArray.push(node)
    if (node.children)
      flatArray = flatArray.concat(flattenTree(node.children))
  })
  return flatArray
}

export function dayjs(date?: Dayjs) {
  return _dayjs(date)
}

export function joinFilePathById(id: string) {
  if (id?.startsWith('/'))
    return id
  const { token } = useUserStore()

  const path = `/files/${id}`

  return `${path}?access_token=${token}`
}

export function joinFilePathByPatn(path: string) {
  const { token } = useUserStore()

  return `${path}?access_token=${token}`
}

export async function loadImage(url: string) {
  try {
    // 使用 Fetch API 下载图片
    const response = await fetch(url)
    if (!response.ok)
      throw new Error('网络错误')

    // 将响应转换为 Blob
    const blob = await response.blob()

    // 创建临时链接
    const imgURL = URL.createObjectURL(blob)

    return imgURL
  }
  catch (error) {
    console.error('下载图片时出错:', error)
  }
}

/**
 * 处理富文本中的img标签
 * @param html
 */
export function replaceImgSrc(html: string): string {
  if (!html)
    return html
  // 替换 figure 为 div
  html = html.replace(/<img[^>]*>/gi, (match) => {
    match = match.replace(/style="[^"]+"/gi, '').replace(/style='[^']+'/gi, '')
    match = match.replace(/width="[^"]+"/gi, '').replace(/width='[^']+'/gi, '')
    match = match.replace(/height="[^"]+"/gi, '').replace(/height='[^']+'/gi, '')
    return match
  })
  // 修改所有style里的width属性为max-width:100%
  html = html.replace(/style="[^"]+"/gi, (match) => {
    match = match.replace(/width:[^;]+;/gi, 'max-width:100%;').replace(/width:[^;]+;/gi, 'max-width:100%;')
    return match
  })
  // 去掉<br/>标签
  html = html.replace(/<br[^>]*\/>/gi, '')
  // img标签添加style属性：max-width:100%;height:auto
  html = html.replace(/<img/gi, '<img style="max-width:100%;height:auto;display:block;margin:0px auto;"')

  // 替换 src 属性
  html = html.replace(/<img\s[^>]*src=["']([^"']*)["'][^>]*>/g, (match, oldSrc) => {
    const [url] = oldSrc.split('?')
    return match.replace(/src=["'][^"']*["']/g, `src="${joinFilePathByPatn(url)}"`)
  })

  html = html.replace(/<source\s[^>]*src=["']([^"']*)["'][^>]*>/g, (match, oldSrc) => {
    const [url] = oldSrc.split('?')
    return match.replace(/src=["'][^"']*["']/g, `src="${joinFilePathByPatn(url)}"`)
  })

  html = html.replace(/<a\s[^>]*href=["']([^"']*)["'][^>]*>/g, (match, oldSrc) => {
    const [url] = oldSrc.split('?')
    return match.replace(/src=["'][^"']*["']/g, `src="${joinFilePathByPatn(url)}"`)
  })

  return html
}

/**
 * 去除富文本中的token
 * @param html
 */
export function cleanEditorHtml(html: string) {
  const fn = (match: any, oldSrc: any, attrName: string = 'src') => {
    const [url] = oldSrc.split('?')

    const reg = new RegExp(`${attrName}=["'][^"']*["']`, 'g')

    return match.replace(reg, `${attrName}="${url}"`)
  }

  html = html.replace(/<img\s[^>]*src=["']([^"']*)["'][^>]*>/g, (match, oldSrc) => fn(match, oldSrc, 'src'))
  html = html.replace(/<source\s[^>]*src=["']([^"']*)["'][^>]*>/g, (match, oldSrc) => fn(match, oldSrc, 'src'))
  html = html.replace(/<a\s[^>]*href=["']([^"']*)["'][^>]*>/g, (match, oldSrc) => fn(match, oldSrc, 'href'))
  return html
}

export function parseUrlParams(url: string) {
  const params = {} as any

  // 查找查询字符串部分
  const queryString = url.split('?')[1]
  if (!queryString)
    return params

  // 拆分查询字符串为键值对
  const pairs = queryString.split('&')

  // 遍历每个键值对，将它们加入到 params 对象中
  pairs.forEach((pair: any) => {
    const [key, value] = pair.split('=')
    if (key) {
      params[decodeURIComponent(key)] = decodeURIComponent(value || '')
    }
  })
  return params
}

export function showSystemNotification(title: string, body: string) {
  if ('Notification' in window) {
    if (Notification.permission === 'granted') {
      void new Notification(title, { body })
    }
    else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then((permission) => {
        if (permission === 'granted') {
          void new Notification(title, { body })
        }
      })
    }
  }
}
