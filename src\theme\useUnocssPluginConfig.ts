/*
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: luckymiaow
 */

import type { GlobalToken } from 'ant-design-vue/es/theme'
import type { ComputedRef } from 'vue'
import { useAppStore } from '@/stores'
import { nextTick, onMounted, onUnmounted, watch } from 'vue'
import { updateCss } from './utils'

/**
 * pinia 驱动修改主题，不可逆向
 */
export function useUnocssPluginConfig(token: ComputedRef<GlobalToken>) {
  const app = useAppStore()
  const themeMedia = window.matchMedia('(prefers-color-scheme: light)')

  let isThemeMedia = false

  const updateTheme = () => {
    nextTick(() => {
      updateCss(token.value)
    })
  }

  watch(token, updateTheme, { immediate: true })

  const monitoringSystem = (e: any) => {
    app.convertThemeColors(!e.matches)
  }

  const changeMonitoringSystem = () => {
    if (app.themeConfig.isDarkBySystem === false && isThemeMedia) {
      themeMedia.removeEventListener('change', monitoringSystem)
      isThemeMedia = false
    }
    else if (!isThemeMedia) {
      themeMedia.addEventListener('change', monitoringSystem)
      isThemeMedia = true
    }
  }

  watch(() => app.$state.themeConfig.isDarkBySystem, (isDarkBySystem, oldIsDarkBySystem) => {
    if (isDarkBySystem !== oldIsDarkBySystem)
      changeMonitoringSystem()
  })

  onUnmounted(() => {
    themeMedia.removeEventListener('change', monitoringSystem)
  })

  onMounted(() => {
    if (app.themeConfig.isDarkBySystem) {
      themeMedia.addEventListener('change', monitoringSystem)
      isThemeMedia = true
    }
  })
}
