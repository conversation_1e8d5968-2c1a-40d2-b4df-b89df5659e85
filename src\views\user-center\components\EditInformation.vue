<!--
 * @Author: <PERSON><PERSON>er
 * @Date: 2022-03-29 16:06:33
 * @LastEditors: 景 彡
 * @LastEditTime: 2024-11-13 17:14:11
 * @Description:
-->
<template>
  <div id="editInformation">
    <c-form :model="userForms" :label-col="{ span: 3 }" :wrapper-col="{ span: 7 }">
      <c-form-item label="用户名" name="userName" :="validateInfos.userName" disabled>
        <c-input v-model:value.trim="userForms.userName" placeholder="请输入用户名" disabled />
      </c-form-item>
      <c-form-item label="姓名" name="name" :="validateInfos.name">
        <c-input v-model:value.trim="userForms.name" placeholder="请输入姓名" />
      </c-form-item>
      <c-form-item label="电子邮箱" name="email" :="validateInfos.email">
        <c-input v-model:value.trim="userForms.email" placeholder="请输入电子邮箱" />
      </c-form-item>
      <c-form-item label="联系电话" name="phoneNumber" :="validateInfos.phoneNumber">
        <c-input v-model:value.trim="userForms.phoneNumber" placeholder="请输入联系电话" />
      </c-form-item>
      <c-form-item :wrapper-col="{ span: 15, offset: 3 }">
        <a-button type="primary" @click="editPersonal">
          <c-icon-save-outlined />
          保存
        </a-button>
      </c-form-item>
    </c-form>
  </div>
</template>

<script lang="ts">
import * as api from '@/api'
import * as models from '@/api/models'
import { Form, message } from 'ant-design-vue'
import { nextTick, onMounted, reactive, ref } from 'vue'

export default {
  setup() {
    /** 表单验证规则 */
    const rules = reactive({
      userName: [{ required: true, message: '请输入用户名!', trigger: 'change' }],
      // name: [{ required: true, message: '请输入姓名!', trigger: 'change' }],
      // phoneNumber: [
      //   {
      //     required: true,
      //     validator: FormValidator('isLandlinePhoneReg'),
      //     trigger: ['blur', 'change'],
      //   },
      // ],
      // email: [
      //   {
      //     required: true,
      //     validator: FormValidator('emailReg'),
      //     trigger: ['blur', 'change'],
      //   },
      // ],
    })

    // 实例化用户对象信息
    const userForms = ref(new models.UserEditModel())

    //  表单校验器
    const { useForm } = Form
    //  选择采用此表单校验器的表单ref引用对象、校验规则
    const { resetFields, validate, validateInfos } = useForm(
      userForms,
      rules,
      //  {
      //   onValidate: (...args) => console.log(...args),
      // }
    )

    // 编辑用户（姓名，电子邮箱，联系电话）
    async function editPersonal() {
      await validate()
        .then(async () => {
          try {
            // 传入用户id及相关用户表单信息
            await api.CurrentUser.EditUserModel_PostAsync(userForms.value)
            message.success('保存信息成功')
          }
          catch (err: any) {
            message.error(err.toString())
          }
        })
        .catch(() => {
          throw new Error('字段校验未通过')
        })
    }

    // 查询当前用户
    async function getDetail() {
      try {
        // 查询并获取当前用户信息
        const data = await api.CurrentUser.Me_GetAsync()
        // 绑定查询接口获取用户信息
        userForms.value = data
      }
      catch (err: any) {
        message.error(err.toString())
      }
    }

    // 初始化执行
    onMounted(async () => {
      await nextTick()
      // 执行查询当前用户信息操作
      await getDetail()
    })

    return { userForms, editPersonal, resetFields, validate, validateInfos }
  },
}
</script>

<style lang="less" scoped>
#editInformation {
  padding: 20px;
  margin: 0 auto;
  background: @colorBgContainer;
}
</style>
