stages:
  - build
  - deploy

variables:
  DEV_SSH_USER_AND_HOST: $DEV_DEPLOY_SSH_USER@$DEV_DEPLOY_SSH_HOST
  IMAGE_TAG: $CI_REGISTRY/$CI_REGISTRY_NAMESPACE/$CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME

build images:
  stage: build
  image: docker:stable
  tags:
    - linux
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build -t $IMAGE_TAG --build-arg SOURCE_URL=http://hub-mirror.c.163.com .
    - docker push $IMAGE_TAG
  only:
    - publish

deploy prod on ali-fastoa01:
  stage: deploy
  image: kroniak/ssh-client
  tags:
    - ali-master-builder
  script:
    - eval $(ssh-agent -s)
    - echo "$PUBLISH_PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -p $PUBLISH_HOST2_PORT  $NN_PUBLISH_HOST1 > ~/.ssh/known_hosts
    - ssh -p  $PUBLISH_HOST2_PORT $NN_PUBLISH_USER@$NN_PUBLISH_HOST1 "cd $PUBLISH_DEPLOY_PATH && docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY && docker compose pull && docker compose up -d --force-recreate"
  only:
    - publish
