/* 数据管理模型 */
export class DataManageModel {
  /* 数据库唯一编号 */
  id: string = "";

  /* 所属数据集ID */
  dataSetId: string = "";

  /* AI分析结果 */
  ai: string | null | undefined = null;

  /* 领域分类 */
  domain: string[] | null | undefined = [];

  /* 情感分析 */
  emotion: string | null | undefined = null;

  /* 链接地址 */
  link: string | null | undefined = null;

  /* 地区 */
  region: string[] | null | undefined = [];

  /* 作者 */
  author: string | null | undefined = null;

  /* 来源 */
  source: string | null | undefined = null;

  /* 时间 */
  time: string | null | undefined = null;

  /* 标题 */
  title: string = "";

  /* 中文标题 */
  titleCn: string | null | undefined = null;

  /* 中文内容 */
  contentCn: string | null | undefined = null;

  /* 内容 */
  content: string | null | undefined = null;

  /* 是否已删除 */
  isDeleted: boolean = false;
}
