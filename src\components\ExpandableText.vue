<template>
  <div>
    <!-- 内容容器 -->
    <div
      ref="textWrapper"
      :style="!expanded ? { maxHeight: `${lineHeight * maxLines}rem`, overflow: 'hidden' } : {}"
      class="transition-all"
    >
      <div
        v-for="(p, idx) in paragraphs"
        :key="idx"
        class="text-gray-600 leading-6"
      >
        {{ p }}
      </div>
    </div>

    <!-- 展开/收起按钮 -->
    <div
      v-if="showToggle"
      class="mt-1 cursor-pointer select-none pr-4 text-right text-blue-500"
      @click="expanded = !expanded"
    >
      {{ expanded ? '收起' : '展开全部' }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue'

const props = defineProps<{
  content: string
  maxLines?: number
}>()

const expanded = ref(false)
const showToggle = ref(false)
const textWrapper = ref<HTMLElement | null>(null)
const lineHeight = 1.5
const maxLines = props.maxLines ?? 2

const paragraphs = computed(() =>
  props.content?.split('\n').filter(line => line.trim() !== ''),
)

function checkOverflow() {
  nextTick(() => {
    const el = textWrapper.value
    if (el) {
      const maxHeight = lineHeight * maxLines * 16 // 1rem = 16px
      showToggle.value = el.scrollHeight > maxHeight + 2
    }
  })
}

onMounted(checkOverflow)
watch(() => props.content, checkOverflow)
</script>
