import type { Ref } from 'vue'
import { ref } from 'vue'

interface FetchOption<T, A extends any[]> {
  fn: (...args: A) => Promise<T>
  options?: {
    default?: T
    callback?: (data: T) => (any | Promise<any>)
    immediate?: boolean
    params?: A
    error?: (error: any) => void
  }
}

export type LoadingType<T, A extends any[]> = [
  FetchOption<T, A>['fn'],
  Ref<boolean>,
  Ref<T | undefined>,
] & {
  run: FetchOption<T, A>['fn']
  spinner: Ref<boolean>
  data: Ref<T | undefined>
}

export function useLoading<T, A extends any[]>(
  fn: FetchOption<T, A>['fn'],
  options?: FetchOption<T, A>['options'],
): LoadingType<T, A> {
  const spinner = ref<boolean>(false)

  const data = ref<T | undefined>(options?.default)

  async function run(...args: A): Promise<T | null> {
    try {
      spinner.value = true
      const res = await fn(...args)
      spinner.value = false
      data.value = res as any
      options?.callback && await options.callback(res)
      return res
    }
    catch (err) {
      spinner.value = false
      if (options?.error)
        options.error(err)
      else
        throw err

      return null
    }
  }

  onMounted(() => {
    data.value = options?.default
    if (options?.immediate) {
      if (options.params)
        run(...options.params as any)
      else
        run(...[] as never)
    }
  })

  return makeDestructurable(
    { run, spinner, data } as const,
    [run, spinner, data] as const,
  ) as any
}
