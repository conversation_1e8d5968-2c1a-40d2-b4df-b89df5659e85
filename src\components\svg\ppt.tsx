import type { CustomIconComponentProps } from '@ant-design/icons-vue/lib/components/Icon'

function icon(props: CustomIconComponentProps) {
  return (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 56.054 66.35" class="ckbox-doc-icon__icon">
      <g transform="translate(-236.224 -502.325)">
        <g transform="translate(192.469 470.079)">
          <path d="M82.585,33.746H53.6a8.342,8.342,0,0,0-8.342,8.342V88.754A8.342,8.342,0,0,0,53.6,97.1H89.966a8.342,8.342,0,0,0,8.342-8.342V49.469Z" fill="#fff" stroke="#c64122" stroke-miterlimit="10" stroke-width="3"></path>
          <path d="M204.77,33.746v9.866a7.156,7.156,0,0,0,7.156,7.156h9.866Z" transform="translate(-123.189)" fill="#c64122"></path>
        </g>
        <path d="M1.767,0a.456.456,0,0,1-.332-.143.456.456,0,0,1-.143-.332V-12.806a.5.5,0,0,1,.133-.352.447.447,0,0,1,.342-.142H7.144a6.059,6.059,0,0,1,3.876,1.121,3.953,3.953,0,0,1,1.406,3.287,3.818,3.818,0,0,1-1.406,3.24A6.25,6.25,0,0,1,7.144-4.579H4.978v4.1a.472.472,0,0,1-.133.332A.447.447,0,0,1,4.5,0ZM7.049-7.3A1.747,1.747,0,0,0,8.275-7.7a1.554,1.554,0,0,0,.446-1.206,1.726,1.726,0,0,0-.408-1.2,1.613,1.613,0,0,0-1.264-.456H4.921V-7.3Z" transform="translate(245 527)" fill="#c64122"></path>
        <g transform="translate(4 9)">
          <rect width="21.546" height="4.463" rx="2.232" transform="translate(249.483 542.098)" fill="#c64122"></rect>
          <path d="M0,10V.03C.245.01.491,0,.74,0A9.443,9.443,0,0,1,10,9.615q0,.193-.008.385Z" transform="translate(261.791 518.347)" fill="#c64122" opacity="0.42"></path>
          <path d="M10.5,21A10.519,10.519,0,0,1,2.8,3.33,10.461,10.461,0,0,1,9.664,0V10.9H21A10.51,10.51,0,0,1,10.5,21Z" transform="translate(250 519.053)" fill="#c64122"></path>
        </g>
      </g>
    </svg>
  )
}

export default {
  icon,
  color: '#F9EBE8',
}
