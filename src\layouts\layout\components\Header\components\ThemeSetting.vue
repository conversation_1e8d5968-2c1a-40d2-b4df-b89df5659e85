<template>
  <div>
    <SkinOutlined @click="visibleHandle()" />
  </div>
  <a-drawer v-model:open="visible" title="主题设置" placement="right" :closable="false" destroy-on-close>
    <a-divider>
      <CopyOutlined @click="copyConfig" />
      布局切换
    </a-divider>

    <a-row>
      <a-col span="24">
        <div class="ant-pro-setting-drawer-block-checbox">
          <div class="ant-pro-setting-drawer-block-checbox-item" @click="() => (themeConfig.layout = 'transverse')">
            <img src="https://gw.alipayobjects.com/zos/antfincdn/XwFOFbLkSM/LCkqqYNmvBEbokSDscrm.svg" alt="sidemenu">
            <c-icon-check-outlined v-if="themeConfig.layout === 'transverse'" class="check" />
          </div>
          <div class="ant-pro-setting-drawer-block-checbox-item" @click="() => (themeConfig.layout = 'vertical')">
            <img src="https://gw.alipayobjects.com/zos/antfincdn/URETY8%24STp/KDNDBbriJhLwuqMoxcAr.svg" alt="topmenu">
            <c-icon-check-outlined v-if="themeConfig.layout === 'vertical'" class="check" />
          </div>
        </div>
      </a-col>
    </a-row>
    <a-row>
      <a-col span="8" offset="8">
        <a-tooltip />
      </a-col>
    </a-row>
    <a-divider>
      <ClearOutlined />
      全局主题
    </a-divider>
    <div class="global-theme">
      <a-row>
        <a-col span="8" :style="textStyle">
          夜间模式
        </a-col>
        <a-col span="4" offset="12">
          <a-switch :checked="themeConfig.isDark" @change="(v) => updateThemeMode(v as boolean)">
            <template #unCheckedChildren>
              <div i-carbon:sun icon />
            </template>
            <template #checkedChildren>
              <div i-carbon:moon icon />
            </template>
          </a-switch>
        </a-col>
      </a-row>
      <a-row>
        <a-col span="10" :style="textStyle">
          夜间模式跟随系统
        </a-col>
        <a-col span="4" offset="10">
          <a-switch v-model:checked="themeConfig.isDarkBySystem" @change="updateIsDarkBySystem" />
        </a-col>
      </a-row>
      <a-row>
        <a-col span="24">
          <div class="theme-color">
            <div
              v-for="(item, key) in themeConfig.isDark ? darkThemes : themes" :key="key" class="theme-color-block"
              :class="{ 'theme-color-active': key === themeConfig.themeColor.colorPrimary }"
              :style="{ 'background-color': key }" @click="updateTheme(item)"
            >
              <c-icon-check-outlined v-if="key === themeConfig.themeColor.colorPrimary" />
            </div>
          </div>
        </a-col>
      </a-row>

      <a-row>
        <a-col span="8" :style="textStyle">
          自定义
        </a-col>
      </a-row>
      <a-row>
        <a-col span="24">
          <div class="custom-color flex flex-wrap gap10px">
            <div v-for="_, key in themeConfig.themeColor" :key="key" class="j items-cente w[100px] flex gap4px">
              <input
                class="theme-color-block" :value="themeConfig.themeColor[key]"
                type="color" @input="(v) => updateThemeColor(key, v.target.value)"
              >
              <span :style="textStyle"> {{ wordColor(key) }}</span>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <a-divider>
      <ToolOutlined />
      界面设置
    </a-divider>
    <div class="global-theme">
      <a-row>
        <a-col span="8" :style="textStyle">
          暗黑菜单
        </a-col>
        <a-col span="4" offset="12">
          <a-switch v-model:checked="themeConfig.darkMenu" />
        </a-col>
      </a-row>
      <a-row>
        <a-col span="8" :style="textStyle">
          折叠菜单
        </a-col>
        <a-col span="4" offset="12">
          <a-switch v-model:checked="themeConfig.isCollapse" />
        </a-col>
      </a-row>
      <a-row>
        <a-col span="8" :style="textStyle">
          面包屑
        </a-col>
        <a-col span="4" offset="12">
          <a-switch v-model:checked="themeConfig.breadcrumb" />
        </a-col>
      </a-row>
      <!--   <a-row>
        <a-col span="8">面包屑菜单</a-col>
        <a-col span="4" offset="12"> <a-switch /></a-col>
      </a-row> -->
      <a-row>
        <a-col span="8" :style="textStyle">
          标签栏
        </a-col>
        <a-col span="4" offset="12">
          <a-switch v-model:checked="themeConfig.tabs" />
        </a-col>
      </a-row>
      <a-row>
        <a-col span="8" :style="textStyle">
          标签栏图标
        </a-col>
        <a-col span="4" offset="12">
          <a-switch v-model:checked="themeConfig.tabsIcon" />
        </a-col>
      </a-row>
      <a-row>
        <a-col span="8" :style="textStyle">
          页脚
        </a-col>
        <a-col span="4" offset="12">
          <a-switch v-model:checked="themeConfig.footer" />
        </a-col>
      </a-row>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import type { ThemeColor, ThemeColorKey } from '@/types/interfaces'
import { useAppStore } from '@/stores'
import { darkThemes, themes, useToken, wordColor } from '@/theme'
// import { ref, reactive, onMounted } from 'vue'
import { ClearOutlined, CopyOutlined, SkinOutlined, ToolOutlined } from '@ant-design/icons-vue'
import { useThrottleFn } from '@vueuse/core'
import { message } from 'ant-design-vue'
import { ref } from 'vue'

const { textStyle } = useToken()

const { themeConfig, convertThemeColors, updateIsDarkBySystem } = useAppStore()

function updateTheme(item: ThemeColor) {
  themeConfig.themeColor = { ...item }
}

function updateThemeMode(v: boolean) {
  themeConfig.isDarkBySystem = false
  convertThemeColors(v)
}

const visible = ref<boolean>(false)

function visibleHandle() {
  visible.value = !visible.value
}

function copyConfig() {
  navigator.clipboard
    .writeText(JSON.stringify(themeConfig))
    .then(() => {
      message.success('配置复制成功')
    })
    .catch((error) => {
      message.error('复制到剪贴板失败:', error)
    })
}

const updateThemeColor = useThrottleFn((key: ThemeColorKey, v: string) => {
  themeConfig.themeColor[key] = v
}, 1000)
</script>

<style lang="less">
.ant-pro-setting-drawer-block-checbox {
  display: flex;

  .ant-pro-setting-drawer-block-checbox-item {
    position: relative;
    margin-right: 10px;
    cursor: pointer;

    .check {
      position: absolute;
      right: 10px;
      bottom: 10px;
      font-weight: 6000;
      // color: @primary-color;
    }
  }
}

.global-theme {
  .ant-row {
    margin-top: 20px;
  }

  .theme-color {
    display: flex;

    .theme-color-block {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      font-weight: 700;
      text-align: center;
      border-radius: 2px;
      cursor: pointer;
    }

    .theme-color-active {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
    }
  }
}

.custom-color {
  input {
    border: 0;
    padding: 0;
    cursor: pointer;
  }
}
</style>
@/theme/theme@/theme/useUnocssConfig
