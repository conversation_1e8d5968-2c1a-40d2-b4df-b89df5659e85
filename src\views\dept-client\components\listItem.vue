<template>
  <article class="mb-4 flex overflow-hidden rounded bg-white shadow">
    <div class="relative flex-1 flex-1 border-r border-gray-200 p-4">
      <!-- <div class="mb-2 flex justify-between">
        <div class="flex gap-1">
          <span class="rounded bg-blue-50 px-2 py-0.5 text-xs text-blue-700">涉华</span>
          <span class="rounded bg-red-50 px-2 py-0.5 text-xs text-red-600">负面</span>
        </div>
        <div>
          <span class="rounded bg-red-50 px-2 py-0.5 text-xs text-red-600">未读</span>
        </div>
      </div> -->
      <div class="absolute right-1 top-1">
        <span v-if="!article.isRead" class="rounded bg-red-50 px-2 py-0.5 text-xs text-red-600">未读</span>
      </div>
      <div class="mb-2 cursor-pointer hover:(c-primary)" @click="toDetail">
        <h3 class="mb-1 text-base"><span class="c-primary">[原文]</span> {{ article.title }}</h3>
        <h3 class="mb-1 text-base"><span class="c-primary">[翻译] </span>{{ article.titleCn }}</h3>
      </div>
      <div class="mb-4 text-sm text-gray-600">
        <div class="mb-1">
          <span class="text-gray-400">涉及国家：</span>
          <span>{{ article.region?.join(' ') }}</span>
        </div>
        <div class="mb-1">
          <span class="text-gray-400">涉及领域：</span>
          <span>{{ article.domain?.join(' ') }}</span>
        </div>
        <div>
          <span class="mr-2">{{ dateTime(article.time) }}</span>
          <span>{{ article.source }}</span>
        </div>
      </div>
      <div class="flex items-center gap-2">
        <a-button type="primary" @click="toDetail">查看详情</a-button>
        <a-button v-if="!article.isFavorite" type="primary" ghost :icon="h(StarOutlined)" @click="onCollect(article.id)">收藏</a-button>
        <C2SecondButton v-else>已收藏</C2SecondButton>
        <!-- <a-tag :bordered="false" color="success">中美关系</a-tag> -->
      </div>
    </div>
    <div class="min-w-400px flex-1 bg-gray-50 p-4">
      <div class="mb-2 flex items-center gap-1">
        <i class="text-purple-600">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z" /><path d="M19 10v2a7 7 0 0 1-14 0v-2" /><line x1="12" y1="19" x2="12" y2="22" /></svg>
        </i>
        <span class="text-sm text-purple-600 font-medium">AI翻译及提取的摘要内容</span>
      </div>
      <p class="text-sm text-gray-700 leading-normal">
        <c-truncated-text :text="article.ai" :max-lines="5" type="launch" />
      </p>
    </div>
  </article>

  <a-drawer
    v-model:open="open"
    title="添加到收藏"
    placement="left"
    size="large"
  >
    <c-pro-form
      v-model:value="form"
      :descriptions="{ column: 1, bordered: true }"
      :fields="fileds"
      :label-col="{ style: { width: '100px' } }"
      layout="inline"
      @finish="onSave"
    >
      <template #footer>
        <a-descriptions-item label="操作">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-descriptions-item>
      </template>
      <template #folderId>
        <a-descriptions-item label="收藏夹">
          <a-tree-select
            v-model:value="form.folderId"
            :tree-data="treeData"
            :show-all-levels="true"
            tree-default-expand-all
          />
        </a-descriptions-item>
      </template>
    </c-pro-form>
  </a-drawer>
</template>

<script lang='ts' setup>
import type { DataManageModelPageView, FolderWithCountDto, UserTagRes } from '@/api/models'
import type { Guid } from '@/utils/GUID'
import type { TreeProps } from 'ant-design-vue'
import type { DataNode } from 'ant-design-vue/es/tree'
import type { FormField } from 'ch2-components/types/pro-form/types'
import * as api from '@/api'
import { FolderType } from '@/api/models'
import { StarOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'

const props = defineProps<{
  article: DataManageModelPageView
}>()

const emit = defineEmits<{ (e: 'onCollectSuccess'): void }>()

const router = useRouter()

const { open, form, fileds, treeData, onCollect, onSave } = collectHook()

async function toDetail() {
  if (!props.article.isRead) {
    try {
      await api.DataManageModels.MarkAsRead_GetAsync({ dataId: props.article.id! })
    }
    catch (error: any) {
      message.error(error.message)
    }
  }

  const routePath = router.resolve({
    path: '/dept-client/detail',
    query: { id: props.article.id },
  }).href
  window.open(routePath, '_blank')

  // router.push({ path: '/dept-client/detail', query: { id: props.article.id } })
}

function collectHook() {
  const open = ref(false)

  const form = ref({ folderId: '', dataManageModelId: '', tags: [] })

  const fileds = ref<FormField<{
    name?: string
    tag?: UserTagRes[]
  }>[]>([
    {
      label: '收藏夹',
      prop: 'folderId',
      el: 'input',
      attrs: {},
    },
    {
      label: '标记',
      prop: 'tag',
      el: 'select',
      attrs: {
        api: api.UserTags.FindByUser_GetAsync,
        fieldNames: { label: 'name', value: 'id' },
        mode: 'multiple',
        onChange: (_val, option) => {
          form.value.tags = option.map((item: any) => ({ id: item.value, name: item.label }))
        },
      },
    },
  ])

  const treeData = ref<TreeProps['treeData']>([])

  function transformDepartmentsToTreeData(data: FolderWithCountDto[]): TreeProps['treeData'] {
    return data.map((item: FolderWithCountDto) => {
      const node: DataNode = {
        title: item.name || '',
        key: item.id!,
        value: item.id,
        disabled: item.type === FolderType.目录,
        children: [],
      }
      node!.children = item.children ? transformDepartmentsToTreeData(item.children) : undefined
      return node
    })
  }

  async function getCollectData() {
    try {
      const res = await api.FavoriteArticles.GetAllFoldersWithArticleCountAsync()
      treeData.value = transformDepartmentsToTreeData(res)
    }
    catch (error: any) {
      message.error(error.message)
    }
  }

  function onCollect(id: string | Guid) {
    form.value.dataManageModelId = id
    open.value = true
    getCollectData()
  }

  async function onSave() {
    console.log('%c [ form.value.tags ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', form.value.tags)
    try {
      await api.FavoriteArticles.AddData_PostAsync({ folderId: form.value.folderId, dataManageModelId: form.value.dataManageModelId }, form.value.tags)
      message.success('收藏成功')
      emit('onCollectSuccess')
      open.value = false
    }
    catch (error: any) {
      message.error(`收藏失败${error.message}`)
    }
  }

  return { open, form, fileds, treeData, onCollect, onSave }
}
</script>
