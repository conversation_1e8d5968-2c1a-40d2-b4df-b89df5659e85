/**图表实质映射的字段配置
[{ "FieldMapName": "XField", "DataFieldName": "month" },
{ "FieldMapName": "SeriesFields", "Children": [{ "FieldMapName": "国家", "DataFieldName": "name" }, { "FieldMapName": "GDP", "DataFieldName": "value" }] },]*/
export class ChartConfig {
  /**对应FieldMapping的Name属性，表示配置字段的名称*/
  fieldMapName?: string | null | undefined = null;
  /**对应ChartDataset中FieldsJson的name属性，表示数据集字段的名称
 普通字段用字符串，数组型字段（ObjectArray）用子配置列表*/
  dataFieldName?: string | null | undefined = null;
  children?: ChartConfig[] | null | undefined = [];
  /**默认值，可以用在样式配置中（实质的值，不需要映射*/
  defaultValue?: any | null | undefined = null;
}
