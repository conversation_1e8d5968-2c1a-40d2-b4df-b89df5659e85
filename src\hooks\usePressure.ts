/*
 * @Author: Ztq
 * @Date: 2023-06-05 09:23:51
 * @LastEditors: Ztq
 * @LastEditTime: 2023-06-05 11:41:01
 * @Description:
 * @FilePath: \ch2-template-vue\src\views\components\chart\pressure.ts
 */
import * as echarts from 'echarts'
import { nextTick, onMounted, shallowRef } from 'vue'

export function usePressure(data: { name: string, value: number }, config?: { auto?: boolean, option: object }) {
  const domRef = shallowRef()

  const defaultOption = {
    tooltip: {
      formatter: '{a} <br/>{b} : {c}%',
    },
    series: [
      {
        name: 'Pressure',
        type: 'gauge',
        progress: {
          show: true,
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}',
        },
        data: [data],
      },
    ],
    ...config?.option,
  }

  const initMap = (dom: HTMLElement, option: any) => {
    if (dom === null)
      return

    const chartObj: echarts.ECharts | null = echarts.init(dom)
    option && chartObj.setOption(option)

    return chartObj
  }

  onMounted(async () => {
    await nextTick()
    if (config?.auto !== false && domRef && domRef.value)
      initMap(domRef.value, defaultOption)
  })

  return { initMap, defaultOption, domRef }
}
