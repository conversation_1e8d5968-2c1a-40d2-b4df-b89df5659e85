<template>
  <div class="flex">
    <a-card title="角色列表" style="height: 85vh">
      <template #extra><a @click="addRole">新增角色</a></template>
      <a-input v-model:value="searchValue" style="margin-bottom: 8px" placeholder="请输入角色" @change="getRoles" />
      <a-directory-tree
        v-model:selected-keys="selectedKeys" v-model:checked-keys="checkedKeys" v-model:expanded-keys="expandedKeys"
        :multiple="false" :tree-data="treeData" @select="selectRole"
      >
        <template #title="{ title }">
          <a-dropdown :trigger="['contextmenu']">
            <span>{{ title }}</span>
            <template #overlay>
              <a-menu>
                <a-menu-item key="1" @click="editRole(title)">编辑角色</a-menu-item>
                <a-menu-item key="2" @click="delRole(title)"><a style="color: red">删除用户</a></a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </a-directory-tree>
    </a-card>
    <Details ref="detailsRef" class="w0 flex-1" />
  </div>

  <a-modal v-model:open="addModelVisible" title="添加角色" @ok="addRoleHandleOk">
    <a-input v-model:value="addNewRoleName" style="margin-bottom: 20px; margin-top: 20px" placeholder="请输入角色名称" />
  </a-modal>
  <a-modal v-model:open="editModelVisible" title="编辑角色" @ok="editRoleHandleOk">
    <a-input v-model:value="editNewRoleName" style="margin-bottom: 20px; margin-top: 20px" placeholder="请输入角色名称" />
  </a-modal>
</template>

<script setup lang="ts">
import type { Role } from '@/api/models'
import type { TreeProps } from 'ant-design-vue'
import * as api from '@/api'
import { message } from 'ant-design-vue'
import Details from './components/details.vue'

definePage({
  meta: {
    title: '角色管理',
    local: true,
  },
})
const detailsRef = useTemplateRef('detailsRef')
const addModelVisible = ref<boolean>(false)
const editModelVisible = ref<boolean>(false)
const editRoleId = ref<string>('')
const addNewRoleName = ref<string>('')
const editNewRoleName = ref<string>('')
const searchValue = ref()
const selectedKeys = ref<string[]>(['0'])
const expandedKeys = ref<string[]>(['0'])
const checkedKeys = ref<string[]>([])
const treeData = ref<TreeProps['treeData'] & { data?: Role }>([
  {
    title: '全部',
    key: '0',
    children: [],
  },
])

/**
 * 获取角色
 */
async function getRoles() {
  await api.RolesManage.RoleAll_PostAsync({ roleName: searchValue.value }).then(
    (res) => {
      if (res) {
        treeData.value[0]!.children = []
        res.forEach((p) => {
          treeData.value[0]!.children?.push({
            title: p.name,
            key: p.id as string,
            data: p,
          })
        })
      }
    },
  )
}

/**
 * 点击添加角色按钮
 */
function addRole() {
  addModelVisible.value = true
  addNewRoleName.value = ''
}

async function editRole(role: string) {
  editModelVisible.value = true
  editNewRoleName.value = role
  await api.RolesManage.RoleAll_PostAsync({ roleName: editNewRoleName.value }).then(async (res) => {
    if (res) {
      editRoleId.value = res[0]?.id as string
    }
  })
}

/**
 * 添加角色确认按钮
 */
async function addRoleHandleOk() {
  if (!addNewRoleName.value) {
    message.error('角色名称不可为空')
    return
  }
  await api.RolesManage.CreateRole_PostAsync({
    roleName: addNewRoleName.value,
  }).then((res) => {
    if (res) {
      addModelVisible.value = false
      getRoles()
    }
  })
}

async function editRoleHandleOk() {
  await api.RolesManage.ModifyRole_PostAsync({ roleId: editRoleId.value, roleName: editNewRoleName.value }).then(async (m) => {
    if (m) {
      message.success('编辑成功')
      editModelVisible.value = false
      getRoles()
    }
  })
}

async function delRole(role: string) {
  await api.RolesManage.RoleAll_PostAsync({ roleName: role }).then(async (res) => {
    if (res) {
      await api.RolesManage.Delete_PostAsync({ roleId: res[0]?.id as string }).then(async (m) => {
        if (m) {
          message.success('删除成功')
          getRoles()
        }
      })
    }
  })
}

function selectRole(_selectedKeys: number[], e: any) {
  detailsRef.value!.acceptParams({
    roleName: e.node.title,
    visible: false,
    title: '',
    currentId: selectedKeys.value[0] as unknown as string,
    type: '',
    menuConfig: e.node.dataRef.data?.menu,
    callback: getRoles,
  })
}

onMounted(async () => {
  getRoles()
})
</script>

<style scoped></style>
