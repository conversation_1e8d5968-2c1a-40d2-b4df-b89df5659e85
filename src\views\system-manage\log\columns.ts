import type { ColumnProps } from 'ch2-components/lib/pro-table/types'
import * as models from '@/api/models'
import { reactive } from 'vue'

const loginLogColumns = reactive<ColumnProps<models.UserLoginLog>[]>([
  { dataIndex: 'provider', title: '站点', key: 'provider' },
  {
    dataIndex: 'result',
    title: '状态',
    key: 'result',
    enum: models.LoginResultLog,
  },
  {
    dataIndex: 'userName',
    title: '用户',
    key: 'userName',
  },
  {
    dataIndex: 'loginTime',
    title: '登录时间',
    key: 'loginTime',
    dateFormat: true,
  },
  { dataIndex: 'ipAddress4', title: 'IP地址V4', key: 'ipAddress4' },
  { dataIndex: 'ipAddress6', title: 'IP地址V6', key: 'ipAddress6' },
  {
    dataIndex: 'userAgent',
    title: '浏览器标志',
    key: 'userAgent',
  },
])

const requestLogColumns = reactive<ColumnProps<models.UserRequestLog>[]>([
  { dataIndex: 'path', title: '请求路径', key: 'path' },
  { dataIndex: 'describe', title: '接口描述', key: 'describe' },
  {
    dataIndex: 'userName',
    title: '用户',
    key: 'userName',
  },
  {
    dataIndex: 'time',
    title: '请求时间',
    key: 'time',
    dateFormat: true,
  },
  { dataIndex: 'ipAddress', title: 'IP地址', key: 'ipAddress' },
  {
    dataIndex: 'userAgent',
    title: '浏览器标志',
    key: 'userAgent',
  },
])

export { loginLogColumns, requestLogColumns }
