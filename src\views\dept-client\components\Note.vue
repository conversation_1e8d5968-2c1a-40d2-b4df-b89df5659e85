<template>
  <!-- <c-list ref="listRef" :api="api.EventManage.GetListAsync" immediate pagination >
  <template #renderItem="{ item, index }:{item:PublicEventViewModel, index: number }">
    </template>
    </c-list> -->

  <div>
    <div class="flex items-center justify-between">
      <!-- <a-radio-group v-model:value="searchValue" button-style="solid">
        <a-radio-button value="a">全部</a-radio-button>
        <a-radio-button value="b">笔记</a-radio-button>
        <a-radio-button value="c">报告</a-radio-button>
      </a-radio-group> -->
      <a-input-search
        v-model:value="keyWord"
        placeholder="请输入关键字"
        enter-button
        style="width: 280px"
        @search="listRef?.fetchData"
      />
      <div class="mr-4">
        <a-button type="primary" @click="addOpen = true">新增笔记</a-button>
        <!-- <a-button type="primary" class="ml-4">新增报告</a-button> -->
      </div>
    </div>
    <div class="mt-4 h-[calc(100vh-300px)] overflow-auto">
      <c-list ref="listRef" :api="api.Notes.GetListAsync" immediate pagination :get-params="{ keyWord }">
        <template #renderItem="{ item, index }:{item:NotesViewModel, index: number }">
          <a-list-item :key="index">
            <div class="mt-4 w-full border border-border-secondary rounded-md border-solid p-4 shadow-sm">
              <div class="flex justify-between">
                <h2>{{ item.name }}</h2>
                <div class="h-100% border border-#2A527A rounded-xl border-solid px-2 c-#2A527A">笔记</div>
              </div>

              <div class="my-4">
                {{ item.content }}
              </div>
              <div class="flex items-center justify-between">
                <div class="c-text-secondary">{{ dateTime(item.createdTime) }}</div>
                <div>
                  <a-button type="primary" @click="onEdit(item)">编辑</a-button>
                  <a-popconfirm
                    title="确认删除吗"
                    ok-text="确定"
                    cancel-text="取消"
                    @confirm="onDel(item.id)"
                  >
                    <a-button type="primary" ghost danger class="ml-4">删除</a-button>
                  </a-popconfirm>
                </div>
              </div>
            </div>
          </a-list-item>
        </template>
      </c-list>
      <!-- <template v-for="item in 4" :key="item">
        <div class="mt-4 border border-border-secondary rounded-md border-solid p-4 shadow-sm">
          <div class="flex justify-between">
            <h2>我的笔记××××××××××××××××</h2>
            <div class="h-100% border border-#2A527A rounded-xl border-solid px-2 c-#2A527A">笔记</div>
          </div>

          <div class="my-4">
            这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本。这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本。这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本。
          </div>
          <div class="flex items-center justify-between">
            <div class="c-text-secondary">2025-05-12</div>
            <div><a-button type="primary">编辑</a-button><a-button type="primary" ghost danger class="ml-4">删除</a-button></div>
          </div>
        </div>
        <div class="mt-4 border border-border-secondary rounded-md border-solid p-4 shadow-sm">
          <div class="flex justify-between">
            <h2>我的报告××××××××××××××××</h2>
            <div class="h-100% border border-#2A527A rounded-xl border-solid px-2 c-#2A527A">报告</div>
          </div>

          <div class="my-4">
            这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本这是一段文本。
          </div>
          <div class="flex items-center justify-between">
            <div class="c-text-secondary">2025-05-12</div>
            <div><a-button type="primary">编辑</a-button><a-button type="primary" danger ghost class="ml-4">删除</a-button></div>
          </div>
        </div>
      </template> -->
    </div>
  </div>
  <a-modal v-model:open="addOpen" title="新增/编辑笔记" width="60%" :closable="false" destroy-on-close>
    <template #footer>
      <a-button key="back" @click="noteClose">关闭</a-button>
    </template>
    <c-pro-form
      v-model:value="form"
      :descriptions="{ column: 1, bordered: true }"
      :fields="fields"
      :label-col="{ style: { width: '100px' } }"
      layout="inline"
      @finish="onSave"
    >
      <template #footer>
        <a-descriptions-item label="操作">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-descriptions-item>
      </template>
    </c-pro-form>
  </a-modal>
</template>

<script lang='ts' setup>
import type { NotesViewModel } from '@/api/models'
import * as api from '@/api'
import { NotesEditModel } from '@/api/models'
import { Guid } from '@/utils/GUID'
import { message } from 'ant-design-vue'

const { addOpen, form, fields, onSave, onEdit, noteClose } = addOrEditHook()

const listRef = useTemplateRef('listRef')

// const searchValue = ref('')
const keyWord = ref('')

async function onDel(id: string | Guid) {
  try {
    await api.Notes.Delete_GetAsync({ id })
    message.success('删除成功')
    listRef.value?.refreshData()
  }
  catch (error: any) {
    message.error(error.message)
  }
}

function addOrEditHook() {
  const addOpen = ref(false)

  const form = ref(new NotesEditModel())

  const fields = ref([
    {
      label: '标题',
      prop: 'name',
      el: 'input',
      formItem: {
        rules: [{ required: true, message: '标题必填!' }],
      },
    },
    {
      label: '内容',
      prop: 'content',
      el: 'textarea',
      formItem: {
        rules: [{ required: true, message: '内容必填!' }],
      },
    },
    // {
    //   label: '引用报告',
    //   prop: 'report',
    //   el: 'select',
    //   formItem: {
    //     rules: [{ required: true, message: '引用报告必填!' }],
    //   },
    // },
  ])

  async function onSave() {
    const temp = Guid.isNotNull(form.value.id) ? api.Notes.Update_PostAsync : api.Notes.Create_PostAsync
    try {
      await temp(form.value)
      message.success('保存成功')
      addOpen.value = false
      listRef.value?.refreshData()
    }
    catch (error: any) {
      message.error(error.message)
    }
  }

  function noteClose() {
    addOpen.value = false
    form.value = new NotesEditModel()
  }

  function onEdit(row: NotesViewModel) {
    form.value.id = row.id
    form.value.content = row.content
    form.value.name = row.name
    addOpen.value = true
  }

  return { addOpen, form, fields, onSave, onEdit, noteClose }
}
</script>

<style scoped>

</style>
