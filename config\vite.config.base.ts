import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { createHtmlPlugin } from 'vite-plugin-html'
import ViteComponents from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import { Ch2ComponentResolver } from 'ch2-components/Ch2ComponentResolver'
import Unocss from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { getLessModifyVars } from '../src/theme/utils'

export default defineConfig({
  server: {
    hmr: {
      overlay: false,
    },
    host: '0.0.0.0',

  },
  plugins: [

    vue(),
    vueJsx(),
    createHtmlPlugin({
      viteNext: true,
    }),
    Unocss(),
    ViteComponents({
      dts: 'src/components.d.ts',
      resolvers: [
        (componentName) => {
          if (componentName.startsWith('CIcon'))
            return { name: componentName.slice(5), from: '@ant-design/icons-vue' }
        },
        AntDesignVueResolver({ importStyle: false }),
        Ch2ComponentResolver(),
      ],
    }),
    AutoImport({
      imports: ['vue', '@vueuse/core'],
      dts: 'src/auto-imports.d.ts',
      dirs: ['src/composables', 'src/stores', 'src/utils', 'src/hooks'],
      vueTemplate: true,
    }),
  ],
  resolve: {
    alias: [
      {
        find: '@/',
        replacement: '/src/',
      },
      {
        find: 'vue-i18n',
        replacement: 'vue-i18n/dist/vue-i18n.cjs.js',
      },
    ],
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
  },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: getLessModifyVars(),
        javascriptEnabled: true,
      },
    },
  },
})
