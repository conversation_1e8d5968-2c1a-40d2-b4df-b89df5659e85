import type { Directive } from 'vue'
import { message } from 'ant-design-vue'

const copy: Directive = {
  created(el, binding) {
    el.onclick = (event: Event) => {
      if (binding.modifiers.stop)
        event.stopPropagation()

      const text = binding.value || el.textContent
      if (text) {
        navigator.clipboard.writeText(text).then(() => {
          message.success('复制成功')
        }).catch((err) => {
          message.error('复制失败', err)
        })
      }
      else {
        message.error('没有找到要复制的文本')
      }
    }
  },
}

export default copy
