<template>
  <main class="flex-1 p-5">
    <div class="flex gap-5">
      <!-- <a-affix :offset-top="116"> -->
      <div class="search-box h-[calc(100vh-160px)] min-w-80 w-80 overflow-y-auto bg-amber">
        <ArticleFilter
          v-model:search-obj="searchObj"
          v-model:get-search-obj="getSearchObj"
          :search-loding="leftSpinning"
          :data-type="props.type"
          @search="onSearch"
          @reset="onReset"
          @save-condition="openSaveModal"
        />
      </div>
      <!-- </a-affix> -->
      <div class="h-[calc(100vh-160px)] w-full overflow-y-auto">
        <a-spin :spinning="rightSpinning">
          <div v-if="articles.length > 0">
            <div>
              <ListItem
                v-for="(article, index) in articles"
                :key="index"
                :article="article"
                @on-collect-success="getData"
              />
            </div>
            <div class="flex justify-end">
              <a-pagination
                v-model:current="pageData.current"
                v-model:page-size="pageData.pageSize"
                show-size-changer
                :show-total="(total) => `总 ${total} 条`"
                :total="pageData.total"
                @change="pageChange"
              />
            </div>
          </div>
          <a-empty v-else />
        </a-spin>
      </div>
    </div>
  </main>
  <a-modal v-model:visible="showSaveModal" title="保存查询条件" @ok="handleSaveCondition">
    <a-form layout="vertical">
      <a-form-item label="分组">
        <c-select v-model:value="saveForm.groupId" :api="api.RiskWarning.GetUserGroupsWithQueriesAsync" :field-names="{ label: 'name', value: 'id' }" placeholder="请选择分组" />
      </a-form-item>
      <a-form-item label="标签">
        <a-input v-model:value="saveForm.queryTag" placeholder="请输入标签" />
      </a-form-item>
      <a-form-item label="备注">
        <a-input v-model:value="saveForm.content" placeholder="请输入内容" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import type { DataManageModelPageView, DataType } from '@/api/models'
import * as api from '@/api'
import { DataQueryParameterEditModel } from '@/api/models'

import { message } from 'ant-design-vue'
import ListItem from './listItem.vue'

interface Props {
  type: DataType
}

const props = defineProps<Props>()

const articles = ref<DataManageModelPageView[]>([])

const searchObj = ref(new DataQueryParameterEditModel())

const getSearchObj = ref({
  isRead: false as boolean | undefined,
})

const pageData = ref({
  current: 1,
  pageSize: 20,
  total: 0,
})

const leftSpinning = ref(false)

const rightSpinning = ref(false)

// 恢复 getData 及相关调用
async function getData() {
  rightSpinning.value = true
  const offset = (pageData.value.current - 1) * pageData.value.pageSize

  try {
    const res = await api.DataManageModels.Query_PostAsync(
      { ...getSearchObj.value, offset, limit: pageData.value.pageSize },
      searchObj.value,
    )

    articles.value = res.items || []
    pageData.value.total = res.totals || 0
    rightSpinning.value = false
  }
  catch (error: any) {
    message.error(`获取数据失败${error.message}`)
    rightSpinning.value = false
  }
}

function pageChange(page: number, pageSize: number) {
  pageData.value.current = page
  pageData.value.pageSize = pageSize
  getData()
}

function onReset() {
  pageData.value.current = 1
  getData()
}

async function onSearch() {
  leftSpinning.value = true
  // 搜索逻辑
  pageData.value.current = 1

  await getData()
  leftSpinning.value = false
}

const {
  showSaveModal,
  saveForm,
  openSaveModal,
  handleSaveCondition,
} = useSaveCondition()

// 保留 useSaveCondition 相关实现
function useSaveCondition() {
  const showSaveModal = ref(false)
  const saveForm = ref({
    queryTag: '',
    content: '',
    groupId: '',
  })

  // 打开弹窗
  function openSaveModal() {
    showSaveModal.value = true
  }

  // 保存
  async function handleSaveCondition() {
    try {
      await api.RiskWarning.CreateRiskWarning_PostAsync(
        {
          queryTag: saveForm.value.queryTag,
          content: saveForm.value.content,
          groupId: saveForm.value.groupId,
        },
        searchObj.value,
      )
      message.success('保存成功')
      showSaveModal.value = false
    }
    catch {
      message.error('保存失败')
    }
  }

  return {
    showSaveModal,
    saveForm,
    openSaveModal,
    handleSaveCondition,
  }
}

onMounted(() => {
  searchObj.value.dataType = props.type
  nextTick(() => {
    getData()
  })
})
</script>

<style lang="less">
.ant-collapse-header {
  padding: 12px 0 !important;
}
.ant-collapse-content-box {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.search-box {
  &::-webkit-scrollbar {
    width: 2px; /* 设置垂直滚动条的宽度 */
  }
}
</style>
