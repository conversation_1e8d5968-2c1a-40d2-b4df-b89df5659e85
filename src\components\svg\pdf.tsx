import type { CustomIconComponentProps } from '@ant-design/icons-vue/lib/components/Icon'

function icon(props: CustomIconComponentProps) {
  return (
    <svg {...props} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 56.054 66.35" class="ckbox-doc-icon__icon">
      <g transform="translate(-760.79 -297.644)">
        <g transform="translate(717.035 265.397)">
          <path d="M82.585,33.746H53.6a8.342,8.342,0,0,0-8.342,8.342V88.754A8.342,8.342,0,0,0,53.6,97.1H89.966a8.342,8.342,0,0,0,8.342-8.342V49.469Z" fill="#fff" stroke="#fa3225" stroke-miterlimit="10" stroke-width="3"></path>
          <path d="M204.77,33.746v9.866a7.156,7.156,0,0,0,7.156,7.156h9.866Z" transform="translate(-123.189)" fill="#fa3225"></path>
        </g>
        <g transform="translate(768.517 337.278)">
          <rect width="17.063" height="3.707" rx="1.853" transform="translate(0 5.926)" fill="#fa3225"></rect>
          <rect width="11.25" height="3.707" rx="1.853" transform="translate(0 11.851)" fill="#fa3225"></rect>
          <rect width="30.474" height="3.707" rx="1.853" fill="#fa3225"></rect>
        </g>
        <g transform="translate(762.773 294.187)"><path d="M49.9-138.9a7.264,7.264,0,0,1-3.09-3.893c.326-1.339.84-3.372.449-4.646a1.812,1.812,0,0,0-3.459-.492c-.362,1.324-.029,3.191.586,5.572a67.964,67.964,0,0,1-2.953,6.209c-.007,0-.007.007-.014.007-1.961,1.006-5.326,3.22-3.944,4.921a2.249,2.249,0,0,0,1.556.724c1.3,0,2.584-1.3,4.422-4.472a41.242,41.242,0,0,1,5.717-1.679,10.968,10.968,0,0,0,4.632,1.411,1.873,1.873,0,0,0,1.426-3.141C54.216-139.367,51.292-139.085,49.9-138.9Zm-11.137,6.969a10,10,0,0,1,2.526-2.909C39.713-132.326,38.758-131.877,38.758-131.935Zm6.788-15.841c.608,0,.55,2.67.145,3.394C45.329-145.54,45.336-147.776,45.546-147.776Zm-2.034,11.347a33.39,33.39,0,0,0,2.055-4.537,9.373,9.373,0,0,0,2.5,2.953A26.647,26.647,0,0,0,43.513-136.429Zm10.935-.413s-.413.492-3.1-.651C54.266-137.7,54.744-137.037,54.447-136.842Z" transform="translate(-29.503 163.391)" fill="#fa3225"></path></g>
      </g>
    </svg>
  )
}
export default {
  icon,
  color: '#FFEAE8',
}
