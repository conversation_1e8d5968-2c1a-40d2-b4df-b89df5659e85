import { _Role } from './RoleName'

export function existenceRole(targe: _Role[] | _Role) {
  const { active: userInfo } = useUserStore()
  const roles = userInfo.roles
  return roles?.some(item => targe.includes(item.name as any))
}

export function isAdmin() {
  const { active: userInfo } = useUserStore()
  return userInfo.roles.some(v => v.name === _Role.超级管理员)
}

/**
 * 业务权限判断
 * @param value
 */
export function $auth(value: _Role | _Role[]): boolean {
  const { active: userInfo } = useUserStore()
  const currentPageRoles = userInfo.roles.map(v => v.name) as _Role[]

  if (isAdmin())
    return true
  if (Array.isArray(value)) {
    const hasPermission = value.some(item => currentPageRoles.includes(item))
    return hasPermission
  }
  else {
    return currentPageRoles.includes(value)
  }
}
