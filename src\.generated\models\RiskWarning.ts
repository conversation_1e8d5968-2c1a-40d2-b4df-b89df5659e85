import { RiskWarningGroup } from "./RiskWarningGroup";
import { User } from "./User";
import { RiskWarningDataId } from "./RiskWarningDataId";
/**风险预警*/
export class RiskWarning {
  groupId?: GUID = null;
  /**风险预警目录*/
  group?: RiskWarningGroup | null | undefined = null;
  userId: GUID = "00000000-0000-0000-0000-000000000000";
  /**用户*/
  user?: User | null | undefined = null;
  /**生成标签*/
  queryTag?: string | null | undefined = null;
  /**备注内容*/
  content?: string | null | undefined = null;
  /**创建时间*/
  time: Dayjs = dayjs();
  /**最后更新时间*/
  lastUpdated?: Dayjs | null | undefined = null;
  /**关联的文章ID缓存*/
  cachedDataIds?: RiskWarningDataId[] | null | undefined = [];
  /**查询的最后一篇文章*/
  lastDataId?: GUID = null;
  keyword?: string | null | undefined = null;
  /**结果中搜索关键词*/
  refineKeyword?: string | null | undefined = null;
  /**情感分析分数（0~100 有小数）*/
  emotionFrom?: number | null | undefined = null;
  /**情感分析分数（0~100 有小数）*/
  emotionTo?: number | null | undefined = null;
  /**是否涉我(涉中)*/
  isChina?: boolean | null | undefined = null;
  tag?: string[] | null | undefined = [];
  domain?: string[] | null | undefined = [];
  region?: string[] | null | undefined = [];
  hotTag?: string[] | null | undefined = [];
  timeFrom?: Dayjs | null | undefined = null;
  timeTo?: Dayjs | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
