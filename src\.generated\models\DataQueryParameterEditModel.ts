import { DataType } from "./DataType";
export class DataQueryParameterEditModel {
  /**文章类型*/
  dataType?: DataType | null | undefined = null;
  keyword?: string | null | undefined = null;
  /**结果中搜索关键词*/
  refineKeyword?: string | null | undefined = null;
  /**情感分析分数（0~100 有小数）*/
  emotionFrom?: number | null | undefined = null;
  /**情感分析分数（0~100 有小数）*/
  emotionTo?: number | null | undefined = null;
  /**是否涉我(涉中)*/
  isChina?: boolean | null | undefined = null;
  tag?: string[] | null | undefined = [];
  domain?: string[] | null | undefined = [];
  region?: string[] | null | undefined = [];
  hotTag?: string[] | null | undefined = [];
  timeFrom?: Dayjs | null | undefined = null;
  timeTo?: Dayjs | null | undefined = null;
}
