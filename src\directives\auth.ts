import type { _Role } from '@/permission/RoleName'
import type { Directive, DirectiveBinding } from 'vue'
import { $auth } from '@/permission'
import { nextTick } from 'vue'

const auth: Directive<Element, _Role | _Role[]> = {
  created: utilHas,
  updated: utilHas,
  mounted: utilHas,
}

function utilHas(el: Element, binding: DirectiveBinding<_Role | _Role[]>) {
  nextTick(async () => {
    let { value } = binding
    console.log('%c [ binding ]-15', 'font-size:13px; background:pink; color:#bf2c9f;', binding)

    if (!value)
      value = el.textContent as any
    if ($auth(value) === false && el)
      el.remove()
  })
}

export default auth
