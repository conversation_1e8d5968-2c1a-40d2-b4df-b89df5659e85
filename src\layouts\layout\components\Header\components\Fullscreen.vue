<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: luckymiaow
-->
<!-- 全屏 -->
<template>
  <div @click="toggle">
    <FullscreenOutlined v-if="!isFullscreen" />
    <CompressOutlined v-else />
  </div>
</template>

<script setup lang="ts">
import { CompressOutlined, FullscreenOutlined } from '@ant-design/icons-vue'
import { useFullscreen } from '@vueuse/core'

const { toggle, isFullscreen } = useFullscreen()
</script>

<style scoped></style>
