<template>
  <div class="loading-box">
    <div v-if="loading">
      <div class="loading">
        <span />
        <span />
        <span />
        <span />
        <span />
      </div>
    </div>
    <div class="spin-container h[100%]" :class="{ skeleton: appStore.routerLoading }">
      <a-skeleton active :loading="loading" />
      <main v-show="!loading" class="h[100%]">
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores'
import { computed } from 'vue'

const props = defineProps<{ spinning?: boolean }>()

const appStore = useAppStore()

const loading = computed(() => appStore.routerLoading || props.spinning)
</script>

<style lang="less" scoped>
.loading-box {
  position: relative;
  height: 100%;
}

.spin-container {
  position: relative;
  transition: opacity 0.3s;
}

.skeleton {
  min-height: 380px;
}

.loading {
  background-color: rgba(170, 170, 170, 0.075);
  background-size: 100% 100%;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  transition: opacity 0.3s;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  min-height: 380px;

  position: absolute;
  --speed-of-animation: 0.9s;
  --gap: 6px;
  --first-color: #4c86f9;
  --second-color: #49a84c;
  --third-color: #f6bb02;
  --fourth-color: #f6bb02;
  --fifth-color: #2196f3;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
  z-index: 2;
}

.loading span {
  width: 4px;
  height: 50px;
  background: var(--first-color);
  animation: scale var(--speed-of-animation) ease-in-out infinite;
}

.loading span:nth-child(2) {
  background: var(--second-color);
  animation-delay: -0.8s;
}

.loading span:nth-child(3) {
  background: var(--third-color);
  animation-delay: -0.7s;
}

.loading span:nth-child(4) {
  background: var(--fourth-color);
  animation-delay: -0.6s;
}

.loading span:nth-child(5) {
  background: var(--fifth-color);
  animation-delay: -0.5s;
}

@keyframes scale {
  0%,
  40%,
  100% {
    transform: scaleY(0.05);
  }

  20% {
    transform: scaleY(1);
  }
}
</style>
