<!--
 * @Author: 龙兆柒 <EMAIL>
 * @Date: 2023-05-23 16:37:53
 * @LastEditors: luckymiaow
 * @LastEditTime: 2023-07-06 17:59:02
 * @FilePath: \ch2-template-vue\src\layouts\layout\components\Menu\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--  -->
<template>
  <a-menu v-model:selected-keys="selectedKeys" v-model:open-keys="openKeys" :theme="theme">
    <template v-for="item in routerMenu" :key="item.path">
      <Menu :menu-info="item" :parent-path="item.path" />
    </template>
    <template #overflowedIndicator>
      <c-icon-menu-outlined class="menu-icon" />
    </template>
  </a-menu>
</template>

<script setup lang="ts">
import { routerMenu } from '@/router'
import { useAppStore } from '@/stores'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import Menu from './SubMenu.vue'

const globalStore = useAppStore()
const route = useRoute()
// 当前路由
const currentRoute = computed(() => route.fullPath)

// 当前展开的 SubMenu 菜单项
const openKeys = ref()

// 当前选中的菜单项 key 数组
const selectedKeys = ref()

/**
 * @function 处理当前选择菜单
 * @param newValue 新路由
 * @description 用于监听tabs组件选择菜单后菜单组件跟着变化
 */
function checkMenu(newValue: string) {
  selectedKeys.value = [newValue]
  if (route && route.matched[0])
    openKeys.value = [route.matched[0].path]
}

// 监听路由变化
watch(currentRoute, (newValue) => {
  checkMenu(newValue)
})

// 当前主题（亮色或者暗色）
const theme = computed(() => (globalStore.themeConfig.darkMenu ? 'dark' : 'light'))

onMounted(() => {
  checkMenu(currentRoute.value)
})
</script>

<style scoped>
.ant-menu-inline {
  border-right-color: transparent;
}
</style>
