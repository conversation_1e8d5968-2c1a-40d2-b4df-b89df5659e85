<template>
  <div class="h-full min-w-80 w-80 overflow-y-auto rounded bg-white p-4 shadow">
    <div class="mb-2 flex justify-between">
      <a-typography-title :level="5">搜索关键词</a-typography-title>
      <a-button type="link" @click="onReset">重置搜索</a-button>
    </div>
    <div class="relative mb-4">
      <a-input-search
        v-model:value="searchObj.keyword!"
        class="c2-search-input"
        placeholder="搜索智库、标题、内容"
        size="large"
        style="width: 100%"
        enter-button
        :loading="searchLoding"
        @search="onSearch"
      />
    </div>

    <a-spin :spinning="leftSpinning">
      <a-collapse v-model:active-key="activeKey" ghost expand-icon-position="end">
        <a-collapse-panel key="5">
          <template #header>
            <div class="text-base font-medium">热门标签</div>
          </template>
          <div class="mb-5 border-t border-gray-200">
            <a-checkbox-group v-model:value="searchObj.hotTag!" style="width: 100%">
              <template v-if="tags.hotTag && tags.hotTag.length">
                <RecycleScroller
                  :items="tags.hotTag"
                  :item-size="40"
                  key-field="name"
                  style="height: 300px; width: 100%;"
                  class="virtual-scroll-list"
                >
                  <template #default="{ item }">
                    <div class="virtual-list-item w-100% flex items-center justify-between" @change="onSearch">
                      <a-checkbox :value="item.name">
                        <span class="text-base">{{ item.name }}</span>
                      </a-checkbox>
                      <span class="pr4 text-sm text-gray-500">{{ item.count }}</span>
                    </div>
                  </template>
                </RecycleScroller>
              </template>
              <template v-else>
                <a-empty description="暂无数据" class="no-data" />
              </template>
            </a-checkbox-group>
          </div>
        </a-collapse-panel>
        <a-collapse-panel key="1">
          <template #header>
            <div class="text-base font-medium">关键信息</div>
          </template>
          <div class="mb-5 border-t border-gray-200">
            <div class="mb-1"> <a-checkbox :checked="searchObj.isChina!" @change="isChinaChange"><span class="text-base">涉中</span></a-checkbox></div>
            <div> <a-checkbox :checked="getSearchObj.isRead === false" @change="isReadChange"><span class="text-base">未读</span></a-checkbox></div>
            <!-- <a-checkbox :checked="searchObj.emotion!" @change="isChinaChange"><span class="text-base">负面</span></a-checkbox> -->
          </div>
        </a-collapse-panel>
        <a-collapse-panel key="2">
          <template #header>
            <div class="text-base font-medium">相关信息</div>
          </template>
          <div class="mb-5 border-t border-gray-200">
            <a-checkbox-group v-model:value="searchObj.tag!" style="width: 100%">
              <template v-if="tags.tag && tags.tag.length">
                <RecycleScroller
                  :items="tags.tag"
                  :item-size="40"
                  key-field="name"
                  style="height: 300px; width: 100%;"
                  class="virtual-scroll-list"
                >
                  <template #default="{ item }">
                    <div class="virtual-list-item w-100% flex items-center justify-between" @change="onSearch">
                      <a-checkbox :value="item.name">
                        <span class="text-base">{{ item.name }}</span>
                      </a-checkbox>
                      <span class="pr4 text-sm text-gray-500">{{ item.count }}</span>
                    </div>
                  </template>
                </RecycleScroller>
              </template>
              <template v-else>
                <a-empty description="暂无数据" class="no-data" />
              </template>
            </a-checkbox-group>
          </div>
        </a-collapse-panel>
        <a-collapse-panel key="3">
          <template #header>
            <div class="text-base font-medium">地区/国家</div>
          </template>
          <div class="mb-5 border-t border-gray-200">
            <a-checkbox-group v-model:value="searchObj.region!" style="width: 100%">
              <template v-if="tags.region && tags.region.length">
                <RecycleScroller
                  :items="tags.region"
                  :item-size="40"
                  key-field="name"
                  style="height: 300px; width: 100%;"
                  class="virtual-scroll-list"
                >
                  <template #default="{ item }">
                    <div class="virtual-list-item w-100% flex items-center justify-between" @change="onSearch">
                      <a-checkbox :value="item.name">
                        <span class="text-base">{{ item.name }}</span>
                      </a-checkbox>
                      <span class="pr4 text-sm text-gray-500">{{ item.count }}</span>
                    </div>
                  </template>
                </RecycleScroller>
              </template>
              <template v-else>
                <a-empty description="暂无数据" class="no-data" />
              </template>
            </a-checkbox-group>
          </div>
        </a-collapse-panel>
        <a-collapse-panel key="4">
          <template #header>
            <div class="text-base font-medium">主题分类</div>
          </template>
          <div class="mb-5 border-t border-gray-200">
            <a-checkbox-group v-model:value="searchObj.domain!" style="width: 100%">
              <template v-if="tags.domain && tags.domain.length">
                <RecycleScroller
                  :items="tags.domain"
                  :item-size="40"
                  key-field="name"
                  style="height: 300px; width: 100%;"
                  class="virtual-scroll-list"
                >
                  <template #default="{ item }">
                    <div class="virtual-list-item w-100% flex items-center justify-between" @change="onSearch">
                      <a-checkbox :value="item.name">
                        <span class="text-base">{{ item.name }}</span>
                      </a-checkbox>
                      <span class="pr4 text-sm text-gray-500">{{ item.count }}</span>
                    </div>
                  </template>
                </RecycleScroller>
              </template>
              <template v-else>
                <a-empty description="暂无数据" class="no-data" />
              </template>
            </a-checkbox-group>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </a-spin>

    <div class="mb-5 border-t border-gray-200 pt-4">
      <h3 class="mb-2 text-base font-medium">发布日期</h3>
      <a-checkbox-group v-model:value="selectedValue" style="width: 100%" @change="handleChange">
        <div v-for="option in timeOptions" :key="option" class="mb-1 w-100%">
          <a-checkbox :value="option">
            <span class="text-base">{{ option }}</span>
          </a-checkbox>
        </div>
      </a-checkbox-group>
      <div v-show="selectedValue?.includes('自定义时间段')" class="mt-2 flex items-center gap-1">
        <a-range-picker v-model:value="dateRange" @change="panelChange" />
      </div>
    </div>

    <div class="mt-5">
      <h3 class="mb-4 text-base font-medium">结果中搜索</h3>
      <div class="mb-4">
        <a-input-search
          v-model:value="searchObj.refineKeyword!"
          class="c2-search-input"
          placeholder="关键词"
          size="large"
          style="width: 100%"
          enter-button
          :loading="searchLoding"
          @search="onSearch"
        />
      </div>
      <a-popover title="提示">
        <template v-if="dataType != null" #content>
          <p>保存当前筛选条件，符合条件的新文章将实时预警，并通过微信/邮箱推送通知。</p>
        </template>
        <a-button type="primary" size="large" class="w-full" @click="onSaveCondition">
          保存搜索条件
        </a-button>
      </a-popover>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { DataType } from '@/api/models'
import type { Dayjs } from 'dayjs'
import * as api from '@/api'
import { DataQueryParameterEditModel, DataTotalByTagView } from '@/api/models'
import { message } from 'ant-design-vue'
import { RecycleScroller } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

const props = defineProps<{
  searchLoding?: boolean
  dataType?: DataType
}>()

const emit = defineEmits<{
  'search': []
  'reset': []
  'saveCondition': []
  'update:searchObj': [value: DataQueryParameterEditModel]
  'update:getSearchObj': [value: typeof getSearchObj.value]
  'update:dateRange': [value: [Dayjs, Dayjs] | undefined]
  'update:selectedValue': [value: string[] | undefined]
}>()

const getSearchObj = ref({
  isRead: false as boolean | undefined,
})

watch(getSearchObj, (newValue) => {
  emit('update:getSearchObj', newValue)
}, { deep: true })

const activeKey = ref('1')
const searchObj = ref(new DataQueryParameterEditModel())
searchObj.value.region = searchObj.value.region || []
searchObj.value.domain = searchObj.value.domain || []
const dateRange = ref<[Dayjs, Dayjs] | undefined>()
const tags = ref<DataTotalByTagView>(new DataTotalByTagView())
const leftSpinning = ref(false)

const { timeOptions, selectedValue, handleChange, panelChange } = generateTimeRangesHook()

// 监听 props 变化，同步到本地状态
watch(() => props.dataType, (newType) => {
  searchObj.value.dataType = newType
  loadSavedCondition()
  getTagsData()
}, { immediate: true })

// 监听本地状态变化，同步到父组件
watch(searchObj, (newValue) => {
  emit('update:searchObj', newValue)
}, { deep: true })

watch(dateRange, (newValue) => {
  emit('update:dateRange', newValue)
})

watch(selectedValue, (newValue) => {
  emit('update:selectedValue', newValue)
})

async function getTagsData() {
  leftSpinning.value = true
  try {
    tags.value = await api.DataManageModels.QueryTotal_PostAsync({
      dataType: props.dataType,
    })
    leftSpinning.value = false
  }
  catch (error: any) {
    message.error(`获取数据失败${error.message}`)
    leftSpinning.value = false
  }
}

function onReset() {
  searchObj.value = new DataQueryParameterEditModel()
  searchObj.value.dataType = props.dataType
  selectedValue.value = []
  dateRange.value = undefined
  emit('reset')
  onSearch()
}

function onSearch() {
  emit('search')
}

function isChinaChange(e: { target: { checked: boolean | null | undefined } }) {
  if (e.target.checked) {
    searchObj.value.isChina = e.target.checked
  }
  else {
    searchObj.value.isChina = undefined
  }
  onSearch()
}

function isReadChange(e: { target: { checked: boolean | null | undefined } }) {
  getSearchObj.value.isRead = e.target.checked ? false : undefined

  // if (e.target.checked) {
  //   false
  // }
  // else {
  //   getSearchObj.value.isRead = undefined
  // }
  onSearch()
}

function onSaveCondition() {
  const saveData = {
    searchObj: searchObj.value,
    dateRange: dateRange.value,
    selectedValue: selectedValue.value,
  }
  localStorage.setItem(`DataType_${props.dataType}`, JSON.stringify(saveData))
  if (props.dataType == null)
    message.success('保存成功')
  emit('saveCondition')
}

function loadSavedCondition() {
  const storedData = localStorage.getItem(`DataType_${props.dataType}`)
  if (storedData) {
    try {
      const parsedData = JSON.parse(storedData)
      searchObj.value = new DataQueryParameterEditModel()
      Object.assign(searchObj.value, parsedData.searchObj)
      searchObj.value.dataType = props.dataType

      if (parsedData.dateRange && parsedData.dateRange.length === 2) {
        dateRange.value = [dayjs(parsedData.dateRange[0]), dayjs(parsedData.dateRange[1])]
      }

      selectedValue.value = parsedData.selectedValue || []
    }
    catch (error) {
      console.error('加载保存的搜索条件失败:', error)
    }
  }
}

function generateTimeRangesHook() {
  const timeOptions = ref([
    '最近24小时',
    '最近1周',
    '最近1个月',
    '最近1年',
    '自定义时间段',
  ])

  const selectedValue = ref<string[]>()

  function handleChange(newCheckedList: (string | number | boolean)[]) {
    const stringList = newCheckedList.map(item => String(item))
    if (stringList.length > 1) {
      const lastItem = stringList[stringList.length - 1]
      if (lastItem) {
        selectedValue.value = [lastItem]
      }
    }
    else {
      selectedValue.value = stringList
    }

    switch (selectedValue.value?.[0]) {
      case '最近24小时':
        searchObj.value.timeFrom = dayjs().subtract(1, 'day')
        searchObj.value.timeTo = undefined
        break
      case '最近1周':
        searchObj.value.timeFrom = dayjs().subtract(1, 'week')
        searchObj.value.timeTo = undefined
        break
      case '最近1个月':
        searchObj.value.timeFrom = dayjs().subtract(1, 'month')
        searchObj.value.timeTo = undefined
        break
      case '最近1年':
        searchObj.value.timeFrom = dayjs().subtract(1, 'year')
        searchObj.value.timeTo = undefined
        break
      case '自定义时间段':
        searchObj.value.timeFrom = dateRange.value?.[0]
        searchObj.value.timeTo = dateRange.value?.[1]
        break
      default:
        searchObj.value.timeFrom = undefined
        searchObj.value.timeTo = undefined
        break
    }
    onSearch()
  }

  function panelChange() {
    searchObj.value.timeFrom = dateRange.value?.[0]
    searchObj.value.timeTo = dateRange.value?.[1]
    onSearch()
  }

  return { timeOptions, selectedValue, handleChange, panelChange }
}
</script>

<style lang="less">
.ant-collapse-header {
  padding: 12px 0 !important;
  font-weight: bold;
  font-size: 16px;
}
.ant-collapse-content-box {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.search-box {
  &::-webkit-scrollbar {
    width: 2px;
    background: #f0f0f0;
  }
  &::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
  }
}
.virtual-list-item {
  transition: background 0.2s;
  &:hover {
    background: #f5f7fa;
  }
  align-items: center;
  min-height: 40px;
}
.virtual-scroll-list {
  overflow-y: auto;
  scrollbar-width: thin;
}
.no-data {
  color: #bfbfbf;
  font-size: 15px;
  text-align: center;
  padding: 32px 0;
}
.c2-search-input {
  border-radius: 8px !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
}
.ant-btn {
  border-radius: 6px;
}
.ant-divider {
  border-color: #f0f0f0;
}
.ant-spin-nested-loading > div > .ant-spin {
  top: 40%;
}
.ant-spin-blur {
  opacity: 0.5;
}
</style>
