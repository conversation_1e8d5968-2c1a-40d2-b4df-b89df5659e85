<template>
  <div>
    <DefineTemplate>
      <div>
        <div class="lang-switcher" style="text-align:right;margin-bottom:8px;">
          <a-radio-group v-model:value="lang">
            <a-radio-button value="zh">中文</a-radio-button>
            <a-radio-button value="en">原文</a-radio-button>
          </a-radio-group>
        </div>
        <div v-if="loading">加载中...</div>
        <div v-else-if="article">
          <div><b>标题：</b>{{ lang === 'zh' ? (article.titleCn ?? article.title ?? '') : (article.title ?? '') }}</div>
          <div><b>作者：</b>{{ article.author ?? '' }}</div>
          <div><b>来源：</b>{{ article.source ?? '' }}</div>
          <div><b>来源国家：</b>{{ article.sourceCountry ?? '' }}</div>
          <div><b>时间：</b>{{ article.time ?? '' }}</div>
          <div><b>类型：</b>{{ article.dataType ?? '' }}</div>
          <div><b>是否涉我：</b>{{ article.isChina ? '是' : '否' }}</div>
          <div><b>AI分析：</b>{{ article.ai ?? '' }}</div>
          <div><b>情感分数：</b>{{ article.emotion ?? '' }}</div>
          <div><b>链接：</b><a :href="article.link ?? ''" target="_blank">{{ article.link ?? '' }}</a></div>
          <div>
            <b>标签：</b>
            <a-tag v-for="tag in article.tag || []" :key="String(tag.id)">{{ tag.name ?? '' }}</a-tag>
          </div>
          <div>
            <b>热门标签：</b>
            <a-tag v-for="tag in article.dataHotTag || []" :key="String(tag.id)">{{ tag.name ?? '' }}</a-tag>
          </div>
          <div>
            <b>领域分类：</b>
            <a-tag v-for="d in article.domain || []" :key="String(d.id)">{{ d.name ?? '' }}</a-tag>
          </div>
          <div>
            <b>涉及地区：</b>
            <a-tag v-for="r in article.region || []" :key="String(r.id)">{{ r.name ?? '' }}</a-tag>
          </div>
          <div><b>内容：</b></div>
          <div style="white-space: pre-wrap;">
            {{ lang === 'zh' ? (article.contentCn ?? article.content ?? '') : (article.content ?? '') }}
          </div>
        </div>
        <div v-else>未找到文章</div>
      </div>
    </DefineTemplate>
    <ReuseTemplate v-if="view" />
    <a-modal v-else :open="visible" title="文章详情" width="80%" :footer="null" @cancel="close" @update:visible="onVisibleChange">
      <ReuseTemplate />
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import type { DataManageModel } from '@/.generated/models/DataManageModel'
import { DataManageModels } from '@/api'
import { message } from 'ant-design-vue'
import { ref, watch } from 'vue'

const props = defineProps<{ articleId?: string, view?: boolean }>()

const visible = defineModel('visible', { default: false })

const [DefineTemplate, ReuseTemplate] = createReusableTemplate()

const article = ref<DataManageModel | undefined>()
const loading = ref(false)
const lang = ref<'zh' | 'en'>('zh')

watch(() => props.articleId, async (val) => {
  if (val && props.articleId) {
    loading.value = true
    article.value = undefined
    try {
      article.value = await DataManageModels.FindOneById_GetAsync({ id: props.articleId })
    }
    catch (e: any) {
      message.error(`获取文章详情失败: ${e.message || e}`)
    }
    finally {
      loading.value = false
    }
  }
}, { immediate: true })

function close() {
  visible.value = false
}
function onVisibleChange(val: boolean) {
  visible.value = val
}
</script>

<style scoped>
.lang-switcher {
  margin-bottom: 8px;
}
</style>
