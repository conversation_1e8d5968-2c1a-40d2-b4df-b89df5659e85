import { User } from "./User";
import { DataManageModel } from "./DataManageModel";
import { FeedbackType } from "./FeedbackType";
import { FeedbackStatus } from "./FeedbackStatus";
import { FeedbackResponse } from "./FeedbackResponse";
/**用户反馈模型*/
export class UserFeedback {
  feedbackUserId: GUID = "00000000-0000-0000-0000-000000000000";
  /**用户*/
  feedbackUser?: User | null | undefined = null;
  /**关联文章*/
  dataManageModelId?: GUID = null;
  /**智库文章*/
  dataManageModel?: DataManageModel | null | undefined = null;
  /**反馈类型*/
  type: FeedbackType = 0;
  /**问题描述*/
  description?: string | null | undefined = null;
  /**创建时间*/
  createdTime: Dayjs = dayjs();
  /**处理状态*/
  status: FeedbackStatus = 0;
  /**反馈回复*/
  responseId?: GUID = null;
  /**反馈处理记录*/
  response?: FeedbackResponse | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
