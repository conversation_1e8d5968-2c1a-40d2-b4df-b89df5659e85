<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: luckymiaow
-->
<!-- 搜索菜单 -->
<template>
  <div @click="selectMenu">
    <SearchOutlined />
  </div>
  <a-modal v-model:open="visible" :closable="false" :footer="null">
    <a-tree-select
      style="width: 100%" :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" placeholder="菜单搜索"
      show-search allow-clear tree-default-expand-all search-placeholder tree-node-filter-prop="select"
      :tree-data="_routerMenu" @select="treeChange"
    />
  </a-modal>
</template>

<script setup lang="ts">
import type { RouterItem } from '@/types/interfaces'
import { routerMenu } from '@/router'
import { useTabsStore } from '@/stores'
import { SearchOutlined } from '@ant-design/icons-vue'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'

const visible = ref(false)

const router = useRouter()

const tabStore = useTabsStore()

const _routerMenu = computed(() => {
  const fn = (routers: RouterItem[]): any => {
    return routers.map(e => ({
      label: e.meta?.title,
      value: e.path,
      children: !e.children || e.children.length === 1 ? [] : fn(e.children || []),
      data: e,
    }))
  }

  console.log('%c [ routerMenu.value ]-45', 'font-size:13px; background:pink; color:#bf2c9f;', routerMenu.value)
  return fn(routerMenu.value)
})

function selectMenu() {
  visible.value = true
}

function treeChange(newValue: string, data: { data: RouterItem }) {
  const route = data.data
  const tabsParams = {
    icon: route.meta?.icon,
    title: route.meta?.title,
    path: route.path,
    name: route.meta?.title,
    close: !route.meta?.isAffix,
  } as any
  tabStore.addTabs(tabsParams)
  router.push(newValue)
  visible.value = false
}
</script>

<style scoped></style>
