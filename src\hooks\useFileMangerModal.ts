import type { FileType, UploadFileInfo } from '@/api/models'
import FileManager from '@/components/FileManager/FileManager.vue'
import { Modal } from 'ant-design-vue'
import { h } from 'vue'

export function useFileMangerModal(callback: (files: UploadFileInfo[], type: FileType) => void, props: InstanceType<typeof FileManager>['$props'] = {}) {
  const modal = Modal.info({
    icon: null,
    class: 'file-manager-modal',
    width: '80%',
    style: { maxWidth: '1120px' },
    closable: true,
    title: '文件管理器',
    content: h(FileManager, {
      showSelectBtn: true,
      onSelected: (files, type) => {
        callback(files, type)
        modal.destroy()
      },
      ...props,
    }),
  })
  return { modal }
}
