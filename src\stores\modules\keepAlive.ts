import type { KeepAliveItem, keepAliveState } from '@/types/interfaces'
/*
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: luckymiaow
 */
import { defineStore } from 'pinia'

// useKeepAliveStore
export const useKeepAliveStore = defineStore({
  id: 'keep-alive',
  state: (): keepAliveState => ({
    list: [],
  }),
  actions: {
    // addKeepAliveName
    async addKeepAliveName(item: KeepAliveItem) {
      !this.list.some(e => e.path === item.path) && this.list.push(item)
    },
    // removeKeepAliveName
    async removeKeepAliveName(item: KeepAliveItem) {
      this.list = this.list.filter(e => e.path !== item.path)
    },
    // setKeepAliveName
    async setKeepAliveName(keepAliveName: KeepAliveItem[] = []) {
      this.list = keepAliveName
    },
  },
  persist: {
    storage: sessionStorage,
  },
})
