<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: luckymiaow
-->
<!-- 配置语言 -->
<template>
  <div>
    <a-dropdown placement="bottom">
      <TranslationOutlined />
      <template #overlay>
        <a-menu>
          <a-menu-item :disabled="language !== null && language === 'zh'" @click="handleSetLanguage('zh')">
            简体中文
          </a-menu-item>
          <a-menu-item :disabled="language === 'en'" @click="handleSetLanguage('en')">
            English
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores'
import { getBrowserLang } from '@/utils/util'
import { TranslationOutlined } from '@ant-design/icons-vue'
import { computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

const i18n = useI18n()
const globalStore = useAppStore()
const language = computed((): string => globalStore.language)

// 切换语言
function handleSetLanguage(lang: string) {
  i18n.locale.value = lang
  globalStore.updateLanguage(lang)
}

onMounted(() => {
  handleSetLanguage(language.value || getBrowserLang())
})
</script>

<style scoped></style>
