# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [0.1.24](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.23...v0.1.24) (2023-06-16)

### [0.1.23](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.22...v0.1.23) (2023-06-15)


### 🚀 Chore | 构建/工程依赖/工具

* **release:** 0.1.22 ([4ead91a](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4ead91aa9887404336e34e97b3fcc69375fba5c2))


### ✨ Features | 新功能

* 修复首次登录路由未加载 ([80df6ba](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/80df6ba68ec761d23951996f4776bc6d8c1c92b6))
* 优化ui ([672ffc8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/672ffc8d3bea2e4b3a1993870e2b729d25505025))
* fix ([b2fb2a0](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/b2fb2a0874e46a621fe6d1b0b6b9ab88bfb08be0))
* tag-bug ([f20b603](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/f20b60370653a8591315a2fde0405c1fc64e508c))


### 🐛 Bug Fixes | Bug 修复

* 被动tag改为主动tag ([a9ecce0](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a9ecce018b14a46a9773fb4084ddf21511e761d5))
* 部分问题 ([feba9ff](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/feba9ffa759fb9b4cb4392f58ec151cb6f91c4f6))
* 修复路由菜单问题 ([a692aeb](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a692aeb066059271a6d661fe5dd50fba93766e70))
* Merge ([4206687](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/420668786dc96143571561167baba9009f2885de))
* router-bug ([4602c05](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4602c05a0e7b286f57267f1628efede75220a9a1))
* signalR-bug ([ba19b96](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/ba19b9615fc5251f1fa7d6cfcd72b844948ab98c))

### [0.1.22](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.21...v0.1.22) (2023-06-09)


### ✅ Tests | 测试

* merge ([95078fd](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/95078fdc1fbe5855dbbfef07285c036e9f057fe7))
* merge ([bef853f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/bef853f4e5b05e0a6de7443b511eb45b1e4de4ea))
* pull ([4cd5c89](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4cd5c896d8794eb0242e7b667d5e07324215ae53))
* test ([dd821c9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/dd821c9468adbfcf5465fe3cf55e4d5ed292cac4))
* todolist ([6cd0cdb](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6cd0cdb61105a87d28cf11db7ef547683ee51941))
* todolist ([61bd4f1](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/61bd4f19bc0f963a9f1c9a6662af58c791bc118c))


### 🐛 Bug Fixes | Bug 修复

* 更新代码 ([d70c676](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d70c676d0fb79d5b248a6f7319bd7135a0247330))
* 更新用户管理和个人设置添加手机号码页面 ([2abf3d6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/2abf3d69ba64f9c561c49db9ef29ee3b1de9250c))
* 添加性能监控页面 ([65b73f4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/65b73f4aff256df70ea5811f6b88199069f9794f))
* fix ([1ef9e67](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1ef9e67b28fb8a6d9499fa35d7c5f1478a1e3216))
* fix ([a918426](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a91842632ffca52bff35da8a1728ed9a5232ffef))
* fix x ([4f95f67](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4f95f67d1b279fe0b558693c9eb158ace984ed83))
* fix: build ([c2dfdb3](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c2dfdb3c4f307cf210e37fef362a9490e890cb0d))


### ✨ Features | 新功能

* 登录页面 ([4b63a8e](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4b63a8eb34d71c14cd2b44301a3665d33fa3f540))
* 发布 ([1e8bfd5](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1e8bfd5d2760ce8b803b79a52f37c0e4922876f1))
* 发布 ([6861b3c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6861b3c787c67b23646aec3e9803212184656ba2))
* 开放面包屑 ([dd770ca](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/dd770cabc0b0fa0013c06cec20de7316df920ffb))
* 全新的主题 ([08e009b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/08e009b6c1def2d14a3131c1506cf675e4474e60))
* 优化路由 ([35b42e9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/35b42e9e130ff97995f499b9a3bfb61ecc02c534))
* 增加less变量提示器 ([743dd9d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/743dd9d3d2674ced9189e5a1f6b6296a1c658e27))
* 主题 ([75961ee](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/75961ee022864f1df4e7c06cb140fc3c78398ce7))
* 主题色 ([1b1de2e](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1b1de2e97f95de1befadbe2b6f42d018ca6a8493))
* 主题色合并 ([98341bf](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/98341bf33249908ba8feb83f25dd86b4ad141a01))
* merge ([98d9d3b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/98d9d3b2ed102e3fe7a5b5f2ada5c1b916c4117c))
* merge ([d903473](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d903473dae0a3ed241d38a2ac0b729acfdd10229))
* Merge ([0012a0a](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/0012a0aa079d8d75e26c71b8221af02fc6061b69))
* Merge ([154bc8d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/154bc8d215dc87d82672762773d6baf45527ee3c))
* omit ([3d3b824](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/3d3b8243beb02c11a2fe75cb826a12ceb33cfbb8))
* singnalr ([cab751d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/cab751d23f555b46f84064c9211644633ae29887))
* todo and api ([67fc642](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/67fc642adf0d610c97852b1f98bb6b06e0678d99))

### [0.1.21](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.20...v0.1.21) (2023-06-05)


### 🎉 Init | 初始化

* layout ([d625af3](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d625af363fefc28511a1e6ace9104704179042f5))


### ✅ Tests | 测试

* 按钮 ([59e3bcd](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/59e3bcd93ac2748351ad406e4cef6f241949914d))
* 按钮权限 ([15c32aa](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/15c32aa4ae23675ce61cc17b8c5a91541902d159))
* 按钮权限 ([fe37843](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/fe37843614eb49997aa8f811f85a3ca755c60fb2))
* 菜单搜索 ([3d70e79](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/3d70e794baaec633f5b98a2295153058c46c037a))
* 测试 ([f58957b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/f58957b2322c7ffd71e38655d5e8c9c415c92a57))
* 局部全屏 ([542974f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/542974ffd6f1ad65932c1b10e06e17dcbf69dfb3))
* 路由 ([9531ed2](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9531ed27e0fae1c08dd52f6588136edec5d14e85))
* 完善路由界面 ([cc539d6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/cc539d6f3983d84575fb1d06190ad8a50954f0b9))
* 完善用户管理页面与路由 ([11abb55](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/11abb55a0f466a3616d5ef0330cbc39f202c256c))
* 修复菜单栏 ([870559e](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/870559e878a2c0c80b63457987b572514c687452))
* api ([f30225f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/f30225fff75d5e9830cd85245b47625bb3418593))
* layout ([3b543d6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/3b543d6c6295299694f3cf912f8baf715795dafc))
* layout ([a2deffd](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a2deffd6fe58a34a33cc29c3df6466f0398ead7c))
* layout ([4a1b607](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4a1b607a61a814442eafa3dd8af102f5cbe52689))
* layout ([e41de5a](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/e41de5a0933cf7ad7200cc7787631a56cb74e7f9))
* layout ([386b8d1](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/386b8d15600c4df3bb4456daadbd865fae5acaf5))
* layout ([177b7d4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/177b7d415691f2a983ada256160a8715f8e80d95))
* route ([7174f3e](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/7174f3e0564cf71c7542c7d77e359110d9520e47))
* route ([1ae2e4d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1ae2e4d55d424af6fe5b874fadf10aaf89af8e7f))
* route ([7794a33](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/7794a33b1b4895d1cbbbccf5f7cb1a950446e7cd))
* route ([69c8f1b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/69c8f1bb2713264e9149a6e469c6e5cb981d1ff1))
* route ([060f1c7](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/060f1c729b492f4f811cbb2d5df588ec7ac106da))
* route ([ef5f8ef](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/ef5f8ef62a468c19f43056c68eae4933a68ee5be))
* tabs ([31d87ec](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/31d87ecab6b1cf9a0ee3239f8f2032b4fe8b6fa4))
* todo ([306bebd](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/306bebd3d4fa988a3d1dd5832983132f114b35d7))
* todolit ([85ccefe](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/85ccefedba4786dfaf1bb5c06f0aea30197df10d))
* user ([35104ab](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/35104ab5ab8a80ea3dc1be27d17e41d86984bbe9))


### ♻️ Code Refactoring | 代码重构

* 合并布局 ([7be85cf](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/7be85cf2353543c0de3c1367c1a693ea2a9689e2))
* route ([16f4a16](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/16f4a16a87eea5be2fcaaefcfe0d4cec79e685e2))


### 🐛 Bug Fixes | Bug 修复

* 更新表单设计，保存及赋值 ([b482800](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/b482800e6b30876b04c2b01e5d9799408c6dcf96))
* 更新代码设计的表单和后端首页 ([ec7dd5a](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/ec7dd5a6abec23e1b6bf8b9c5175712573a21f38))
* 更新后端模板 ([64a901b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/64a901bb031fdae2177e7188d2489d8c22f3ba64))
* 更新路由 ([58a8ef5](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/58a8ef5049f1a41e745364d8541324e8c979a2c4))
* 更新路由编辑 ([2b92aa6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/2b92aa6076b7df8d521f5d49d026bd15d4d2080f))
* 更新用户管理 ([25ac760](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/25ac760498f9faff99cdfde4074e11ebe89cbd3d))
* 更新api ([43275c8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/43275c8976db34ecfdb3e4f041ec69c158af555f))
* 合并 ([717dd69](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/717dd693edd28ffeab05fd3dc7f6e15b4eeacc5e))
* 添加个人中心和修改添加用户角色一点逻辑 ([71b1ae3](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/71b1ae365a01eb6360f7511a02c76a6a14f4fbf1))
* 添加角色管理 ([a051fba](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a051fba752049a4dd96231ebb4e567dd3016fa92))
* 系统管理添加几个无api的页面 ([f2d05ab](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/f2d05abce0ddc252b3e5e7946a2c9bf5ef159786))
* 修复角色管理及添加编辑 ([0c578ac](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/0c578aca682f44d0d1416699634fcad061b39efb))
* 修复依赖不兼容的问题 ([fecf2e2](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/fecf2e2e4d98a3ec22cf346f9662c2148d47a5cc))
* 修复用户修改密码和管理员修改密码 ([216e59d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/216e59d5e3affb44c731fc6136eb25358e060f81))
* 用户管理添加角色 ([edf6615](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/edf6615f023ff407fde3cad5363e61d5bd2d2bbf))
* end-update ([8878572](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/8878572a31f431c8057a3082f5ea5165722fd4e3))
* layout ([c358cba](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c358cba00a468c882fb1ff75ddb24743e2de331d))


### ✨ Features | 新功能

* 测试 ([d1169a0](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d1169a01ee547ec897e5482f03203c37d8fc2fea))
* 更新权限标签 ([9b3b403](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9b3b4032ceecf2a73a981046772d48de24897e78))
* 删除多余文件 ([b1b5eac](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/b1b5eac47b736e9c05399ef4918c3d9769955cc4))
* 添加顶部导航拖拽和右键菜单 ([7eb3c88](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/7eb3c88d1fb4415be9bff204976391aa1f933003))
* 添加注释 ([815e362](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/815e36270be162e68edc95d2d5c6623663a92464))
* layout ([7ac31b4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/7ac31b4c294f083da9b67b0f55701af78115121a))
* layout ([433b1fb](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/433b1fb87b3545d108d40b32b6d2f719bd3f25eb))
* service ([f2f65c4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/f2f65c42ae8a58ca9ac84a1f438ae48dc75bbb1a))

### [0.1.20](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.19...v0.1.20) (2023-05-06)

### [0.1.19](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.18...v0.1.19) (2023-05-06)

### [0.1.18](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.17...v0.1.18) (2023-05-06)

### [0.1.17](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.16...v0.1.17) (2023-05-06)

### [0.1.16](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.15...v0.1.16) (2023-05-06)

### [0.1.15](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.14...v0.1.15) (2023-05-06)

### [0.1.14](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.13...v0.1.14) (2023-05-06)

### [0.1.13](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.12...v0.1.13) (2023-05-06)

### [0.1.12](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.11...v0.1.12) (2023-05-06)

### [0.1.11](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.10...v0.1.11) (2023-05-06)

### [0.1.10](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.9...v0.1.10) (2023-05-06)

### [0.1.9](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.8...v0.1.9) (2023-05-06)

### [0.1.8](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.7...v0.1.8) (2023-05-06)

### [0.1.7](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.6...v0.1.7) (2023-05-06)

### [0.1.6](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.5...v0.1.6) (2023-05-06)


### ⚡ Performance Improvements | 性能优化

* 路由守卫：异步路由 ([885efc9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/885efc90661c0d9e0961986854aaecefc7624075))


### 🐛 Bug Fixes | Bug 修复

* 解决字段信息树结构增删改子节点信息bug ([9323e1e](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9323e1e3581562416a902dc8571d81ee86545c4c))
* 删除垃圾依赖 ([24487e4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/24487e4e2f068c01ed280c803aa87bedcd4f3130))
* 修复侧边栏路由 false VITE_APP_ROUTER_MODE ([95a417d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/95a417df14b1c1cca4503a4f7eb5ba35c210bf25))
* 字段显示条件判断 ([bf86a70](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/bf86a7090691c21ad3ec4cb08e1ae3e7254858e4))
* back ([1de86d5](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1de86d56e42b76fe70dfca950ec1d7a8a6b252ca))
* bug ([3bd3642](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/3bd3642f72172f21bb32a9dc6ca7f12e3da91482))
* com ([a0755ff](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a0755ff0119c92007414eb36ccd7db3e441c5585))
* fix 侧边栏闪烁 ([cb18fe9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/cb18fe9d58f6f40a74c4b496ab891d3c8198f3f9))
* fix:扫描注册路由 ([6520df2](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6520df28b2ed84aa01c434fce440691ddd6a3110))
* merge ([49d36da](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/49d36da22ef806a09dfc82c746b2a4a219783c25))
* test ([4057c23](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4057c230d19ef27e5ba2a22acc5146a6f7e0adbe))
* type ([4fdb7d6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4fdb7d6575eb5ef52d8a257e27470cd45486e677))


### ✨ Features | 新功能

* 编辑基础信息、字段信息table ([76be6d6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/76be6d6e1ccdb352e3971debafb2d6cf88a35c1e))
* 多用户 移除session文件 ([01a4554](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/01a4554661400fd50cbb878424e6e15ac610bfd2))
* 将vuex改为pinia ([dfdeb17](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/dfdeb17e1e0a977650d6496fdeccb1b901ca0da8))
* 添加后端模板列表 ([0ed3320](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/0ed3320927c81c8974113b3d46eda8b8b83acd8b))
* 添加验证码 ([3c02acc](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/3c02accc920567a50ff39c0f211beaf697f2b7c1))
* 限制eslin乱改 ([24d9138](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/24d913801c499d8e0d03585eb93048b308da976e))
* 行内编辑字段信息、添加子级字段信息 ([a66b540](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a66b540fdd8d670e3c7afa2a10f31378b19d43b5))
* 优化类型提示 ([edfed15](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/edfed15a617add577ee07517adae31ae840cb1c2))
* api ([bde4e11](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/bde4e11dc8bee78a2d643bad6f30c0ab92e2fd48))
* api ([b98750b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/b98750bc2951080be0c344cad7fd87dc8693b651))
* checkESlintPermission ([9fac96a](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9fac96a6adc070a9978e8180e405e2436c2d9539))
* error store active ([df9efb7](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/df9efb74674501423c67b58fd14297159518a78c))
* lint ([ef6060b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/ef6060b169b53d27aace02767843979bc89be001))
* merge ([cdcd7c8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/cdcd7c8f544b56e88c0920ba62c9cfc0141570eb))
* sign 优化登录逻辑 ([c1103db](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c1103db736c0a875ccdf84ca0ce64e27269f8e36))
* test ([7a47514](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/7a475146571cade43cb3c59db31f4b82c5b06332))
* test ([ced72b2](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/ced72b20b5f8e0712a5aa9fee3c88c801ea1c5f5))
* test ([cea4a49](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/cea4a493fc125ad142bb9f934f73c45711df80bf))
* test ([ad0deb0](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/ad0deb0ffea10025d922820931b2ac906d781657))
* VITE_APP_ROUTER_MODE ([aa018f4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/aa018f4ed3c828cf764f1d91881e5596e4ad1c68))

### [0.1.5](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.3...v0.1.5) (2022-12-20)


### 💄 Styles | 风格

* 切换commit工具 ([50b8b9c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/50b8b9c43e44f4331386e986951a14a13d33843d))
* style ([31dc694](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/31dc69433db65d9639fda646e8b51c44e7e305d2))


### 🚀 Chore | 构建/工程依赖/工具

* v0.1.4 ([73fb59a](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/73fb59a1441bb1c26f5c4144b547a357b355153e))


### ✨ Features | 新功能

* 查看json功能 ([146b751](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/146b751ddf8659a46635837dbc8f5f1dd137c8bf))
* 导出JSON文件 ([565fdee](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/565fdeeefbcf7730a01e7c76932405e4ff75e3c7))
* 更新代码 ([cd87d43](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/cd87d437445eee3b391ffa36f615e9748957c9ac))
* 更新复制代码和导出vue文件 ([445358b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/445358baef26111137b2e14cdca6cc451e31f3c5))
* 更新设计界面的右板块和数据类型 ([8792f0a](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/8792f0af2495f98db35d20ceefa438eeb5b469ec))
* 更新设计界面的组件和打开设计界面的方式 ([65b32f7](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/65b32f7587cc3f93067e6cc36deffb98183ea2d2))
* 更新输入型组件 ([c86fc86](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c86fc86a17d7acceb29e67034c6b93a393ee45bf))
* 更新组件文档指令 ([c2c19d9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c2c19d93b3aabd4c1878da36f104d698183395f0))
* 拉取代码 ([6c0da4c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6c0da4cdf123e38618c1d2a2fcceacb462e2121e))
* 拉取最新代码 ([c2c9c40](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c2c9c40f80ca6e16267b551a27c7ce2ef92263ac))
* 模板新增改查 ([4df7ccb](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4df7ccb140133e3f7f4171d9f179a5f191dafb61))
* 设计器列表页 ([e7986b4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/e7986b419e2582abd131e448cdfb0e7791b860d3))
* 设计器列表页 ([923de4b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/923de4bdde25411e8ac9db52b3f38a61ec62fe5b))
* 升级vite，更新组件库按需脚本 ([4e25fc7](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4e25fc7223226a115dd11d729bc05015d49399cb))
* 升级vite4 ([60c4dc3](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/60c4dc3de62809fa2b1d127ad70e5c59fe9bfcaa))
* 生成接口 ([7fbc3ad](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/7fbc3ad6bc8a8afafeb66e5640db63e07a7fdd42))
* 提交代码 ([9ab8ec2](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9ab8ec2c7d956ed6503847051f150e1ce607ea5e))
* 文件系统路由/约定式路由 ([cd1d217](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/cd1d217e908486c2de2c3d4204da1d424627ad32))
* 修复vuex存储问题 ([2253115](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/2253115bddd9e94439fa809644c86ad84af707de))
* 修改样式和组件类型 ([b4f18b8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/b4f18b8e97214df71f644250e180e7a032c021c3))
* 选中组件样式 ([66877f4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/66877f40ea1a09a78d8d38b22de481c3a47ddfb5))
* 优化查看json添加删除当前项 ([4de3ff9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4de3ff9752d42e67e51385a8f1d9fa3cf11cb046))
* 增加携带边框的表格 ([c160103](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c160103d7796c19c7500150008d0be2465d56d7c))
* 组件属性 ([756b156](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/756b156955f74b88af00af0b77263329fde52581))
* env ([0a664e7](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/0a664e75487acef297266dbe3bdb6576fe1b18f1))
* feature: ([831cce6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/831cce6d943928217796ce645f4ae032b1dacd88))
* genxing ([ddd09b8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/ddd09b8935c11d20c491345d121376523f85156e))
* Merge ([6e3d470](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6e3d47011847bf7eed60b2572c9befb151d7bc89))
* meta script ([9350ae7](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9350ae78aaf68aadf69d150e6d9bf15e3704d554))
* nginx ([be51180](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/be51180843b44a9e1b5119ef942bba003c848a17))
* vite test ([85dcae5](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/85dcae5ad033904e4384ff9c0bbffca6e47e70df))


### 👷 Continuous Integration | CI 配置

* ci ([42d826c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/42d826c2fbfb8d25e98de240e391ec3a2f78bde6))


### ⚡ Performance Improvements | 性能优化

* commder ([1f8bbb8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1f8bbb80abdfc5dbf1ee253053c37462212c2ceb))


### 🐛 Bug Fixes | Bug 修复

* 表单设计界面的整体样式和右板块的功能 ([22aa850](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/22aa850f0b43b394a59913bb87fc41fc6707eb9c))
* 冲突 ([0e46200](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/0e46200055268d00a84232c07ea1e103f35a3501))
* 冲突 ([eefe670](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/eefe67045ffa4a22a5346c15e73a785a7359db42))
* 复制JSON ([9130b22](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9130b222bd8ce19807fc0341e1387b51b8d2a80b))
* 更新报错 ([c01055f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c01055f645c0d894a7819b8a8471a8cfce329771))
* 更新表单设计 ([33ef595](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/33ef5958034ecdfc4507a293e8e30c6c0f2cccde))
* 更新表单设计，解决变量重复问题 ([f757e51](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/f757e5195424122d727d5ebef98dbbbcc348dd1e))
* 更新部分代码 ([4f6fcd8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4f6fcd8a465ea2dab23f24dbea7d574cfc2da1c3))
* 更新冲突 ([b161cd3](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/b161cd3ddbb47053333c92ea9771f2258c6eee7f))
* 更新代码 ([dfea17f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/dfea17f24fcd363907e9b54bd9e6b74e73888656))
* 更新依赖 ([d44ee1f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d44ee1f763434eed020555faec590ac8fd8fc6ca))
* 更新组件库 ([d60fc07](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d60fc07dfb781c801ad40f351056041aeaf49020))
* 更新组件库 ([2b57ea6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/2b57ea6f458b35d28fcbbe30548c4271d853ff49))
* 关闭清除表单项 ([744d514](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/744d5145f074ce9890f92eb167124e93d51331c1))
* 解决报错，修复复制代码功能 ([aa0758f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/aa0758f3db8dab754594871ea5850478f3cd5ad2))
* 解决冲突 ([78814c4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/78814c4f7cde5ead6c95fbd4d9893bc34f40f4bb))
* 解决冲突 ([feeaf45](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/feeaf45e63cab33e6fdb15a15b969b44fa2dc108))
* 解决冲突 ([8d38f6d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/8d38f6d6a397ec19589088423060d5934e2e810e))
* 解决冲突 ([6bb6d08](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6bb6d0867d43431036e7a6ab4efacb839d4ce7f8))
* 解决冲突 ([748df9b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/748df9b348ebe1851798774d2e5dd301853279ac))
* 解决冲突 ([51d15b6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/51d15b6673e63280779170edf4a80d2aa69a4217))
* 解决冲突 ([cafbe66](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/cafbe66b23c3648427a5276e509f1308c442cb49))
* 解决冲突 ([eaa73c8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/eaa73c891a26de2aca78dd06c56c9cfe3c956a2e))
* 解决冲突 ([bc33a31](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/bc33a3120d3b0f0844a64ca95dc5d3f78717773c))
* 解决冲突 ([a40d3d8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a40d3d8b8a1b3cf5c64f6a54f12a3bfaaa70f707))
* 解决冲突 ([03e4015](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/03e40158fff3ee605f86bef9eefbe94b42d9198a))
* 解决控制台报错 ([bffb8f9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/bffb8f9b024211bf4ada9253b6aa0b583827c996))
* 解决依赖报错 ([1354d9f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1354d9fdae4856e340ccbd2d7a5fc9dea12043ae))
* 解决依赖循环 ([d17ce92](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d17ce922eff4718656c3df59c9056a34f740880c))
* 解决eslint警告 ([c789c28](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c789c28e55b52336acde39924df1ee4a4609dd64))
* 解决eslint提示警告 ([1185b78](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1185b7861b875a8572aa59c9b63ec60a09c3c899))
* 拉取代码 ([c608cfd](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c608cfdf3ed29582b9c7f3d6445d1bba2758c4ab))
* 拉取最新代码 ([a53e364](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a53e364b0b0d660ce324859c53ed5c5f2224d935))
* 删除一些多余代码 ([9a6e4ef](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9a6e4ef14215fe483bce969f2dbb2d2aa83cdb86))
* 上传代码 ([00675e1](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/00675e192d6c83caeb92c63c5d1c342621b948a3))
* 设计表单界面添加组件属性 ([c0ca806](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c0ca8060c3e2fcfcfe575e7d2950f1b696feb209))
* 提交代码 ([85eb25f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/85eb25fdadd2d72c635937dd992252de1ef42d05))
* 添加枚举和button显示 ([906af2c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/906af2cbf74d3fe2973b167f4d6fdeb822894bb0))
* 添加拖拽 ([3a51439](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/3a514394d9d92caa991504afcaaa36c778eb44ac))
* 添加下拉选择和多选框组和开关的下拉渲染 ([394559d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/394559d2ae79a303b05e9a580229d29acd32aa1f))
* 添加自定义校验 ([983b94f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/983b94f0e4b63c218db8a62e75ea08bb48e848ca))
* 修改bug ([11a934a](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/11a934adcfa6f6d1ba0e3d50d80b8e348815856a))
* 预览 ([1932b18](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1932b1897966c94ec10cf64a39b3a0cda3fd0e9e))
* 预览添加页面和弹框两种模式 ([f3e62b9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/f3e62b9d4c2cc04dfbefb381822ec3cc912e6b5a))
* 只读表单的生成代码 ([5db6fbc](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/5db6fbcb723b1167800649a57eda1fa4975a5da3))
* eslin: export default ([6bb2e38](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6bb2e38700357e3f405c7bea19c201dab0128980))
* fix copyCode ([d261ab6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d261ab648b113b6673b938ac4210d19d7bd39751))
* fix userModule await ([4896dba](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4896dba99949d6a2e2f99a07628b1a829797d42b))
* merge ([49bba63](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/49bba63776feea2d375ec41f2f8d23af708b2a7b))
* Merge branch ([06efaaa](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/06efaaa8e6b8e60d1984f201519069e62fc94165))
* Merge branch ([aac5b24](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/aac5b24c67da33196f74f56a1ac501ba9e85192d))
* script ([a8b36ab](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a8b36abc9993798b348e2db8d7fee274d8bc7137))
* type ([46e3711](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/46e37118b3a81b66acdeedf6728b402867c6fe91))
* xx ([47fb223](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/47fb22305c807a20a399936765ac679ec7c06115))

### [0.1.4](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.3...v0.1.4) (2022-12-20)

### 💄 Styles | 风格

- style ([31dc694](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/31dc69433db65d9639fda646e8b51c44e7e305d2))
- 切换 commit 工具 ([50b8b9c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/50b8b9c43e44f4331386e986951a14a13d33843d))

### 🐛 Bug Fixes | Bug 修复

- fix copyCode ([d261ab6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d261ab648b113b6673b938ac4210d19d7bd39751))
- fix userModule await ([4896dba](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4896dba99949d6a2e2f99a07628b1a829797d42b))
- merge ([49bba63](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/49bba63776feea2d375ec41f2f8d23af708b2a7b))
- script ([a8b36ab](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a8b36abc9993798b348e2db8d7fee274d8bc7137))
- type ([46e3711](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/46e37118b3a81b66acdeedf6728b402867c6fe91))
- 上传代码 ([00675e1](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/00675e192d6c83caeb92c63c5d1c342621b948a3))
- 修改 bug ([11a934a](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/11a934adcfa6f6d1ba0e3d50d80b8e348815856a))
- 关闭清除表单项 ([744d514](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/744d5145f074ce9890f92eb167124e93d51331c1))
- 冲突 ([0e46200](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/0e46200055268d00a84232c07ea1e103f35a3501))
- 冲突 ([eefe670](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/eefe67045ffa4a22a5346c15e73a785a7359db42))
- 删除一些多余代码 ([9a6e4ef](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9a6e4ef14215fe483bce969f2dbb2d2aa83cdb86))
- 只读表单的生成代码 ([5db6fbc](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/5db6fbcb723b1167800649a57eda1fa4975a5da3))
- 复制 JSON ([9130b22](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9130b222bd8ce19807fc0341e1387b51b8d2a80b))
- 拉取代码 ([c608cfd](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c608cfdf3ed29582b9c7f3d6445d1bba2758c4ab))
- 拉取最新代码 ([a53e364](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a53e364b0b0d660ce324859c53ed5c5f2224d935))
- 提交代码 ([85eb25f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/85eb25fdadd2d72c635937dd992252de1ef42d05))
- 更新代码 ([dfea17f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/dfea17f24fcd363907e9b54bd9e6b74e73888656))
- 更新依赖 ([d44ee1f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d44ee1f763434eed020555faec590ac8fd8fc6ca))
- 更新冲突 ([b161cd3](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/b161cd3ddbb47053333c92ea9771f2258c6eee7f))
- 更新报错 ([c01055f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c01055f645c0d894a7819b8a8471a8cfce329771))
- 更新组件库 ([d60fc07](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d60fc07dfb781c801ad40f351056041aeaf49020))
- 更新组件库 ([2b57ea6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/2b57ea6f458b35d28fcbbe30548c4271d853ff49))
- 更新表单设计 ([33ef595](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/33ef5958034ecdfc4507a293e8e30c6c0f2cccde))
- 更新表单设计，解决变量重复问题 ([f757e51](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/f757e5195424122d727d5ebef98dbbbcc348dd1e))
- 更新部分代码 ([4f6fcd8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4f6fcd8a465ea2dab23f24dbea7d574cfc2da1c3))
- 添加下拉选择和多选框组和开关的下拉渲染 ([394559d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/394559d2ae79a303b05e9a580229d29acd32aa1f))
- 添加拖拽 ([3a51439](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/3a514394d9d92caa991504afcaaa36c778eb44ac))
- 添加枚举和 button 显示 ([906af2c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/906af2cbf74d3fe2973b167f4d6fdeb822894bb0))
- 添加自定义校验 ([983b94f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/983b94f0e4b63c218db8a62e75ea08bb48e848ca))
- 表单设计界面的整体样式和右板块的功能 ([22aa850](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/22aa850f0b43b394a59913bb87fc41fc6707eb9c))
- 解决 eslint 提示警告 ([1185b78](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1185b7861b875a8572aa59c9b63ec60a09c3c899))
- 解决 eslint 警告 ([c789c28](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c789c28e55b52336acde39924df1ee4a4609dd64))
- 解决依赖循环 ([d17ce92](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d17ce922eff4718656c3df59c9056a34f740880c))
- 解决依赖报错 ([1354d9f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1354d9fdae4856e340ccbd2d7a5fc9dea12043ae))
- 解决冲突 ([78814c4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/78814c4f7cde5ead6c95fbd4d9893bc34f40f4bb))
- 解决冲突 ([feeaf45](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/feeaf45e63cab33e6fdb15a15b969b44fa2dc108))
- 解决冲突 ([8d38f6d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/8d38f6d6a397ec19589088423060d5934e2e810e))
- 解决冲突 ([6bb6d08](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6bb6d0867d43431036e7a6ab4efacb839d4ce7f8))
- 解决冲突 ([748df9b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/748df9b348ebe1851798774d2e5dd301853279ac))
- 解决冲突 ([51d15b6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/51d15b6673e63280779170edf4a80d2aa69a4217))
- 解决冲突 ([cafbe66](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/cafbe66b23c3648427a5276e509f1308c442cb49))
- 解决冲突 ([eaa73c8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/eaa73c891a26de2aca78dd06c56c9cfe3c956a2e))
- 解决冲突 ([bc33a31](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/bc33a3120d3b0f0844a64ca95dc5d3f78717773c))
- 解决冲突 ([a40d3d8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a40d3d8b8a1b3cf5c64f6a54f12a3bfaaa70f707))
- 解决冲突 ([03e4015](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/03e40158fff3ee605f86bef9eefbe94b42d9198a))
- 解决报错，修复复制代码功能 ([aa0758f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/aa0758f3db8dab754594871ea5850478f3cd5ad2))
- 解决控制台报错 ([bffb8f9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/bffb8f9b024211bf4ada9253b6aa0b583827c996))
- 设计表单界面添加组件属性 ([c0ca806](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c0ca8060c3e2fcfcfe575e7d2950f1b696feb209))
- 预览 ([1932b18](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1932b1897966c94ec10cf64a39b3a0cda3fd0e9e))
- 预览添加页面和弹框两种模式 ([f3e62b9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/f3e62b9d4c2cc04dfbefb381822ec3cc912e6b5a))

### ✨ Features | 新功能

- feature: ([831cce6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/831cce6d943928217796ce645f4ae032b1dacd88))
- genxing ([ddd09b8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/ddd09b8935c11d20c491345d121376523f85156e))
- Merge ([6e3d470](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6e3d47011847bf7eed60b2572c9befb151d7bc89))
- meta script ([9350ae7](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9350ae78aaf68aadf69d150e6d9bf15e3704d554))
- vite test ([85dcae5](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/85dcae5ad033904e4384ff9c0bbffca6e47e70df))
- 优化查看 json 添加删除当前项 ([4de3ff9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4de3ff9752d42e67e51385a8f1d9fa3cf11cb046))
- 修复 vuex 存储问题 ([2253115](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/2253115bddd9e94439fa809644c86ad84af707de))
- 修改样式和组件类型 ([b4f18b8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/b4f18b8e97214df71f644250e180e7a032c021c3))
- 升级 vite，更新组件库按需脚本 ([4e25fc7](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4e25fc7223226a115dd11d729bc05015d49399cb))
- 升级 vite4 ([60c4dc3](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/60c4dc3de62809fa2b1d127ad70e5c59fe9bfcaa))
- 增加携带边框的表格 ([c160103](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c160103d7796c19c7500150008d0be2465d56d7c))
- 导出 JSON 文件 ([565fdee](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/565fdeeefbcf7730a01e7c76932405e4ff75e3c7))
- 拉取代码 ([6c0da4c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6c0da4cdf123e38618c1d2a2fcceacb462e2121e))
- 拉取最新代码 ([c2c9c40](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c2c9c40f80ca6e16267b551a27c7ce2ef92263ac))
- 提交代码 ([9ab8ec2](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9ab8ec2c7d956ed6503847051f150e1ce607ea5e))
- 文件系统路由/约定式路由 ([cd1d217](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/cd1d217e908486c2de2c3d4204da1d424627ad32))
- 更新代码 ([cd87d43](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/cd87d437445eee3b391ffa36f615e9748957c9ac))
- 更新复制代码和导出 vue 文件 ([445358b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/445358baef26111137b2e14cdca6cc451e31f3c5))
- 更新组件文档指令 ([c2c19d9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c2c19d93b3aabd4c1878da36f104d698183395f0))
- 更新设计界面的右板块和数据类型 ([8792f0a](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/8792f0af2495f98db35d20ceefa438eeb5b469ec))
- 更新设计界面的组件和打开设计界面的方式 ([65b32f7](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/65b32f7587cc3f93067e6cc36deffb98183ea2d2))
- 更新输入型组件 ([c86fc86](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c86fc86a17d7acceb29e67034c6b93a393ee45bf))
- 查看 json 功能 ([146b751](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/146b751ddf8659a46635837dbc8f5f1dd137c8bf))
- 模板新增改查 ([4df7ccb](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4df7ccb140133e3f7f4171d9f179a5f191dafb61))
- 生成接口 ([7fbc3ad](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/7fbc3ad6bc8a8afafeb66e5640db63e07a7fdd42))
- 组件属性 ([756b156](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/756b156955f74b88af00af0b77263329fde52581))
- 设计器列表页 ([e7986b4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/e7986b419e2582abd131e448cdfb0e7791b860d3))
- 设计器列表页 ([923de4b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/923de4bdde25411e8ac9db52b3f38a61ec62fe5b))
- 选中组件样式 ([66877f4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/66877f40ea1a09a78d8d38b22de481c3a47ddfb5))

### [0.1.3](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.2...v0.1.3) (2022-11-17)

### ✅ Tests | 测试

- ppp ([55c051c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/55c051cf9153bf87fd912fbb5e688594ec7d8bb4))
- ppp ([a876380](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a8763801b372bf2c004030dc6fa239ba58d1efcd))

### ✨ Features | 新功能

- 更新校验错误 ([5ff3400](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/5ff340041ec79d862c683b89a5ee75ac7a813682))

### [0.1.2](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.1...v0.1.2) (2022-11-14)

### 💄 Styles | 风格

- remove less lint ([68b75b2](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/68b75b267ba79e3b9b5e25f1059f9fba2fa8d0da))
- update lint ([1eecbdf](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1eecbdff229f2f11957ef7638a4d7436a9a8717f))

### [0.1.1](https://git.fastoa.co/dev-nn/ch2-template-vue/compare/v0.1.0...v0.1.1) (2022-11-10)

### ⚡ Performance Improvements | 性能优化

- per cz config ([ab31931](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/ab319318bb57bc9b9433d6718d2b088788b7b1ea))

### ✨ Features | 新功能

- dayjs ([eb9f593](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/eb9f5930d2cb60f7b08c90afa415873d3d50fb8f))

### 🐛 Bug Fixes | Bug 修复

- 更新路由信息 ([fbedb07](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/fbedb07eb4062f5ad834441c423f65f6efcc6c02))

### 🚀 Chore | 构建/工程依赖/工具

- add standard-version && CHANGELOG ([9ea7260](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9ea726061a8cc415626b012bf5bac06212ea3dfc))
- lint-staged 检查暂存区文件风格 ([aed2ef2](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/aed2ef2f57024bcc519eb297c001f73604a12178))

### ♻️ Code Refactoring | 代码重构

- 强类型兼容 ([4fa5d4d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4fa5d4d3cf9a8d18e2efb0525ad0acdff2231548))

## 0.1.0 (2022-08-18)

### 💄 Styles | 风格

- 修复路由注册 ([69f70c7](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/69f70c7f98d23ab28254e507e63b2d9ec03f33f5))

### ✏️ Documentation | 文档

- update select doc ([50cd9f3](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/50cd9f3e768972cbb70bc0d1d5aeb04dfe96f00a))
- update select doc ([cd22e21](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/cd22e212c9395077ad833c24be9147c17fc4466d))
- update upload doc ([03f3ba4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/03f3ba466fc4d08c7e82e06401cafc9e722101b0))
- upload 文档 ([18e1a05](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/18e1a0556703eb6e35b1bf9dd14300d57f70aa31))
- 合并 select 选择器 ([329b78e](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/329b78ed0b4c8e55d608136b47f22e516ab7c80f))
- 增加选项无内容时的情况 ([4ddba20](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4ddba201202dede6573348dc14daab2d7c705cb4))
- 更新选择器只读模式下获取插槽失败 bug ([f96fd8d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/f96fd8df98fc5da54b8d4a31efe83f5532db9b1a))

### ♻️ Code Refactoring | 代码重构

- 切换 vite ([3f0a2ef](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/3f0a2ef235c551018db9d555bd1a443db5ec8c07))
- 增加路由菜单、更新请求拦截、更新模拟登录、更新路由拦截 ([611c2f6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/611c2f6055941ecf102879cf2c21327578f4f623))
- 更改全局配置注入模式 ([64da9fe](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/64da9fe42975292fcda45b55fe33517f7a2788f3))
- 更改组件导入模式 ([0299706](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/0299706f03868cc7154168ef32e10c9fc0c2aae2))
- 更改路由导入方式 ([daa4021](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/daa4021c03f148babc5c0b205354328e37d2629b))
- 更新用户登录模块 ([5ee793c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/5ee793c4b07ae75587dc6517ed1ce288b30c5077))

### 🐛 Bug Fixes | Bug 修复

- '改进编辑用户信息' ([147927d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/147927d73a57bb29249c6f995ba84c613454d49d))
- '改进编辑用户信息' ([d459f29](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d459f29105a577aa5c0b691c8852d239aa6667ed))
- '补充座机号正则校验及调整个人信息和密码代码结构' ([b3fb8f2](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/b3fb8f2be5a4f41cd47bf23344d1aaa86b4e71eb))
- '阿斌编写修改密码、修改信息' ([880ea7c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/880ea7c7fdc492c253e97e9f1fdc4baa9547cdc1))
- fix data-picker ([e504d3a](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/e504d3a1a92b6be9fa641cd03e253e6a7b6343b5))
- fix error and git config core.ignorecase false ([b28148d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/b28148dff3da044813defecec62fa79cfc5568fe))
- fix SelectPaged ([9f5f7dc](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9f5f7dc7b7c4440c7d5828398c3872ae2963e540))
- lv ([73102b6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/73102b6cf904493bc13f47f7042428c4af32854b))
- pull ([04b0772](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/04b07722d15a059343f3751d63f323923c697e87))
- 优化 input 只读模式 ([7008802](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/70088027fa8157e652b08c019a0cca19acf7a304))
- 修复 label-in-value 模式删除报错 数据统一转换 ([6a4de96](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6a4de96d9b2d92c1032d40315c8a9335d9915253))
- 修复冲突 ([6ed2a72](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6ed2a7215a2182512cb951b16be59b3e4b025031))
- 修复头像显示 word 文档 ([9a67b71](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9a67b7113a4a1da6edcfe52d9367b370f1a883c9))
- 修复角色权限 ([92c27e9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/92c27e9358943fda11b1da9fa9df543a604d71b2))
- 修复角色权限 ([851baf6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/851baf61627c842f20d3aa3b767558637b5129db))
- 修复路由注册 ([01a9b88](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/01a9b8875784da8191b12f25ce0a7c1333f88247))
- 修复路由注册 ([e39a060](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/e39a060b3df57364aef5b1c182d6505100efd083))
- 修复路由注册 ([100c7b2](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/100c7b2c893eca9064ee5ca72a9c105ba3182076))
- 修复选择器插槽失效 ([2b5ce15](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/2b5ce159317844538d9a5f6af0489acb68c012de))
- 修改 ([a76e8d8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a76e8d8f67ac3c7d76e74dabbf497b0f1ad70047))
- 修改组件文件夹 ([aff47ad](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/aff47ade439cf5be888e01b9d5d049d5d1ed7d71))
- 修改组件文件夹 ([9c0b9f7](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9c0b9f7c7debe31d18a1919bbafa6aa16c62fe84))
- 修改组件文件夹 ([ce1c4d8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/ce1c4d866ad989e5e526119db571d2b27867a8c1))
- 去掉 fieldNames 默认值 fix 节点渲染 ([d8faa76](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d8faa76ec643b2882a164c898b04c45737d2da1b))
- 文本超长省略 ([769a7d1](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/769a7d1baf0cd676daedbf6244acf8a32510ad14))
- 更新 api ([3d3926f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/3d3926fa3c210bb50831af0da9f0768e6a5049da))
- 更新 model ([ad93246](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/ad93246e171337bf6a0f53f66b7ac2949788cd9d))
- 更新 select 显示值 ([a6f2596](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a6f25964fea21646ae387caa300e8a55a9c33a16))
- 更新树组件 ([34c07a1](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/34c07a1b370e20f4b38c1baa34d96f8b79ddb0db))
- 更新用户管理 ([492d2fe](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/492d2fe661c3f0226b95601fbea115f32c629a89))
- 更新路由 model ([2103b61](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/2103b61cdac0d54100b4b255f547eea60d3ce3b2))
- 更新路由管理 ([8b2ad7c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/8b2ad7c5919ce78c32cb5da8b56b8bc909f00a09))
- 更新路由管理 ([79452e5](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/79452e517cd5ba333728e0a653f7b80d4eff070c))
- 更新路由管理 ([1e21ff2](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1e21ff2ba609ce374d2e911fd6ca0527e2f01ad8))
- 更新路由管理 ([8a0b228](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/8a0b2286def75268a1b913571e7c429b849cd6a1))
- 更新路由管理 ([6a70c19](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6a70c19c1bb9b830ebbac11831c2d826c3e2e0ff))
- 更新路由管理 ([45b5486](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/45b5486fa2d333d3d88aefcd6363a966a506b559))
- 更新路由管理 ([9f493b6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9f493b68be1b2f0389ac915d230d55fb4368b610))
- 更新路由管理 ([eda9d7f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/eda9d7f183278f63aedf5ab6bb964518cbd72cd4))
- 更新路由管理 ([3acee35](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/3acee3576dc69faa751d2b5b94a73db410821876))
- 更新路由管理 ([6ceed19](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6ceed19b13ba1e61be5c78994936ec2cda4b4c43))
- 更新路由管理 ([bbc2896](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/bbc2896cc62c892632f0d7c73baa979d095198e7))
- 更新选择器只读模式下获取插槽失败 bug ([0139791](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/0139791b1e14e1392881381950d570515416f9ec))
- 添加用户信息添加角色 ([250bcd8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/250bcd84a4bd24b4d6230d80bbf55b348e8671ce))
- 用户管理 ([4aa58fc](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4aa58fc0c72f5d75fc199c5cd68aca36376c5469))
- 系统角色下拉框 ([679b7de](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/679b7de57895fdbbc6e50e4076514cae863deb53))
- 系统角色下拉框 ([94c58ae](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/94c58ae9c705e5f72b63042a76b2d636c8fa4d74))
- 调整登录问题 ([4ae2f99](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4ae2f99d04fe1645162c4f8bca0bed077c5e1e24))
- 路由管理图库选择与搜索 ([cabf76a](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/cabf76abb7bbcf4d0935e1a1d3abdbb93b5616d4))
- 路由管理图库选择与搜索 ([eebf135](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/eebf135454ed7a8b4966afb6c8068de3b4fb4e79))
- 路由进不去，设置静态访问 ([4f8821b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/4f8821bcbb62411997dc09df1e9aa2d1c6c5726a))

### ✨ Features | 新功能

- '阿斌写修改个人信息、密码' ([49e578e](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/49e578e541f6c891550ec55b39fb40e4607aaa26))
- add highlight ([adc0ac2](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/adc0ac230155d5287dcf72beeee1249df9061e77))
- add 金额转换器 ([ed56449](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/ed56449937c3cd0a16219055cc67ee8352fc7542))
- dateTime ([fc763ad](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/fc763ade7705ad2c624868123df25fe8885463e5))
- gzip ([fc06289](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/fc06289c26fbc38bf1b40bb7b852c3152935d6df))
- switch ([a375f2b](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a375f2b2f12cfb4308575243bee180788aaddcb0))
- Upload ([2b6886d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/2b6886d6100ba3be3d1b42018e9cc94d1bd83b58))
- viet-config ([5a8522c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/5a8522c8ab6889e42a7ff63491867e9078c95aee))
- 优化树形组件，增加字段替换编辑 ([e4000d9](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/e4000d914bd75587c7ec73fa0e5bc520cd419a3d))
- 去除选择框的属性继承 ([c4a1798](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c4a179801618f60ce51510e639bc0ca16dea57bc))
- 同步 checkbox 公共组件 ([6301f19](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6301f1959f8c35392cb3273f99fc097e50c8fd80))
- 增加 form 只读 hook ([8473967](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/8473967e204a94d060b62c95058fc1291ba29a96))
- 增加 slot hooks ([e59e02e](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/e59e02eed1e8995c584de38cce6b03a7d8985aec))
- 增加 uploadComposition UploadDragger UploadModal 全局配置注入 ([0001706](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/0001706b989a94b477c9035e17d85f8a7f0ac5fe))
- 增加全局配置注入类型限制、增加表单组件，增加表单配置注入、增加文本输入框组件、增加数字输入框组件 ([a2093a4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a2093a4222c0e56ae8c2040908d199c9d766fdee))
- 增加单选框组件 ([00bea40](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/00bea40df7b2f953ed01d688596cb54f820d70dc))
- 增加基础树型组件测试用例 ([6bae5b4](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6bae5b4715ffca02bfc782c67a96044f2a80b92a))
- 增加富文本选择器 ([1ed8a53](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/1ed8a53d56d91c3633cd4eb28632a90a49043244))
- 增加弹窗组件、并增加表格组件中非 api 数据源 ([16383fe](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/16383fe8370fb17a702381eaf1f3e1989de6a70b))
- 增加标准 select 选择器 ([c0b5a39](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/c0b5a39580e8e937f0252bbe9da163b62d125503))
- 增加标准管理数据源编辑 ([a689ac0](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/a689ac0593a3993c448dc752f457c1afb33f6b25))
- 增加树形基础组件 ([01fefbd](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/01fefbd12467b5cab0b37cfeda21fa45f27447c4))
- 增加树形组件 ([912c1c6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/912c1c6c43b9ffd12bf6baed07ad92ce51bb461a))
- 增加组件注册文件生成脚本 ([b83aa94](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/b83aa9455e41a131f6b3e7f10f1c0fe23ce5d8b7))
- 增加表单组件、统一表单写法 ([0a5be63](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/0a5be6318b2bff0e7a2474906146fd6559c94734))
- 增加表格组件 ([b2357f6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/b2357f6ae66ab53fbe196325c6df74fb3015b71a))
- 增加表格组件非分页接口的情况 ([d50ea8d](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/d50ea8d6b246ada86edd2185601e8f570b8a9ff5))
- 增加选项无内容时的情况 ([e641687](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/e641687c91d5e9f92420e7cc4e032c1990968117))
- 完善 select 选择器的只读模式、增加枚举选择器 ([5aba9f6](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/5aba9f614017150caac2def8e50edfb738473d02))
- 完成 GenPage V1 丐版 ([ba59047](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/ba59047cbc847e647e76822b4de3dc66cfc3058a))
- 文档修改样式 ([efab5a0](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/efab5a03dbbc3ab2d8e3e27dd0f8e8dab6bf374d))
- 新增 Ellipsis IOpenLink ([67fba85](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/67fba85d5c05cab237ee5b1ad44eb45a9b6ed1aa))
- 更换组件库 ([22db282](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/22db2827bf2a4ca1d8ffbf6913fd952e2fe99d85))
- 更改注入 ([24013e8](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/24013e87113a5225cb35a6c3876d66be2be0165f))
- 更新 input money 组件 ([35031ee](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/35031eec4e9888f88ee686a840f70fe6eab1ca9e))
- 更新组件模式 ([b316956](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/b31695690672e97df602308cd22be739e7d361e2))
- 更新路由管理 ([5f3ced5](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/5f3ced514991a9224a45186e9c3bc0ce513c5532))
- 更新路由管理 ([e284341](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/e284341313b0fc5862ab023f28779dc60abbcb10))
- 更新路由管理 ([963d83e](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/963d83ef1cb3abab4ea6b3cb03a1254133e1a3a7))
- 更新配置文件 ([00f292c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/00f292c54a2921f1a70891ec47d41418bbef9759))
- 标准选择器默认值设置 ([28457de](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/28457de26e1c8d4aa87a35c87347ad18f2008c0d))
- 添加 BooleanSelect ([e769e4a](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/e769e4a1ac5f045ced8113a06e0124536edad471))
- 添加 GenPge 生成页面 ([819a90c](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/819a90c106fe96fc8328be53ff7e2a72ca7b3247))
- 添加\_SelectBasic \_SelectApi ([9a2a5c2](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/9a2a5c2b44e7e4ac378141fd06c38c04be1303f2))
- 添加 c-input password 组件 ([f2963af](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/f2963af2d8af6f430e4c810e5503074cf5524665))
- 添加 TextEllipsis 收缩文本显示 ([48d1631](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/48d1631e66a9d16b7099170a996e3590e69e0af9))
- 添加头像显示及上传组件 ([654d62e](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/654d62e0c39d51c3b34747ebd3727a3d25c706e0))
- 路由管理 ([6b6b3d1](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/6b6b3d199c09098728e878157bc989379f92b5ce))
- 路由管理图库选择与搜索 ([422e40f](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/422e40f5d4f2a594b4351480bde2524402adbbcc))
- 配置全局样式 ([21a8eea](https://git.fastoa.co/dev-nn/ch2-template-vue/commit/21a8eeaae96d8cc94c0bb760447168b7fda3dd5b))
