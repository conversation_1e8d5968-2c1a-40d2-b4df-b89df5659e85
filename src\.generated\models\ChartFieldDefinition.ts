import { ChartFieldType } from "./ChartFieldType";
/**图表数据集字段定义*/
export class ChartFieldDefinition {
  /**字段名（key）*/
  name?: string | null | undefined = null;
  /**字段类型*/
  type: ChartFieldType = 0;
  /**字段单位（可选）*/
  unit?: string | null | undefined = null;
  /**备注说明*/
  description?: string | null | undefined = null;
  /**是否可编辑，默认为 true*/
  isEdit: boolean = false;
  /**是否是指标，指标模式下可用*/
  isIndicator: boolean = false;
}
