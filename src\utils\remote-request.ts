/*
 * @Descripttion:
 * @Author: <PERSON><PERSON>
 * @Date: 2021-03-19 10:36:21
 * @LastEditors: luckymiaow
 * @LastEditTime: 2023-07-10 11:50:48
 */

import type { AxiosRequestConfig, AxiosResponse } from 'axios'
import type { App } from 'vue'
import { apiOptions } from '@/.generated/apis'
import router from '@/router'
import { whiteListTest } from '@/router/utils/whiteList'
import { message, Modal, notification } from 'ant-design-vue'
import axios, { CanceledError } from 'axios'
import { useUserStore } from '../stores/modules/user'

// axios.defaults.baseURL = import.meta.env.VITE_APP_PROXY_TARGET

const instance = axios.create({
  timeout: 50000,
})

export async function request<TData, TResult>(options: AxiosRequestConfig<TData>): Promise<TResult> {
  const { token: getToken } = useUserStore()

  const headers = { ...(options.headers || {}) }
  if (getToken)
    headers.Authorization = `Bearer ${getToken}`

  if (!options.params) {
    options.params = {}
  }
  if (!options.params.timeStamp) {
    options.params.timeStamp = Date.now().toString()
  }
  const result = await instance.request<TData, AxiosResponse<TResult>>({
    ...options,
    headers,
    withCredentials: false,
  })

  if (options.responseType === 'blob')
    return result as any

  const { data } = result as any

  // if (data.data) return data.data
  return data
}

const messageOpen = ref(false)

function errMessage() {
  if (messageOpen.value === true)
    return
  messageOpen.value = true
  if (!whiteListTest(router.currentRoute.value?.path)) {
    Modal.info({
      content: '您的登录凭证已经过期，请重新登录',
      title: '重新登录',
      okText: '重新登录',
      onOk: () => {
        messageOpen.value = false
        router.push(`/logoutReload?redirect=${router.currentRoute.value?.fullPath}`)
        Modal.destroyAll()
      },
    })
  }
}

// respone 拦截器
instance.interceptors.response.use(
  async (response) => {
    const { data } = response
    if (data.code === 401) {
      errMessage()
    }
    if (Object.keys(data).includes('code')) {
      if (data.code === 400) {
        let msg = data.message
        if (data.message === 'Invalid ModelState') {
          msg = Object.values((data as any).modelState).flatMap(v => v).join('、')
        }
        else if (data?.code === 400 && data.data) {
          msg = Object.values(data.data).join(' or ')
        }
        message.error(msg)
        throw new Error(data.message)
      }
      else if (data.code !== 200) {
        console.log('服务器操作错误：', data.data)
        notification.error({
          message: '服务调用失败',
          description: `错误代码：${data.code}，异常消息：${data.message}`,
        })
        throw new Error(data.message)
      }
    }
    return response
  },
  (error) => {
    let msg = error.toString()
    if (error.response) {
      console.log('API ERROR error.response:', error.response)

      switch (error.response.status) {
        case 401:
          errMessage()
          break
        case 403:
          msg = `code=403: ${error.response.data.message}`
          message.error(msg)
          break
        case 500:
          msg = `code=500: 服务器错误:${error.response.data} `
          console.log(msg, error)
          message.error(msg)
          break
        default:
          msg = `code=${error.response?.status || 400}: ${error.response.data.message}`
          message.error(msg)
      }
    }
    else {
      console.log('API ERROR:', error)
      if (error instanceof CanceledError) {
        console.log('请求被取消')
      }
      else {
        Modal.confirm({ content: `错误:response=${error}`, title: '错误' })
      }
    }
    return Promise.reject(msg)
  },
)

export default {
  install(_app: App<Element>) {
    apiOptions.request = request
  },
}
