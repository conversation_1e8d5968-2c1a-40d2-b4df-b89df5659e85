import { FeedbackType } from "./FeedbackType";
import { FeedbackStatus } from "./FeedbackStatus";
export class UserFeedbackPageView {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  /**文章id*/
  dataId?: GUID = null;
  title?: string | null | undefined = null;
  /**反馈类型*/
  type: FeedbackType = 0;
  /**问题描述*/
  description?: string | null | undefined = null;
  /**创建时间*/
  createdTime: Dayjs = dayjs();
  /**处理状态*/
  status: FeedbackStatus = 0;
  /**回复内容*/
  response?: string | null | undefined = null;
  feedbackUserId: GUID = "00000000-0000-0000-0000-000000000000";
  feedbackUserName?: string | null | undefined = null;
}
