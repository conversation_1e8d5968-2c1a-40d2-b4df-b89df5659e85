import { FieldDataType } from "./FieldDataType";
import { ConfigType } from "./ConfigType";
/**字段映射，描述图表类型需要哪些配置字段*/
export class FieldMapping {
  /**字段名称，如"XField", "SeriesFields"*/
  name?: string | null | undefined = null;
  /**字段显示名称*/
  displayName?: string | null | undefined = null;
  /**字段类型，如string, list*/
  dataType: FieldDataType = 0;
  /**映射数据类型*/
  configType: ConfigType = 0;
  /**子字段定义（适用于对象或对象数组）*/
  child?: FieldMapping[] | null | undefined = [];
}
