import { DataManageModel } from "./DataManageModel";
import { FavoriteFolder } from "./FavoriteFolder";
import { UserTag } from "./UserTag";
export class FavoriteData {
  /**被收藏的文章ID*/
  dataManageModelId: GUID = "00000000-0000-0000-0000-000000000000";
  /**被收藏的文章*/
  dataManageModel?: DataManageModel | null | undefined = null;
  /**所属收藏夹*/
  favoriteFolderId: GUID = "00000000-0000-0000-0000-000000000000";
  /**收藏夹*/
  favoriteFolder?: FavoriteFolder | null | undefined = null;
  createdAt: Dayjs = dayjs();
  /**自定义标签*/
  userTags?: UserTag[] | null | undefined = [];
  /**笔记内容*/
  notes?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
