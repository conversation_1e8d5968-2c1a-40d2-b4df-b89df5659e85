import type { Ref } from 'vue'
/*
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-06-09 09:38:38
 * @LastEditors: luckymiaow
 */
import type { Router, RouteRecordRaw } from 'vue-router'
import { isAdmin } from '@/permission'
import { ref } from 'vue'

export const viewPage = () => import.meta.glob('/src/views/**/*.vue') || {}
export const viewLayouts = () => import.meta.glob('/src/layouts/**/**.vue') || {}

export const viewPagePath = Object.keys(viewPage())
export const viewLayoutsPath = Object.keys(viewLayouts())

/**
 * 根据传入的文件路由和后台查询的路由进行权限判断
 * 后台路由只需要关注页面的标题、菜单结构等非影响路由系统的配置
 * @param router
 * @param allRoutes 文件路由
 */
export function useDynamicRouter(router: Router, allRoutes: Array<RouteRecordRaw>, _allRoutesMap: Record<string, RouteRecordRaw>, localRoutes: Array<RouteRecordRaw>, localRoutesMap: Record<string, RouteRecordRaw>): { dynamicRoutes: Ref<RouteRecordRaw[]>, asyncRouteRegister: () => Promise<void>, delUserRouter: () => void } {
  const dynamicRoutes = ref<RouteRecordRaw[]>([]) // 作为菜单使用

  const dynamicRouteKeys = ref<string[]>([])

  /**
   * 注册有有权限的该分支路由
   * @param roots
   * @param names
   */
  function findBranches(roots: RouteRecordRaw[], names: string[]): RouteRecordRaw[] {
    function search(nodes: RouteRecordRaw[]): RouteRecordRaw[] {
      return nodes.flatMap((node) => {
        const res: RouteRecordRaw = {
          ...node,
          children: node.children ? search(node.children) : [],
        }
        if (names.includes(res.name?.toString() || res.name as string) || res.children?.length)
          return [res]
        return []
      })
    }
    return search(roots)
  }

  function routerWithRegister() {
    const names = dynamicRouteKeys.value = dynamicRouteKeys.value.filter(v => !(v in localRoutesMap))
    const res = findBranches(allRoutes, names)
    res.map(route => router.addRoute(route))
  }

  async function asyncRouteRegister() {
    delUserRouter()

    const userinfo = useUserStore()

    const menuList = userinfo.active.roles.flatMap(v => v.menu)
    const chid = (routes?: RouteRecordRaw[], authorize = false) => {
      return routes?.flatMap((route) => {
        const redirect = route.children?.find(v => !v.path)
        const flag = authorize || isAdmin() || menuList.includes(route.meta?.fullPath) || menuList.includes(redirect?.meta?.fullPath)
        if (flag) {
          const res = {
            ...route,
            component: null,
            children: chid(route.children, route.meta?.authorizeChild && flag),
          } as RouteRecordRaw
          dynamicRouteKeys.value.push(res.name?.toString() || res.path)
          return [res]
        }
        return []
      }) || []
    }
    dynamicRoutes.value = chid(allRoutes)
    routerWithRegister()
  }

  function delUserRouter() {
    router.clearRoutes()
    localRoutes.map(router.addRoute)
    dynamicRoutes.value = []
    dynamicRouteKeys.value = []
  }

  return { dynamicRoutes, asyncRouteRegister, delUserRouter }
}
