import { DataType } from "./DataType";
import { DataTag } from "./DataTag";
import { DataDomain } from "./DataDomain";
import { DataRegion } from "./DataRegion";
import { DataAndRegion } from "./DataAndRegion";
import { DataAndDomain } from "./DataAndDomain";
import { DataAndTag } from "./DataAndTag";
import { DataHotTag } from "./DataHotTag";
import { DataAndHotTag } from "./DataAndHotTag";
/**智库文章*/
export class DataManageModel {
  /**文章类型*/
  dataType: DataType = 0;
  /**AI分析结果*/
  ai?: string | null | undefined = null;
  /**情感分析分数（0~100 有小数）*/
  emotion?: number | null | undefined = null;
  /**链接地址*/
  link?: string | null | undefined = null;
  /**作者*/
  author?: string | null | undefined = null;
  /**来源国家*/
  sourceCountry?: string | null | undefined = null;
  /**来源*/
  source?: string | null | undefined = null;
  /**是否涉我*/
  isChina: boolean = false;
  /**时间*/
  time?: Dayjs | null | undefined = null;
  /**标题*/
  title: string = "";
  /**中文标题*/
  titleCn?: string | null | undefined = null;
  /**中文内容*/
  contentCn?: string | null | undefined = null;
  /**内容*/
  content?: string | null | undefined = null;
  /**标签*/
  tag?: DataTag[] | null | undefined = [];
  /**领域分类*/
  domain?: DataDomain[] | null | undefined = [];
  /**涉及地区*/
  region?: DataRegion[] | null | undefined = [];
  /**涉及地区*/
  dataAndRegion?: DataAndRegion[] | null | undefined = [];
  /**领域分类*/
  dataAndDomain?: DataAndDomain[] | null | undefined = [];
  /**涉及标签*/
  dataAndTag?: DataAndTag[] | null | undefined = [];
  dataHotTag?: DataHotTag[] | null | undefined = [];
  dataAndHotTag?: DataAndHotTag[] | null | undefined = [];
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
