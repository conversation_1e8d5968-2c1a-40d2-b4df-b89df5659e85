import { ResourceType } from "./ResourceType";
export interface ILimitedResource {
  id?: string | null | undefined;
  type: ResourceType;
  displayName?: string | null | undefined;
  description?: string | null | undefined;
  builtRolesOnly: boolean;
  allowedBuiltRoles?: string[] | null | undefined;
  allowedRoles?: string[] | null | undefined;
  customValidator: boolean;
  defaultForbidden?: boolean | null | undefined;
}
