import { DataManageModel } from "./DataManageModel";
import { DataHotTag } from "./DataHotTag";
export class DataAndHotTag {
  dataId: GUID = "00000000-0000-0000-0000-000000000000";
  dataHotTagId: GUID = "00000000-0000-0000-0000-000000000000";
  /**标记时间*/
  time: Dayjs = dayjs();
  /**智库文章*/
  data?: DataManageModel | null | undefined = null;
  /**文章热门标签*/
  hotTag?: DataHotTag | null | undefined = null;
}
