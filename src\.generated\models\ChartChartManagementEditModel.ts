import { ChartConfig } from "./ChartConfig";
import { PivotConfig } from "./PivotConfig";
export class ChartChartManagementEditModel {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  /**目录 /aa/bb/cc*/
  title?: string | null | undefined = null;
  chartTypeId: GUID = "00000000-0000-0000-0000-000000000000";
  /**图表数据映射*/
  fieldConfigJson?: ChartConfig[] | null | undefined = [];
  /**图表样式配置（如颜色、字体、布局等）*/
  styleConfigJson?: ChartConfig[] | null | undefined = [];
  /**关联的数据集（而非直接数据）*/
  chartDatasetId: GUID = "00000000-0000-0000-0000-000000000000";
  /**数据透视配置，存储行维度、列维度、指标、筛选条件等信息*/
  pivotConfig?: PivotConfig | null | undefined = null;
  updatedAt: Dayjs = dayjs();
}
