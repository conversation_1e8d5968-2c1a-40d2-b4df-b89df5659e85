<template>
  <div class="min-h-screen flex flex-col">
    <a-affix :offset-top="0">
      <Header />
    </a-affix>
    <main class="ch2-main m-auto w-80% flex-1 p-4 max-2xl:w-full">
      <RouterView />
    </main>
  </div>
</template>

<script setup lang="ts">
import Header from './components/Header.vue'

definePage({
  meta: {
    layout: false,
    isLayout: true,
    title: '东盟国别大数据精准画像平台',
    hidden: true,
  },
})
</script>

<style lang="less" scoped>

</style>
