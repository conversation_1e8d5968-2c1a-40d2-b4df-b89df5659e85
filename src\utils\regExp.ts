/*
 * @Author: Ztq
 * @Date: 2022-05-12 09:52:55
 * @LastEditors: Ztq
 * @LastEditTime: 2023-05-17 16:43:47
 * @Description:
 * @FilePath: \ch2-template-vue\src\utils\regExp.ts
 */
// 身份证号
export const idCardReg
  = /^[1-9]\d{5}(19|20)\d{2}((01|03|05|07|08|10|12)(0[1-9]|[12]\d|31)|(04|06|09|11)(0[1-9]|[12]\d|30)|02(0[1-9]|[12]\d))\d{3}([0-9x])$/i

// 手机号
export const phoneReg = /^1([3-9])\d{9}$/
// 邮箱
export const emailReg = /^([\w-])+@([\w-])+(\.[\w-])+/

// 只能输入数字
export const numberReg = /^[-+]?((\d+)(\.(\d+))?|(\.(\d+))?)$/

// 身份证号码
export const isCardReg = /^\d{17}[\dx]$/i

// 电话座机号码、手机号码
export const isLandlinePhoneReg = /^((0\d{2,3}-\d{7,8})|(1[3584]\d{9}))$/

// 密码校验 ，至少两种组合(数字、字母、特殊字符), 长度为8-16位字符'
export const isPasswordReg = /^(?!\d+$)(?![a-z]+$)(?!.+[\\|/\u4E00-\u9FA5]).{8,16}$/i

export const guidReg = /^(?!00000000-0000-0000-0000-000000000000$)/
