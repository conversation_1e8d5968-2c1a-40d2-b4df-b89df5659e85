// * Menu
import type { Dayjs as _Dayjs } from 'dayjs'

declare global {

  declare namespace Menu {
    interface MenuOptions {
      path: string
      name: string
      component?: string | (() => Promise<any>)
      redirect?: string
      meta: MetaProps
      children?: MenuOptions[]
    }
    interface MetaProps {
      icon: string
      title: string
      activeMenu?: string
      isLink?: string
      isHide: boolean
      isFull: boolean
      isAffix: boolean
      isKeepAlive: boolean
    }
  }

  // * Vite
  declare type Recordable<T = any> = Record<string, T>

  declare interface PackedApiResult<T> {
    data: T
    success: boolean
  }

  declare interface ViteEnv {
    VITE_API_URL: string
    VITE_PORT: number
    VITE_OPEN: boolean
    VITE_GLOB_APP_TITLE: string
    VITE_DROP_CONSOLE: boolean
    VITE_PROXY_URL: string
    VITE_BUILD_GZIP: boolean
    VITE_REPORT: boolean
  }

  type Dayjs = _Dayjs

  type GUID = string | null | undefined

  const TMap = any

  const uni = any
  const wx = any

}
