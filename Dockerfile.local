# FROM docker.fastoa.co/node:18-alpine as build
# WORKDIR /code
# # RUN  yarn config set registry http://npm.an.ch2lab.cn
# COPY package*.json yarn.lock ./
# RUN  yarn install
# COPY . .
# ENV NODE_OPTIONS="--max-old-space-size=8192"
# RUN yarn run build 

FROM docker.fastoa.co/nginx-br:1.20.1-alpine as final
RUN rm -rf /usr/share/nginx/html
RUN rm /etc/nginx/conf.d/default.conf
# RUN yum install  git
WORKDIR /usr/share/nginx/html
# COPY --from=build /code/dist .
COPY ./dist .
