# Excel导入功能说明

## 功能概述

已为数据集管理界面添加了Excel导入功能，支持前端直接解析Excel文件并自动生成字段定义和数据。

## 主要特性

### 1. 智能字段识别
- 自动读取Excel第一行作为字段名
- 智能推断字段类型：
  - **数字类型**：自动识别纯数字列
  - **日期类型**：识别日期格式（YYYY-MM-DD 或 YYYY/MM/DD）
  - **字符串类型**：默认类型，适用于文本内容

### 2. 文件格式支持
- 支持 `.xlsx` 和 `.xls` 格式
- 自动读取第一个工作表
- 兼容常见的Excel文件结构

### 3. 数据处理
- 保留内置的"国家"字段
- 自动转换数据类型
- 过滤空值和无效数据
- 支持大量数据导入

### 4. 用户体验
- 显示已选择的文件名
- 实时反馈导入结果
- 自动切换到数据录入标签页
- 错误处理和提示

## 使用方法

1. **创建或编辑数据集**
   - 点击"新增数据集"按钮或编辑现有数据集
   - 来源类型可以选择任意类型（手动录入、Excel导入、API对接、SQL）

2. **快速导入Excel文件**
   - 在"字段定义"标签页中，找到"快速导入"区域
   - 点击"从Excel导入"按钮
   - 选择要导入的Excel文件
   - 系统会自动解析文件并生成字段定义和数据

3. **查看和编辑**
   - 导入成功后会自动切换到"数据录入"标签页
   - 可以查看导入的数据
   - 可以在"字段定义"中调整字段类型和属性
   - 可以继续手动添加字段或数据行

4. **保存数据集**
   - 确认数据无误后，点击"确定"保存数据集

## Excel文件格式要求

### 推荐格式
```
| 产品名称 | 销售额 | 销售日期   | 地区   |
|----------|--------|------------|--------|
| 产品A    | 1000   | 2024-01-01 | 北京   |
| 产品B    | 1500   | 2024-01-02 | 上海   |
| 产品C    | 800    | 2024-01-03 | 广州   |
```

### 注意事项
- 第一行必须是字段名（列标题）
- 字段名不能为空
- 数据从第二行开始
- 建议数据格式保持一致

## 技术实现

### 核心依赖
- `xlsx` 库：用于解析Excel文件
- `FileReader API`：读取文件内容
- `Ant Design Vue`：UI组件

### 数据结构更新
- **records字段**：已从对象数组 `Record<string, any>[]` 更改为二维数组 `any[][]`
- **数据存储**：按行列索引存储，配合 `fieldsJson` 确定字段对应关系
- **兼容性**：自动适配新的数据结构，保持功能完整性

### 主要功能函数
- `handleExcelUpload()`: 处理Excel文件上传和解析，输出二维数组格式
- `syncRecordsWithFields()`: 同步字段变更到数据行
- 自动类型推断算法
- 数据格式转换和验证

## 错误处理

系统会处理以下常见错误：
- 文件格式不支持
- Excel文件为空
- 解析失败
- 数据类型转换错误

所有错误都会显示友好的提示信息，帮助用户快速定位问题。
