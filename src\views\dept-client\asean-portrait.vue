<!-- 东盟国情概况 -->
<template>
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-3 md:grid-cols-2 xl:grid-cols-4">
    <a-card
      v-for="(card, index) in cards"
      :key="index"
      :hoverable="true"
      :bordered="false"
      class="card-item group transform overflow-hidden bg-white shadow-md transition-all duration-300 hover:shadow-xl hover:-translate-y-2"
      :body-style="{ padding: 0 }"
      @click="toLink(card.url)"
    >
      <!-- 图片容器 -->
      <div class="relative overflow-hidden">
        <img
          :src="card.image"
          :alt="card.title"
          class="h-56 w-full object-cover transition-transform duration-300 group-hover:scale-110"
        >
        <!-- 悬浮遮罩 -->
        <div class="absolute inset-0 bg-black/0 transition-all duration-300 group-hover:bg-black/20" />
      </div>

      <!-- 标题区域 -->
      <div class="p-6">
        <h3 class="text-lg text-gray-900 font-semibold transition-colors duration-200 group-hover:text-blue-600">
          {{ card.title }}
        </h3>
        <div class="mt-2 h-1 w-0 from-blue-500 to-purple-500 bg-gradient-to-r transition-all duration-300 group-hover:w-full" />
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import AI在马来西亚动态中国 from './images/AI在马来西亚动态中国.png'
import AI在马来西亚动态欧美 from './images/AI在马来西亚动态欧美.png'
import image2 from './images/东盟国别企业库-越南.png'
import image1 from './images/东盟国别大数据精准画像-越南.png'
import image4 from './images/中国AI马来西亚布局.png'
import image3 from './images/欧美AI马来西亚布局.png'
import 马来西亚AI医疗应用场景 from './images/马来西亚AI医疗应用场景.png'
import 马来西亚最新动态 from './images/马来西亚最新动态.png'
import 马来西亚棕榈油AI应用场景 from './images/马来西亚棕榈油AI应用场景.png'

definePage({ meta: { title: '东盟精准画像' } })

const cards = ref([
  {
    title: '欧美AI马来西亚布局',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view-ai/ai-layout?type=%E6%AC%A7%E7%BE%8EAI%E9%A9%AC%E6%9D%A5%E8%A5%BF%E4%BA%9A%E5%B8%83%E5%B1%80',
    image: image3,
  },
  {
    title: '中国AI马来西亚布局',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view-ai/ai-layout?type=%E4%B8%AD%E5%9B%BDAI%E9%A9%AC%E6%9D%A5%E8%A5%BF%E4%BA%9A%E5%B8%83%E5%B1%80',
    image: image4,
  },
  {
    title: '马来西亚棕榈油AI应用场景',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view-ai/implement?type=%E9%A9%AC%E6%9D%A5%E8%A5%BF%E4%BA%9A%E6%A3%95%E6%A6%88%E6%B2%B9AI%E5%BA%94%E7%94%A8%E5%9C%BA%E6%99%AF',
    image: 马来西亚棕榈油AI应用场景,
  },
  {
    title: '马来西亚AI医疗应用场景',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view-ai/implement?type=马来西亚AI医疗应用场景',
    image: 马来西亚AI医疗应用场景,
  },
  {
    title: 'AI在马来西亚动态-欧美',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view-ai/news-dynamic?type=%E6%AC%A7%E7%BE%8E',
    image: AI在马来西亚动态欧美,
  },
  {
    title: 'AI在马来西亚动态-中国',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view-ai/news-dynamic?type=%E4%B8%AD%E5%9B%BD',
    image: AI在马来西亚动态中国,
  },
  {
    title: '马来西亚最新动态',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view-ai/news-dynamic?type=%E9%A9%AC%E6%9D%A5%E8%A5%BF%E4%BA%9A%E6%9C%80%E6%96%B0%E5%8A%A8%E6%80%81',
    image: 马来西亚最新动态,
  },
  {
    title: '东盟国别大数据精准画像-越南',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/knowledge-center/leader',
    image: image1,
  },
  {
    title: '东盟国别企业库-越南',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/knowledge-center/enterprise',
    image: image2,
  },

])

function toLink(url: string) {
  window.open(url, '_blank')
}
</script>
