<template>
  <a-card :active-tab-key="cardKey" :tab-list="tabList" @tab-change="onTabChange">
    <template v-if="detailsRef.roleName" #tabBarExtraContent>
      <a-button v-if="cardKey === '1'" type="primary" @click="() => permissionRef?.savePermission()">保存权限配置</a-button>
      <a-button v-if="cardKey === '2'" type="primary" @click="() => menuConfigRef?.save().then(detailsRef.callback)">保存菜单配置</a-button>
    </template>
    <template v-if="detailsRef.roleName && detailsRef.roleName !== '全部'">
      <permission v-show="cardKey === '1' " ref="permissionRef" :role-name="detailsRef.roleName" />
      <menuConfig v-show="cardKey === '2' " ref="menuConfigRef" :menu-config="detailsRef.menuConfig" :role-id="detailsRef.currentId" />
    </template>
  </a-card>
</template>

<script setup lang='tsx'>
import { ref } from 'vue'
import menuConfig from './menu.vue'
import permission from './permission.vue'

const detailsRef = ref({
  visible: false,
  title: '',
  currentId: '0',
  roleName: '',
  menuConfig: [] as string[],
  type: '',
  callback: () => { },
})

const cardKey = ref('1')

const permissionRef = useTemplateRef('permissionRef')

const menuConfigRef = useTemplateRef('menuConfigRef')

const tabList = ref([
  {
    key: '1',
    tab: '权限配置',
  },
  {
    key: '2',
    tab: '菜单配置',
  },
])

/**
 *  父组件传值
 * @param params 参数
 */
async function acceptParams(params: typeof detailsRef.value): Promise<void> {
  detailsRef.value = params
}

function onTabChange(value: string) {
  cardKey.value = value
}

defineExpose({
  acceptParams,
})
</script>

<style scoped lang='less'></style>
