import { useUserStore } from '@/stores/modules/user'
/*
 * @Author: 龙兆柒
 * @Date: 2024-01-25 10:08:47
 * @LastEditors: 龙兆柒
 * @LastEditTime: 2024-01-26 09:47:04
 * @FilePath: \ch2-template-vue\src\utils\signalR.ts
 * @Description:
 */
import * as SignalR from '@microsoft/signalr'

export class signalR {
  static connection: SignalR.HubConnection
  static init() {
    const { token: getToken } = useUserStore()
    // const url =`http://***********:1234`
    const hubUrl = `${import.meta.env.VITE_APP_PROXY_TARGET || ''}/api/OnlineUserHub?access_token=${getToken || ''}`
    //  const hubUrl = `${url || ''}/api/OnlineUserHub?access_token=${getToken || ''}`;
    console.log('%c [ hubUrl ]-18', 'font-size:13px; background:pink; color:#bf2c9f;', hubUrl)
    this.connection = new SignalR.HubConnectionBuilder()
      .configureLogging(SignalR.LogLevel.Information)
      .withUrl(hubUrl)
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: () => 2000, // 每5秒重连一次
      })
      .build()

    this.connection.keepAliveIntervalInMilliseconds = 30 * 1000 // 心跳检测15s
    this.connection.serverTimeoutInMilliseconds = 30 * 60 * 1000 // 超时时间30m
  }

  static async start() {
    try {
      // await this.connection.start();
    }
    catch (error) {
      console.error('SignalR 连接失败', error)
      // 处理连接失败的情况
    }
  }

  static onclose(callback: (error?: Error) => void) {
    this.connection.onclose(callback)
  }

  static onreconnecting(callback: (error?: Error) => void) {
    this.connection.onreconnecting(callback)
  }

  static stop() {
    // this.connection.stop();
  }

  static off(methodName: string) {
    this.connection.off(methodName)
  }

  static on(methodName: string, newMethod: (...args: any[]) => any) {
    this.connection.on(methodName, newMethod)
  }

  static install(app: any) {
    this.init()
    return app
  }
}

export default signalR
