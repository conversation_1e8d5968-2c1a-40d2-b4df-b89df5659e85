/*
 * @Description: ^_^
 * @Author: sharebravery
 * @Date: 2022-12-08 11:58:47
 */
import { mergeConfig } from 'vite'
import compressPlugin from 'vite-plugin-compression'

import baseConfig from './vite.config.base'
import getEnv from './utils/getEnv'
import proxy from './utils/proxy'

const mode = 'prod'
const env = getEnv(mode)
const proxyTarget = env.VITE_APP_PROXY_TARGET

export default mergeConfig(
  {
    mode,
    server: {
      proxy: proxyTarget ? proxy(env) : undefined,
    },
    plugins: [
      compressPlugin({
        deleteOriginFile: false,
        algorithm: 'brotliCompress',
        ext: '.br',
      }),

    ],
    build: {
      outDir: 'dist',
      sourcemap: true,
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            if (id.includes('node_modules'))
              return id.toString().split('node_modules/')[1].split('/')[0].toString()
          },
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name!.split('.')
            let extType = info[info.length - 1]
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo!.name || ''))
              extType = 'media'
            else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(assetInfo!.name || ''))
              extType = 'img'
            else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo!.name || ''))
              extType = 'fonts'

            return `static/${extType}/[name]-[hash][extname]`
          },
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
        },
      },
    },
  },
  baseConfig,
)
