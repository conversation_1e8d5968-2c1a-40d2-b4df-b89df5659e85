/*
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-09-25 10:32:44
 * @LastEditors: luckymiaow
 */
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')

export function dateTime(value?: dayjs.ConfigType, pattern = 'YYYY-MM-DD HH:mm:ss'): string | null {
  if (!value)
    return '--'
  const result = dayjs(value).format(pattern)
  if (result === 'Invalid date')
    return null

  return result.replace('00:00:00', '')
}

export function nullText(value: string | null, pattern: string = '--'): string {
  return value || pattern
}
