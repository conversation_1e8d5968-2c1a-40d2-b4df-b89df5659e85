<!--
 * @Author: Ztq
 * @Date: 2023-06-01 11:15:33
 * @LastEditors: 景 彡
 * @LastEditTime: 2023-06-08 10:56:43
 * @Description:
 * @FilePath: \ch2-template-vue\src\views\user-center\index.vue
-->
<template>
  <div class="user-center">
    <a-tabs v-model:active-key="activeKey" tab-position="left">
      <a-tab-pane key="个人信息" tab="个人信息">
        <h2>个人信息</h2>
        <EditInformation />
      </a-tab-pane>
      <a-tab-pane key="安全设置" tab="安全设置">
        <h2>安全设置</h2>
        <SecuritySetting />
      </a-tab-pane>
      <a-tab-pane key="关于平台" tab="关于平台">
        <h2>关于平台</h2>
        <AboutSystem />
      </a-tab-pane>
    </a-tabs>

    <!-- <c-enum-select class="m16 w-200px" :enum="RoleName" placeholder="切换角色" @change="changeRole" /> -->
  </div>
</template>

<script setup lang="tsx">
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import AboutSystem from './components/AboutSystem.vue'
import EditInformation from './components/EditInformation.vue'
import SecuritySetting from './components/SecuritySetting.vue'

const activeKey = ref('个人信息')

const route = useRoute()

watch(route, () => {
  activeKey.value = route.query.activeKey as string
}, { immediate: true })

// const userStore = useUserStore()

// function changeRole(v: RoleName) {

//   userStore.

// }
</script>

<style scoped lang="less">
.user-center {
  padding-top: 20px;
  min-height: 50vh;
  background: @colorBgContainer;
  border-radius: calc(@borderRadiusLG * 1px);
}
</style>
