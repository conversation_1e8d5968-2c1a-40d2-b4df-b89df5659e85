import type { Plugin } from 'vite'

// 你原始的代码
const code = `
import { routes as _allRoutes } from 'vue-router/auto-routes';

function getLocalRoutes(routes) {
  function findLocalBranches(node) {
    return node?.flatMap(item => {
      const res = {
        ...item,
        children: item.meta?.local ? item.children || [] : findLocalBranches(item.children || [])
      };
      if ((res.meta?.local || res.children?.length) && res.meta?.authorize?.length === 0) return [res]
      return [];
    });
  }
  return findLocalBranches(routes);
}

const layouts = Object.entries(import.meta.glob('/src/layouts/*.vue')).map(([key, modules]) => {
  const p = key.split('/');
  return [p[p.length - 1]?.split('.')[0] || '', modules];
}).reduce((a, b) => {
  a[b[0]] = b[1];
  return a;
}, {});

function setupLayouts2(routes) {
  const defaultLayout = 'default' in layouts ? 'default' : false;
  const rootRoutes = [];

  function push(res, route) {
    res.push(route);
  }

  function filterChildNoLayout(child) {
    const root = child?.filter(v => v.meta?.layout === false);
    if (root) {
      root.forEach(item => {
        if (item.meta?.fullPath) item.path = item.meta?.fullPath;
        rootRoutes.push(item);
      });
    }
    return child?.filter(v => v.meta?.layout !== false);
  }

  function setLayout(routes, isLayout = true, parentPath = '') {
    const res = []
    for (const route of routes) {
      const fullPaths = []
      
      if (parentPath)
        fullPaths.push(parentPath)

      fullPaths.push(route.path)

      const fullPath = fullPaths.join('/')

      let tempRoute = { ...route, meta: { ...route.meta, fullPath } }

      const layoutIndex = tempRoute.children?.findIndex(v => v.meta?.isLayout || v.meta?.layoutRoute) ?? -1

      if (layoutIndex > -1) {
        const t = tempRoute.children[layoutIndex]

        const isFather = t.meta?.layoutRoute?.meta?.layout

        // 生成一个新的父级嵌套
        const layout = t.meta?.layoutRoute?.meta?.layout ?? defaultLayout

        // isLayout不替换时才产生新的路由，产生的路由必须要有布局文件，否则无法渲染
        if (isFather && layout && !t.meta?.layoutRoute?.meta?.isLayout) {
          tempRoute = {
            ...t.meta?.layoutRoute,
            isLayout: true,
            component: layouts[layout],
            meta: { ...t.meta?.layoutRoute?.meta, fullPath, layoutRoute: undefined, layout, isLayout: true },
            path: tempRoute.path,
            children: tempRoute.children || [],
          } 
        }
        else {
          tempRoute.children?.splice(layoutIndex, 1)
          tempRoute = {
            ...t,
            meta: { ...t.meta, fullPath },
            path: tempRoute.path,
            name: tempRoute.name,
            children: tempRoute.children || [],
          }
        }
      }

      const meta = tempRoute.meta || {}

      const layout = meta?.layout ?? defaultLayout

      if (meta?.layout === false) {
        push(res, tempRoute)
      }
      else if (layout && isLayout) {
        if (meta.isLayout) {
          tempRoute.component = layouts[layout]
          push(res, tempRoute)
        }

        else {
          const layoutRoute = {
            ...meta.layoutRoute,
            meta: { ...meta.layoutRoute?.meta, isLayout: true, fullPath },
            component: layouts[layout],
            children: [tempRoute],
          } 

          if (!layoutRoute.path) {
            layoutRoute.path = tempRoute.path
            tempRoute.path = ''
            tempRoute.meta.fullPath = layoutRoute.path + '/'

          }

          push(res, layoutRoute)
        }
      }
      else {
        push(res, tempRoute)
      }

      if (tempRoute.children)
        tempRoute.children = filterChildNoLayout(setLayout(tempRoute.children, false, fullPath))
    }
    return res
  }

  const result = setLayout(routes);
  return [...result, ...rootRoutes];
}

function flattenTree(tree) {
  let flatArray = [];
  tree.forEach(node => {
    flatArray.push(node);
    if (node.children) {
      flatArray = flatArray.concat(flattenTree(node.children));
    }
  });
  return flatArray;
}

export function useFileRouter() {
  const allRoutes = setupLayouts2(_allRoutes);
  const localRoutes = getLocalRoutes(allRoutes);
  const allRoutesNamedMap = flattenTree(allRoutes).filter(p => p.name).reduce((acc, curr) => {
    acc[curr.name] = curr;
    return acc;
  }, {});
  const localRoutesNamedMap = flattenTree(localRoutes).filter(p => p.name).reduce((acc, curr) => {
    acc[curr.name] = curr;
    return acc;
  }, {});
  return {
    localRoutes,
    allRoutesNamedMap,
    localRoutesNamedMap,
    allRoutes
  };
}
`

/**
 * 文件路由加上布局虚拟模块
 * @returns
 */
export default function virtualModulePlugin(): Plugin {
  const virtualModuleId = 'virtual:file-layout-router-module'

  return {
    name: 'file-layout-router-module',
    resolveId(id) {
      if (id === virtualModuleId)
        return id
    },
    load(id) {
      if (id === virtualModuleId)
        return code
    },
  }
}
