<template>
  <div>
    <a-tabs v-model:active-key="tabKey" :animated="false" @change="tabChange">
      <a-tab-pane key="1" tab="子项模式">
        <c-form ref="modalFormRef" :model="standardItems">
          <c-table
            row-key="key"
            class="table"
            bordered
            :data-source="standardItems"
            :loading="loading"
            :serial-number="false"
            :expanded-row-keys="expandedRowKeys"
            @expanded-rows-change="expandedRowsChange"
          >
            <a-table-column align="center" />
            <a-table-column title="显示标签" data-index="label" align="center">
              <template #default="{ record }">
                <c-form-item
                  :name="getValName(record, 'label')"
                  :rules="[{ required: true, trigger: ['change', 'blur'], message: '显示标签必填' }]"
                >
                  <c-input
                    allow-clear
                    style="margin: -5px 0"
                    :value="record.label"
                    @change="labelChange($event, record)"
                  />
                </c-form-item>
              </template>
            </a-table-column>
            <a-table-column title="值" data-index="value" align="center">
              <template #default="{ record }">
                <c-form-item
                  :name="getValName(record, 'value')"
                  :rules="[{ required: true, trigger: ['change', 'blur'], message: '值必填' }]"
                >
                  <c-input
                    v-model:value="record.value"
                    :disabled="!record.isEdit"
                    style="margin: -5px 0"
                    placeholder="自动填充"
                    allow-clear
                  />
                </c-form-item>
              </template>
            </a-table-column>
            <a-table-column title="是否默认" data-index="isDefault" width="100px" align="center">
              <template #default="{ record }">
                <c-form-item>
                  <c-boolean-select
                    v-model:value="record.isDefault"
                    style="width: 60px"
                    placeholder="是否默认"
                    allow-clear
                  />
                </c-form-item>
              </template>
            </a-table-column>
            <a-table-column title="备注" data-index="remark" align="center">
              <template #default="{ record }">
                <c-form-item required>
                  <c-input v-model:value="record.remark" style="margin: -5px 0" />
                </c-form-item>
              </template>
            </a-table-column>
            <a-table-column title="字段类型" data-index="type" align="center" width="180px">
              <template #default="{ record }">
                <c-form-item required>
                  <c-enum-select
                    v-model:value="record.type"
                    :enum="FieldType"
                    style="width: 100%"
                    placeholder="文本"
                  />
                </c-form-item>
              </template>
            </a-table-column>
            <a-table-column title="操作" data-index="itemAction" align="center" width="180px">
              <template #default="{ record }">
                <a-popconfirm
                  title="确定删除?"
                  ok-text="是"
                  cancel-text="否"
                  @confirm="deleteItem(record.parent, record.key)"
                >
                  <a>删除</a>
                </a-popconfirm>
                <a-divider type="vertical" />
                <a @click="addItem(record.children, record)">添加子项</a>
              </template>
            </a-table-column>
          </c-table>
        </c-form>
      </a-tab-pane>
      <a-tab-pane key="2" tab="Json模式">
        <c-textarea
          v-model:value="jsonData"
          style="height: 500px"
          @change="tabChange('1')"
        />
      </a-tab-pane>
      <template #rightExtra>
        <a-space>
          <a-button v-if="tabKey === '1'" type="primary" @click="addItem(standardItems, null)">
            新增子项
          </a-button>
        </a-space>
      </template>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import type { Key } from 'ant-design-vue/lib/_util/type'
import type { FormRef } from 'ch2-components/lib/form/types'
import type { StandardItem } from './useStandardItem'
import { FieldType } from 'ch2-components/lib/standard/types'
import { ref } from 'vue'
import { useStandardItem } from './useStandardItem'

const standardItems = defineModel<StandardItem[]>('value', { default: [] })

const tabKey = ref('1')

const loading = ref(false)

const visible = ref(false)

const jsonData = ref<string>('')

const modalFormRef = ref<FormRef>()

const {

  toStandardEdit,
  addItem,
  deleteItem,
  expandedRowKeys,
  expandedRowsChange,
  labelChange,
  toSaveData,
} = useStandardItem(standardItems) // 编辑过程中的标准项

function tabChange(key: Key) {
  if (key === '2')
    jsonData.value = JSON.stringify(toSaveData(standardItems.value), null, 2)
  else
    standardItems.value = toStandardEdit(JSON.parse(jsonData.value!), null)
}

function getValName(record: StandardItem, field?: string): string[] {
  const path: string[] = []
  if (field)
    path.unshift(field)
  if (record.parent) {
    path.unshift(record.parent.children.findIndex(e => e.key === record.key).toString())
    path.unshift('children')
    path.unshift(...getValName(record.parent))
  }
  else {
    path.unshift(standardItems.value.findIndex(e => e.key === record.key).toString())
  }
  return path
}

defineExpose({
  visible,
})
</script>

<style lang="less" scoped>
:deep(.ant-form-item) {
  margin: 0;
}
</style>
