<!-- AI政策新闻 -->
<template>
  <div>
    <article
      v-for="(item, index) in dataList"
      :key="item.title"
      class="mb-4 flex overflow-hidden rounded bg-white shadow"
    >
      <div class="relative flex-1 border-r border-gray-200 p-4">
        <div class="mb-2 cursor-pointer hover:(text-blue-500)" @click="toDetail(item)">
          <h3 class="mb-1 text-base"> {{ index + 1 }}、 {{ item.title }}</h3>
        </div>
        <div class="mt-4">
          <span class="mr-4 text-sm c-text-secondary">{{ item.time }}</span>
          <span class="text-sm c-text-secondary">原文链接：<a :href="item.url" _blank>{{ item.url }}</a></span>
        </div>
        <!-- <p class="text-sm text-gray-700 leading-normal" v-html="item.content" /> -->
      </div>
    </article>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'

import aiList from './data/aiList.json'

// 模拟数据
const dataList = ref([])

const router = useRouter()

function toDetail(data: any) {
  router.push({ path: '/dept-client/policy-news-detail?', query: { title: data.title } })
}

onMounted(() => {
  dataList.value = aiList.sort((a, b) => {
    const parseTime = (str: string) => {
    // 替换为标准时间格式：2023-07-21
      const parts = str.match(/(\d+)年(\d+)月(\d+)日/)
      if (!parts)
        return new Date(0) // 无法解析时放最前面
      const [_, year, month, day] = parts
      return new Date(`${year}-${month?.padStart(2, '0')}-${day?.padStart(2, '0')}`)
    }

    return parseTime(b.time).getTime() - parseTime(a.time).getTime() // 降序排序
  })
})
</script>
