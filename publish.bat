@echo off
setlocal

:: 设置变量
set "DOCKER_IMAGE=ch2/asean-ds-park-pa-vue:publish"
set "DOCKER_REGISTRY=docker.fastoa.co"
set "CI_REGISTRY_USER=docker"
set "CI_REGISTRY_PASSWORD=Abcd1234"
set "SSH_USER=proton"  :: 替换为你的 SSH 用户名
set "DEV_DEPLOY_SSH=172.22.0.50"
set "COMPOSE_DIR=/data/docker/asean-ds-park-pa/vue"
echo %SSH_USER%@%DEV_DEPLOY_SSH%

:: 生成 Docker 镜像
echo Building Docker image...
docker build -f Dockerfile.local -t %DOCKER_IMAGE%  ./
if errorlevel 1 (
    echo Docker image build failed!
    exit /b 1
)

:: 登录私有 Docker 仓库
echo Logging in to Docker registry...
docker login -u %CI_REGISTRY_USER% -p %CI_REGISTRY_PASSWORD% %DOCKER_REGISTRY%
if errorlevel 1 (
    echo Docker registry login failed!
    exit /b 1
)

:: 推送 Docker 镜像
echo Pushing Docker image to registry...
docker tag %DOCKER_IMAGE% %DOCKER_REGISTRY%/%DOCKER_IMAGE%
if errorlevel 1 (
    echo Docker image tagging failed!
    exit /b 1
)
docker push %DOCKER_REGISTRY%/%DOCKER_IMAGE%
if errorlevel 1 (
    echo Docker image push failed!
    exit /b 1
)

:: 使用 SSH 连接到 Linux 服务器并运行 Docker Compose 命令
echo Connecting to Linux server and running Docker Compose...
ssh %SSH_USER%@%DEV_DEPLOY_SSH% "cd %COMPOSE_DIR% && docker login -u %CI_REGISTRY_USER% -p %CI_REGISTRY_PASSWORD% %DOCKER_REGISTRY% && docker compose pull && docker compose up -d"
if errorlevel 1 (
    echo SSH or Docker Compose command failed!
    exit /b 1
)

echo Done.
endlocal