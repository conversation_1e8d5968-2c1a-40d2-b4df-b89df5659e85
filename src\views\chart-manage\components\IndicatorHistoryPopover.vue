<template>
  <a-popover
    v-if="history && history.length"
    placement="topLeft"
  >
    <template #title>
      <div class="">
        <span class="">更新记录</span>
        <a-tag
          v-if="currentVia"
          :color="getViaColor(currentVia)"
          class="via-tag"
        >
          {{ currentVia }}
        </a-tag>
      </div>
    </template>

    <template #content>
      <div class="history-content min-w80">
        <div class="history-list">
          <div
            v-for="(cell, cellIndex) in history"
            :key="cellIndex"
            class="history-item mt2"
            :class="{ 'is-latest': cellIndex === 0 }"
          >
            <div class="flex">
              <!-- 左侧标签 -->
              <a-tag
                :color="getViaColor(cell.via || '')"
                size="small"
                class="tag-small"
              >
                {{ cell.via || '未知来源' }}
              </a-tag>
              <span class="">{{ dateTime(cell.updatedAt) }}</span>
              <a class="mlauto" @click="handleViewSource(cell)">查看溯源</a>
            </div>
            <!-- 更新内容 -->
            <div class="mt2">
              {{ cell.remarks || getDefaultUpdateContent(cell) }}
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 默认的可点击单元格 -->
    <div
      class="min-h-8 flex cursor-pointer items-center border border-gray-300 rounded border-dashed px-2 py-1 hover:border-blue-400 hover:bg-blue-50"
      @click="handleCellClick"
    >
      <span v-if="displayValue === EMPTY_PLACEHOLDER || !displayValue" class="text-gray-400">点击编辑指标</span>
      <span v-else>{{ displayValue }}</span>
    </div>
  </a-popover>
  <!-- 默认的可点击单元格 -->
  <div
    v-else
    class="min-h-8 flex cursor-pointer items-center border border-gray-300 rounded border-dashed px-2 py-1 hover:border-blue-400 hover:bg-blue-50"
    @click="handleCellClick"
  >
    <span v-if="displayValue === EMPTY_PLACEHOLDER || !displayValue" class="text-gray-400">点击编辑指标</span>
    <span v-else>{{ displayValue }}</span>
  </div>
</template>

<script setup lang="ts">
import type { ChartDatasetCell } from '@/.generated/models/ChartDatasetCell'
import { EMPTY_PLACEHOLDER, getViaColor } from '../composables/useIndicatorData'

interface Props {
  history?: ChartDatasetCell[]
  displayValue?: string | number
  onCellClick?: () => void
  onViewSource?: (cell: ChartDatasetCell) => void
}

const props = withDefaults(defineProps<Props>(), {
  history: () => [],
  displayValue: '',
  onCellClick: () => {},
  onViewSource: () => {},
})

const emit = defineEmits<{
  cellClick: []
  viewSource: [cell: ChartDatasetCell]
}>()

// 计算属性
const currentVia = computed(() => props.history[0]?.via || '')

function getDefaultUpdateContent(cell: ChartDatasetCell): string {
  if (cell.updateType) {
    return cell.updateType
  }
  if (cell.value !== undefined && cell.value !== null) {
    return `更新数值为 ${cell.value}`
  }
  return '数据更新'
}

function handleCellClick() {
  emit('cellClick')
  props.onCellClick?.()
}

function handleViewSource(cell: ChartDatasetCell) {
  emit('viewSource', cell)
  props.onViewSource?.(cell)
}
</script>

<style scoped lang="less">
.history-item {
  &.is-latest {
    background-color: #f6ffed;
    padding: 8px;
    margin: -8px;
    border-radius: 4px;
    border-left: 3px solid #52c41a;
  }
}
</style>
