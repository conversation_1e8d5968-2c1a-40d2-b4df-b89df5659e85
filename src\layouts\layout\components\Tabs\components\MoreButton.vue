<!-- 标签页更多操作按钮 -->
<template>
  <a-dropdown :trigger="['click']" placement="bottomRight" style="margin-right: 100px">
    <template #overlay>
      <a-menu>
        <a-menu-item key="1" @click="refresh">
          <SyncOutlined />
          刷新
        </a-menu-item>
        <a-menu-item key="2" @click="maximize">
          <ExpandOutlined />
          最大化
        </a-menu-item>
        <a-divider style="margin: 0 0" />
        <a-menu-item key="3" @click="closeCurrentTab">
          <BookOutlined />
          关闭当前
        </a-menu-item>
        <a-menu-item key="3" @click="closeOtherTab">
          <DeleteOutlined />
          关闭其他
        </a-menu-item>
        <a-menu-item key="3" @click="closeAllTab">
          <DatabaseOutlined />
          关闭所有
        </a-menu-item>
      </a-menu>
    </template>
    <a-button>
      更多
      <DownOutlined />
    </a-button>
  </a-dropdown>
</template>

<script setup lang="ts">
import { useAppStore, useTabsStore } from '@/stores'
import {
  BookOutlined,
  DatabaseOutlined,
  DeleteOutlined,
  DownOutlined,
  ExpandOutlined,
  SyncOutlined,
} from '@ant-design/icons-vue'
import { computed, inject } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()

const router = useRouter()

const tabStore = useTabsStore()
const globalStore = useAppStore()

const themeConfig = computed(() => globalStore.themeConfig)

// 刷新当前页面
const refresh = inject('refresh') as any

// 刷新当前页面方法

function maximize() {
  globalStore.setThemeConfig({ ...themeConfig.value, maximize: true })
}

function closeCurrentTab() {
  if (route.meta.isAffix)
    return
  tabStore.removeTabs(route.fullPath, router)
}

// Close Other
function closeOtherTab() {
  tabStore.closeMultipleTab(route.fullPath)
}

// Close All
function closeAllTab() {
  tabStore.closeMultipleTab()

  router.push('/home')
}
</script>

<style scoped></style>
