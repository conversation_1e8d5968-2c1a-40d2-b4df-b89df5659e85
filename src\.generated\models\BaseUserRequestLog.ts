import { RequestType } from "./RequestType";
import { ResponseType } from "./ResponseType";
export class BaseUserRequestLog<TKey> {
  id?: TKey | null | undefined = null;
  describe?: string | null | undefined = null;
  userId?: TKey | null | undefined = null;
  roles?: string[] | null | undefined = [];
  time: Dayjs = dayjs();
  path?: string | null | undefined = null;
  userAgent?: string | null | undefined = null;
  ipAddress?: string | null | undefined = null;
  requestQuery?: string | null | undefined = null;
  requestBody?: string | null | undefined = null;
  requestFileName?: string | null | undefined = null;
  type: RequestType = 0;
  responseContentBody?: string | null | undefined = null;
  responseFileName?: string | null | undefined = null;
  controllerName?: string | null | undefined = null;
  actionName?: string | null | undefined = null;
  responseDescribe?: string | null | undefined = null;
  responseType: ResponseType = 0;
}
