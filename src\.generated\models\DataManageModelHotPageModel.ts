import { DataHotTagView } from "./DataHotTagView";
export class DataManageModelHotPageModel {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  /**来源国家*/
  sourceCountry?: string | null | undefined = null;
  /**来源*/
  source?: string | null | undefined = null;
  /**是否涉我*/
  isChina: boolean = false;
  /**时间*/
  time?: Dayjs | null | undefined = null;
  /**标题*/
  title: string = "";
  /**中文标题*/
  titleCn?: string | null | undefined = null;
  /**中文内容*/
  contentCn?: string | null | undefined = null;
  /**内容*/
  content?: string | null | undefined = null;
  hotTags?: DataHotTagView[] | null | undefined = [];
  readCount: number = 0;
}
