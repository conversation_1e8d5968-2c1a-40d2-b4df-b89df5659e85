<!--
 * @Author: Ztq
 * @Date: 2023-06-01 11:40:38
 * @LastEditors: 景 彡
 * @LastEditTime: 2023-06-08 10:43:17
 * @Description:
 * @FilePath: \ch2-template-vue\src\views\user-center\components\SecuritySetting.vue
-->

<template>
  <div class="security">
    <a-list item-layout="horizontal">
      <a-list-item>
        <a-list-item-meta description="当前密码强度:强">
          <template #title>
            账号密码
          </template>
        </a-list-item-meta>
        <template #extra>
          <a @click="editPassword()">修改</a>
        </template>
      </a-list-item>
      <a-list-item>
        <a-list-item-meta>
          <template #title>
            绑定邮箱
          </template>
          <template #description>
            {{ `未绑定` }}(暂无接口)
          </template>
        </a-list-item-meta>
        <template #extra>
          <a @click="eidtEmail()">修改</a>
        </template>
      </a-list-item>

      <!-- 2023年3月2日 新增 绑定手机号 -->
      <a-list-item>
        <a-list-item-meta>
          <template #title>
            绑定手机号码
          </template>
          <template #description>
            {{ `未绑定` }}(暂无接口)
          </template>
        </a-list-item-meta>
        <template #extra>
          <a @click="eidtPhone()">修改</a>
        </template>
      </a-list-item>
    </a-list>
    <Password ref="passwordRef" />

    <Email ref="emailRef" />

    <Phone ref="phoneRef" />
  </div>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import Email from './Email.vue'
import Password from './Password.vue'
import Phone from './Phone.vue'

const passwordRef = ref<InstanceType<typeof Password>>()

const emailRef = ref<InstanceType<typeof Email>>()

const phoneRef = ref<InstanceType<typeof Email>>()

function editPassword() {
  passwordRef.value!.visible = true
}
function eidtEmail() {
  emailRef.value!.visible = true
}
function eidtPhone() {
  phoneRef.value!.visible = true
}

// const activeKey = ref('个人信息');
</script>

<style scoped lang="less">
.security {
  width: 95%;
}
</style>
