<template>
  <a-spin class="spin-content" tip="注销中..." size="large">
    <div id="c2-box" class="box" />
  </a-spin>
</template>

<script lang="ts" setup>
import { UserService } from '@/api/user'
import router, { dynamicRouter } from '@/router'
import { LoginRoute } from '@/router/utils/useRouteConfig'
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'

definePage({
  meta: {
    local: true,
    layout: false,
    hidden: true,
    authorize: [],
    layoutRoute: {
      meta: { hidden: true, authorize: [] },
    },
  },
})

const route = useRoute()

onMounted(() => {
  try {
    dynamicRouter.Delete()
    UserService.logout(!!router.currentRoute?.value?.query?.api)
  }
  catch { }
  router.push({
    path: LoginRoute.path,
    query: { redirect: route.query?.redirect },
    replace: true,
  })
})
</script>

<style scoped lang="less">
.box {
  width: 100%;
  min-height: 950px;
  background-color: #e6f7ff;
}
</style>
