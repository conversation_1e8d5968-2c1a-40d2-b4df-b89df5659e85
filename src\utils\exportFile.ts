/*
 * @Description: ^_^
 * @Author: sharebravery
 * @Date: 2022-09-21 09:06:00
 */
import type { AxiosResponse } from 'axios'

/**
 * @example exportFile(await api.internal.ExamStructure.DownloadObjectiveAnswerTemplate(params,data))
 * @export
 * @param {AxiosResponse<Blob>} result
 * @return {*}  {Promise<void>}
 */
export default async function exportFile(result: AxiosResponse<Blob>): Promise<void> {
  if (!result)
    throw new Error('接口返回值为空')

  try {
    const reader = new FileReader()
    const { data, headers } = result
    reader.onload = () => {
      try {
        const fileName = window.decodeURI(
          decodeURIComponent(decodeURI(headers['content-disposition']!.split('\'\'')[1])),
        )
        const url = window.URL.createObjectURL(data)
        const link = document.createElement('a')
        const body = document.querySelector('body') as HTMLBodyElement

        link.href = url
        link.download = fileName
        link.style.display = 'none'
        body.appendChild(link)
        link.click()
        body.removeChild(link)
      }
      catch (error: any) {
        console.log('%c [ e ]', 'font-size:13px; background:yellow; color:#bf2c9f;', error)
        throw new Error(error)
      }
    }
    reader.readAsText(data)
  }
  catch (error: any) {
    console.log('%c [ error ]', 'font-size:13px; background:yellow; color:#bf2c9f;', error)
    throw new Error(error)
  }
}
