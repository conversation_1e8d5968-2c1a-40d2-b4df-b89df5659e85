<template>
  <div>
    <div class="header">
      <h1>「同声」AI内容工坊 (Resonance AI Content Workshop)</h1>
      <p>一个面向东盟市场，由多AI智能体协同驱动的高效能涉中话题内容创作平台</p>
    </div>

    <h2 class="section-title">工作流AI智能体 (Workflow AI Agents)</h2>
    <p>以下智能体按内容创作的工作流顺序排列，协同完成从选题到发布的完整任务。</p>

    <div class="agent-grid">
      <!-- Agent 1: 话题与舆情洞察 -->
      <div class="agent-card">
        <div class="agent-header">
          <span class="agent-icon">📡</span>
          <span class="agent-title">1. 话题与舆情洞察AI</span>
        </div>
        <div class="agent-body">
          <h4>核心功能：</h4>
          <p style="font-size:0.9em; margin:0; color: #5a6a82;">实时监控并分析涉中及东盟本地热点，构建与维护“涉中话题聚类库”。</p>
          <h4>主要职责：</h4>
          <ul>
            <li>全网扫描（新闻、社交媒体、论坛）相关话题。</li>
            <li>进行情感分析、热度追踪和趋势预测。</li>
            <li>对涉中话题进行聚类和标签化（如：科技合作、供应链、文化交流）。</li>
            <li>识别指定东盟国家/区域的“信息真空”或“兴趣点”。</li>
          </ul>
        </div>
        <div class="agent-footer">
          <span class="input-tag">输入: 关键词、监控源列表</span>
          <span class="output-tag">输出: 结构化话题库、热点报告</span>
        </div>
      </div>

      <!-- Agent 2: 跨文化策略规划 -->
      <div class="agent-card">
        <div class="agent-header">
          <span class="agent-icon">🌏</span>
          <span class="agent-title">2. 跨文化策略规划AI</span>
        </div>
        <div class="agent-body">
          <h4>核心功能：</h4>
          <p style="font-size:0.9em; margin:0; color: #5a6a82;">基于目标受众，匹配最佳话语风格和传播角度，制定内容策略。</p>
          <h4>主要职责：</h4>
          <ul>
            <li>调用“东盟话语风格库”，匹配目标国（如：泰国）的沟通范式。</li>
            <li>定义内容的核心信息、基调（如：友好、专业、活泼）和叙事框架。</li>
            <li>建议切入点，规避潜在的文化、宗教、政治风险点。</li>
            <li>推荐最适合的媒介渠道（如：Facebook长文、TikTok短视频、专业博客）。</li>
          </ul>
        </div>
        <div class="agent-footer">
          <span class="input-tag">输入: 选定话题、目标国家/人群</span>
          <span class="output-tag">输出: 内容策略简报 (Content Brief)</span>
        </div>
      </div>

      <!-- Agent 3: 本地化内容生成 -->
      <div class="agent-card">
        <div class="agent-header">
          <span class="agent-icon">✍️</span>
          <span class="agent-title">3. 本地化内容生成AI</span>
        </div>
        <div class="agent-body">
          <h4>核心功能：</h4>
          <p style="font-size:0.9em; margin:0; color: #5a6a82;">根据策略简报，用目标市场地道的话语风格生成初稿。</p>
          <h4>主要职责：</h4>
          <ul>
            <li>遣词造句符合当地语言习惯和网络流行语。</li>
            <li>使用当地用户熟悉的比喻、案例和文化符号。</li>
            <li>根据不同媒介（文字、脚本）的要求调整内容的结构和长度。</li>
            <li>生成多个版本（A/B Test备选）以供选择。</li>
          </ul>
        </div>
        <div class="agent-footer">
          <span class="input-tag">输入: 内容策略简报</span>
          <span class="output-tag">输出: 本地化内容初稿（多种版本）</span>
        </div>
      </div>

      <!-- Agent 4: 传播效能评估 -->
      <div class="agent-card">
        <div class="agent-header">
          <span class="agent-icon">📊</span>
          <span class="agent-title">4. 传播效能评估AI</span>
        </div>
        <div class="agent-body">
          <h4>核心功能：</h4>
          <p style="font-size:0.9em; margin:0; color: #5a6a82;">在发布前，预测内容的潜在传播效果和受众反应。</p>
          <h4>主要职责：</h4>
          <ul>
            <li>模拟目标受众对内容的“可读性”和“共鸣度”进行打分。</li>
            <li>检查是否存在易引发误解或负面情绪的表述（“冒犯度”检测）。</li>
            <li>- 预测内容的分享潜力、评论倾向（正面/负面/中性）。</li>
            <li>对不同版本的内容稿件进行效能排序，并提出优化建议。</li>
          </ul>
        </div>
        <div class="agent-footer">
          <span class="input-tag">输入: 内容初稿、策略简报</span>
          <span class="output-tag">输出: 效能评估报告、优化建议</span>
        </div>
      </div>

      <!-- Agent 5: 多模态适配 -->
      <div class="agent-card">
        <div class="agent-header">
          <span class="agent-icon">🎬</span>
          <span class="agent-title">5. 多模态适配AI</span>
        </div>
        <div class="agent-body">
          <h4>核心功能：</h4>
          <p style="font-size:0.9em; margin:0; color: #5a6a82;">
            将最终确定的文本内容，一键适配为多种媒体格式的素材。
          </p>
          <h4>主要职责：</h4>
          <ul>
            <li>将长文稿件提炼为社交媒体帖子摘要和关键词标签。</li>
            <li>将文章内容转换为分镜式短视频脚本。</li>
            <li>生成配套的图片建议或调用文生图AI生成配图。</li>
            <li>将内容转化为信息图（Infographic）的结构化大纲。</li>
          </ul>
        </div>
        <div class="agent-footer">
          <span class="input-tag">输入: 最终内容稿件</span>
          <span class="output-tag">输出: 视频脚本、社媒摘要、图片建议</span>
        </div>
      </div>

      <!-- Agent 6: 知识库反哺与迭代 -->
      <div class="agent-card">
        <div class="agent-header">
          <span class="agent-icon">🧠</span>
          <span class="agent-title">6. 知识库迭代AI</span>
        </div>
        <div class="agent-body">
          <h4>核心功能：</h4>
          <p style="font-size:0.9em; margin:0; color: #5a6a82;">学习已发布内容的真实市场反馈，持续优化两大核心知识库。</p>
          <h4>主要职责：</h4>
          <ul>
            <li>分析已发布内容的真实互动数据（点赞、分享、评论）。</li>
            <li>从成功的案例中提炼新的、有效的话语风格和叙事模式。</li>
            <li>将未被市场验证或引发负面反馈的表达方式加入“待观察/规避”列表。</li>
            <li>根据真实反馈，更新“涉中话题库”中各话题在特定区域的热度。</li>
          </ul>
        </div>
        <div class="agent-footer">
          <span class="input-tag">输入: 真实市场反馈数据</span>
          <span class="output-tag">输出: 更新后的话题库与话语风格库</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

</script>

<style lang="less" scoped>
.container {
  max-width: 1200px;
  margin: auto;
  background-color: #ffffff;
  padding: 25px 40px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.header {
  text-align: center;
  border-bottom: 2px solid #e0e6ed;
  padding-bottom: 20px;
  margin-bottom: 30px;
}

.header h1 {
  color: #1a2a45;
  font-size: 2em;
  margin: 0;
}

.header p {
  color: #5a6a82;
  font-size: 1.1em;
  margin-top: 5px;
}

.section-title {
  font-size: 1.5em;
  color: #0056b3;
  margin-top: 30px;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ddd;
}

.agent-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 25px;
}

.agent-card {
  background: #fff;
  border: 1px solid #e0e6ed;
  border-radius: 10px;
  padding: 20px;
  transition:
    transform 0.3s,
    box-shadow 0.3s;
  display: flex;
  flex-direction: column;
}

.agent-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.agent-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.agent-icon {
  font-size: 1.8em;
  margin-right: 15px;
}

.agent-title {
  font-size: 1.2em;
  font-weight: 600;
  color: #1a2a45;
}

.agent-body {
  flex-grow: 1;
}

.agent-body h4 {
  font-size: 0.95em;
  color: #0056b3;
  margin-top: 15px;
  margin-bottom: 5px;
}

.agent-body ul {
  padding-left: 20px;
  margin: 0;
  color: #5a6a82;
  font-size: 0.9em;
}

.agent-footer {
  margin-top: 20px;
  font-size: 0.8em;
  color: #8899aa;
}

.agent-footer span {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 12px;
  margin-right: 8px;
  font-weight: 500;
}

.input-tag {
  background-color: #e7f3ff;
  color: #0056b3;
}

.output-tag {
  background-color: #e4f8f0;
  color: #1b8763;
  margin-top: 8px;
}

.footer {
  text-align: center;
  margin-top: 40px;
  font-size: 0.9em;
  color: #999;
}
</style>
