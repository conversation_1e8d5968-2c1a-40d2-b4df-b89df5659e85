<template>
  <div class="custom-head-to">
    <div class="custom-head-top from-[#2c628e] to-[#5fb2ff] bg-gradient-to-r p-4 pt-8">
      <div class="text-center text-8 c-#fff font-bold">东盟精准画像</div>
      <div class="my-6 text-center text-3 c-#fff">点击国家卡片查看详细信息</div>
      <div class="flex flex-wrap justify-center gap-4">
        <div
          v-for="country in countries"
          :key="country.name"
          class="relative h-80px w-120px flex flex-col cursor-pointer items-center justify-center border-2 transition-all duration-200"
          :class="currentNav === country.name ? 'border-[#1765ad] bg-[#eaf3fb] shadow-lg' : 'border-transparent bg-white hover:border-[#1765ad] hover:bg-[#f1f6fa]'"
          @click="currentNav = country.name"
        >
          <img
            :class="currentNav === country.name ? 'scale-110 duration-200 size-full' : 'scale-100 duration-200 size-full'"
            :src="country.logo"
            :alt="country.name"
          >
          <div class="absolute bottom-0 left-1/2 w-100% translate-x--1/2 text-center text-[#22314a] font-bold text-shadow-[0px_2px_4px_rgba(0,0,0,0.15)]" :class="currentNav === country.name ? 'text-[#1765ad]' : ''">{{ country.name }}</div>
        </div>
      </div>
    </div>
  </div>
  <div class="m-auto mt-8 w-80%">
    <!-- Detailed Information Tabs -->
    <a-card class="ch2-tabs-card shadow-sm">
      <a-tabs v-model:active-key="activeTab" type="card" centered>
        <a-tab-pane key="overview">
          <template #tab>
            <span class="flex items-center gap-1">
              <GlobalOutlined />
              <span class="hidden sm:inline">国情概况</span>
            </span>
          </template>
          <div class="space-y-6">
            <!-- 国家标题和基本信息 -->
            <div class="border border-blue-100 rounded-lg from-blue-50 to-indigo-50 bg-gradient-to-r p-6">
              <div class="mb-4 flex items-center gap-4">
                <div class="flex items-center gap-3">
                  <div class="h-12 w-16 flex items-center justify-center border rounded bg-gray-200 text-2xl">
                    <img class="h-full w-full" :src="countries.find(v => v.name === currentNav)!.logo!" alt="">
                  </div>
                  <div>
                    <h2 class="text-2xl text-gray-800 font-bold">{{ currentCountry?.name }}</h2>
                    <p class="text-gray-500">{{ currentCountry?.englishName }}</p>
                  </div>
                </div>
              </div>

              <!-- 核心指标卡片 -->
              <div class="grid grid-cols-2 gap-4 md:grid-cols-4">
                <div class="border border-gray-100 rounded-lg bg-white p-4 shadow-sm transition-shadow hover:shadow-md">
                  <div class="mb-2 flex items-center gap-2">
                    <div class="h-8 w-8 flex items-center justify-center rounded-full bg-blue-100">
                      <span class="text-sm text-blue-600">🏛️</span>
                    </div>
                    <span class="text-sm text-gray-500 font-medium">首都</span>
                  </div>
                  <p class="text-lg text-gray-800 font-semibold">{{ currentCountry?.capital }}</p>
                </div>

                <div class="border border-gray-100 rounded-lg bg-white p-4 shadow-sm transition-shadow hover:shadow-md">
                  <div class="mb-2 flex items-center gap-2">
                    <div class="h-8 w-8 flex items-center justify-center rounded-full bg-green-100">
                      <span class="text-sm text-green-600">👥</span>
                    </div>
                    <span class="text-sm text-gray-500 font-medium">人口</span>
                  </div>
                  <p class="text-lg text-gray-800 font-semibold">{{ currentCountry?.population }}</p>
                </div>

                <div class="border border-gray-100 rounded-lg bg-white p-4 shadow-sm transition-shadow hover:shadow-md">
                  <div class="mb-2 flex items-center gap-2">
                    <div class="h-8 w-8 flex items-center justify-center rounded-full bg-orange-100">
                      <span class="text-sm text-orange-600">🗺️</span>
                    </div>
                    <span class="text-sm text-gray-500 font-medium">面积</span>
                  </div>
                  <p class="text-lg text-gray-800 font-semibold">{{ currentCountry?.area }}</p>
                </div>

                <div class="border border-gray-100 rounded-lg bg-white p-4 shadow-sm transition-shadow hover:shadow-md">
                  <div class="mb-2 flex items-center gap-2">
                    <div class="h-8 w-8 flex items-center justify-center rounded-full bg-purple-100">
                      <span class="text-sm text-purple-600">💰</span>
                    </div>
                    <span class="text-sm text-gray-500 font-medium">GDP</span>
                  </div>
                  <p class="text-lg text-gray-800 font-semibold">{{ currentCountry?.gdp }}</p>
                </div>
              </div>
            </div>

            <!-- 详细信息区域 -->
            <div class="grid gap-6 lg:grid-cols-2">
              <!-- 经济概况 -->
              <a-card class="border-gray-200 shadow-sm transition-shadow hover:shadow-md">
                <template #title>
                  <div class="flex items-center gap-2">
                    <div class="h-6 w-6 flex items-center justify-center rounded-full bg-blue-100">
                      <span class="text-xs text-blue-600">📊</span>
                    </div>
                    <span class="text-gray-800 font-semibold">经济概况</span>
                  </div>
                </template>

                <div class="space-y-4">
                  <div v-if="currentCountry?.economy" class="rounded-lg bg-gray-50 p-4">
                    <p class="text-gray-700 leading-relaxed">{{ currentCountry?.economy }}</p>
                  </div>
                  <div v-else class="rounded-lg bg-gray-50 p-4">
                    <p class="text-gray-700 leading-relaxed">
                      {{ currentCountry?.name }}是东南亚地区重要的经济体，在区域经济一体化中发挥重要作用。
                    </p>
                  </div>

                  <div class="grid grid-cols-1 gap-3">
                    <div class="flex items-center justify-between border border-gray-100 rounded bg-white px-3 py-2">
                      <span class="flex items-center gap-2 text-gray-600">
                        <span class="text-yellow-500">💱</span>
                        货币
                      </span>
                      <span class="text-gray-800 font-semibold">{{ currentCountry?.currency }}</span>
                    </div>
                    <div class="flex items-center justify-between border border-gray-100 rounded bg-white px-3 py-2">
                      <span class="flex items-center gap-2 text-gray-600">
                        <span class="text-red-500">🏛️</span>
                        政治体制
                      </span>
                      <span class="text-gray-800 font-semibold">{{ currentCountry?.government.type }}</span>
                    </div>
                  </div>
                </div>
              </a-card>

              <!-- 政府信息 -->
              <a-card class="border-gray-200 shadow-sm transition-shadow hover:shadow-md">
                <template #title>
                  <div class="flex items-center gap-2">
                    <div class="h-6 w-6 flex items-center justify-center rounded-full bg-red-100">
                      <span class="text-xs text-red-600">🏛️</span>
                    </div>
                    <span class="text-gray-800 font-semibold">政府信息</span>
                  </div>
                </template>

                <div class="space-y-4">
                  <div class="rounded-lg bg-gray-50 p-4">
                    <div class="grid grid-cols-1 gap-3">
                      <div class="flex items-center justify-between border border-gray-100 rounded bg-white px-3 py-2">
                        <span class="flex items-center gap-2 text-gray-600">
                          <span class="text-blue-500">👑</span>
                          国家元首
                        </span>
                        <span class="text-gray-800 font-semibold">{{ currentCountry?.government.president }}</span>
                      </div>
                      <div class="flex items-center justify-between border border-gray-100 rounded bg-white px-3 py-2">
                        <span class="flex items-center gap-2 text-gray-600">
                          <span class="text-green-500">🤝</span>
                          政府首脑
                        </span>
                        <span class="text-gray-800 font-semibold">{{ currentCountry?.government.primeMinister }}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 class="mb-3 flex items-center gap-2 text-sm text-gray-700 font-medium">
                      <span class="text-purple-500">🏗️</span>
                      政府架构
                    </h4>
                    <div class="space-y-2">
                      <div
                        v-for="(item, index) in currentCountry?.government.structure" :key="index"
                        class="flex items-center gap-3 border border-gray-100 rounded bg-white px-3 py-2"
                      >
                        <div class="h-2 w-2 flex-shrink-0 rounded-full bg-blue-500" />
                        <span class="text-sm text-gray-700">{{ item }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </a-card>
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="government">
          <template #tab>
            <span class="flex items-center gap-1">
              <BankOutlined />
              <span class="hidden sm:inline">政府架构</span>
            </span>
          </template>
          <div class="space-y-6">
            <!-- 政治体制概览 -->
            <div class="border border-indigo-100 rounded-lg from-indigo-50 to-purple-50 bg-gradient-to-r p-6">
              <div class="mb-4 flex items-center gap-3">
                <div class="h-12 w-12 flex items-center justify-center rounded-full bg-indigo-100">
                  <span class="text-2xl">🏛️</span>
                </div>
                <div>
                  <h2 class="text-xl text-gray-800 font-bold">{{ currentCountry?.name }}政治体制</h2>
                  <p class="text-indigo-600 font-medium">{{ currentCountry?.government.type }}</p>
                </div>
              </div>

              <!-- 核心领导人信息 -->
              <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div class="border border-gray-100 rounded-lg bg-white p-4 shadow-sm">
                  <div class="mb-3 flex items-center gap-3">
                    <div class="h-10 w-10 flex items-center justify-center rounded-full bg-blue-100">
                      <span class="text-blue-600">👑</span>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500 font-medium">国家元首</p>
                      <p class="text-gray-800 font-semibold">{{ currentCountry?.government.president }}</p>
                    </div>
                  </div>
                  <div class="rounded bg-gray-50 px-2 py-1 text-xs text-gray-500">
                    作为国家的象征和代表，行使国家最高权力
                  </div>
                </div>

                <div class="border border-gray-100 rounded-lg bg-white p-4 shadow-sm">
                  <div class="mb-3 flex items-center gap-3">
                    <div class="h-10 w-10 flex items-center justify-center rounded-full bg-green-100">
                      <span class="text-green-600">🤝</span>
                    </div>
                    <div>
                      <p class="text-sm text-gray-500 font-medium">政府首脑</p>
                      <p class="text-gray-800 font-semibold">{{ currentCountry?.government.primeMinister }}</p>
                    </div>
                  </div>
                  <div class="rounded bg-gray-50 px-2 py-1 text-xs text-gray-500">
                    负责政府日常运作和政策执行
                  </div>
                </div>
              </div>
            </div>

            <!-- 政府架构详情 -->
            <div class="grid gap-6 lg:grid-cols-2">
              <!-- 政府机构层级 -->
              <a-card class="border-gray-200 shadow-sm transition-shadow hover:shadow-md">
                <template #title>
                  <div class="flex items-center gap-2">
                    <div class="h-6 w-6 flex items-center justify-center rounded-full bg-purple-100">
                      <span class="text-xs text-purple-600">🏗️</span>
                    </div>
                    <span class="text-gray-800 font-semibold">政府机构层级</span>
                  </div>
                </template>

                <div class="space-y-3">
                  <div
                    v-for="(item, index) in currentCountry?.government.structure" :key="index"
                    class="relative"
                  >
                    <div class="flex items-start gap-4 rounded-lg bg-gray-50 p-3 transition-colors hover:bg-gray-100">
                      <div class="h-12 w-12 flex flex-shrink-0 items-center justify-center border-2 border-blue-200 rounded-full bg-white shadow-sm">
                        <span class="text-sm text-blue-600 font-bold">{{ index + 1 }}</span>
                      </div>
                      <div class="flex-1">
                        <h4 class="mb-1 text-gray-800 font-medium">{{ item }}</h4>
                        <p class="text-xs text-gray-500">
                          {{ getInstitutionDescription(item) }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </a-card>

              <!-- 权力分工 -->
              <a-card class="border-gray-200 shadow-sm transition-shadow hover:shadow-md">
                <template #title>
                  <div class="flex items-center gap-2">
                    <div class="h-6 w-6 flex items-center justify-center rounded-full bg-orange-100">
                      <span class="text-xs text-orange-600">⚖️</span>
                    </div>
                    <span class="text-gray-800 font-semibold">三权分立体系</span>
                  </div>
                </template>

                <div class="space-y-4">
                  <div class="border-l-4 border-blue-400 rounded-lg bg-blue-50 p-4">
                    <div class="mb-2 flex items-center gap-2">
                      <span class="text-blue-600">📜</span>
                      <h4 class="text-blue-800 font-medium">立法权</h4>
                    </div>
                    <p class="text-sm text-blue-700">
                      {{ currentCountry?.government.legislative.name }} - {{ currentCountry?.government.legislative.description }}
                    </p>
                    <div class="mt-2 text-xs text-blue-600">
                      负责制定法律、监督政府、批准预算
                    </div>
                  </div>

                  <div class="border-l-4 border-green-400 rounded-lg bg-green-50 p-4">
                    <div class="mb-2 flex items-center gap-2">
                      <span class="text-green-600">🏛️</span>
                      <h4 class="text-green-800 font-medium">行政权</h4>
                    </div>
                    <p class="text-sm text-green-700">
                      {{ currentCountry?.government.executive.name }} - {{ currentCountry?.government.executive.description }}
                    </p>
                    <div class="mt-2 text-xs text-green-600">
                      负责执行法律、管理国家事务、制定政策
                    </div>
                  </div>

                  <div class="border-l-4 border-purple-400 rounded-lg bg-purple-50 p-4">
                    <div class="mb-2 flex items-center gap-2">
                      <span class="text-purple-600">⚖️</span>
                      <h4 class="text-purple-800 font-medium">司法权</h4>
                    </div>
                    <p class="text-sm text-purple-700">
                      {{ currentCountry?.government.judicial.name }} - {{ currentCountry?.government.judicial.description }}
                    </p>
                    <div class="mt-2 text-xs text-purple-600">
                      负责解释法律、审理案件、维护司法公正
                    </div>
                  </div>
                </div>
              </a-card>
            </div>

            <!-- 政治制度特色 -->
            <a-card class="border-gray-200 shadow-sm">
              <template #title>
                <div class="flex items-center gap-2">
                  <div class="h-6 w-6 flex items-center justify-center rounded-full bg-red-100">
                    <span class="text-xs text-red-600">🌟</span>
                  </div>
                  <span class="text-gray-800 font-semibold">政治制度特色</span>
                </div>
              </template>

              <div class="rounded-lg from-gray-50 to-blue-50 bg-gradient-to-r p-6">
                <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <div class="text-center">
                    <div class="mx-auto mb-3 h-16 w-16 flex items-center justify-center rounded-full bg-blue-100">
                      <span class="text-2xl">🗳️</span>
                    </div>
                    <h4 class="mb-2 text-gray-800 font-medium">选举制度</h4>
                    <p class="text-sm text-gray-600">
                      {{ currentCountry?.government.electoral.description }}
                    </p>
                  </div>

                  <div class="text-center">
                    <div class="mx-auto mb-3 h-16 w-16 flex items-center justify-center rounded-full bg-green-100">
                      <span class="text-2xl">🤝</span>
                    </div>
                    <h4 class="mb-2 text-gray-800 font-medium">政党制度</h4>
                    <p class="text-sm text-gray-600">
                      {{ currentCountry?.government.party.description }}
                    </p>
                  </div>

                  <div class="text-center">
                    <div class="mx-auto mb-3 h-16 w-16 flex items-center justify-center rounded-full bg-purple-100">
                      <span class="text-2xl">🌐</span>
                    </div>
                    <h4 class="mb-2 text-gray-800 font-medium">地方治理</h4>
                    <p class="text-sm text-gray-600">
                      {{ currentCountry?.government.local.description }}
                    </p>
                  </div>
                </div>
              </div>
            </a-card>
          </div>
        </a-tab-pane>

        <a-tab-pane key="companies">
          <template #tab>
            <span class="flex items-center gap-1">
              <RiseOutlined />
              <span class="hidden sm:inline">政企清单</span>
            </span>
          </template>
          <div class="space-y-5">
            <!-- 专业标题区域 -->
            <div class="border border-blue-100 rounded-lg from-blue-50 to-indigo-50 bg-gradient-to-r p-4">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-blue-100">
                    <span class="text-xl">🏢</span>
                  </div>
                  <div>
                    <h2 class="text-lg text-gray-800 font-bold">{{ currentCountry?.name }}重点企业</h2>
                    <p class="text-sm text-blue-600">企业总数：{{ currentCountry?.companies?.length || 0 }} 家</p>
                  </div>
                </div>
                <div class="hidden items-center gap-4 text-sm text-gray-600 sm:flex">
                  <div class="flex items-center gap-1">
                    <div class="h-3 w-3 rounded bg-blue-500" />
                    <span>国有企业</span>
                  </div>
                  <div class="flex items-center gap-1">
                    <div class="h-3 w-3 rounded bg-green-500" />
                    <span>私营企业</span>
                  </div>
                  <div class="flex items-center gap-1">
                    <div class="h-3 w-3 rounded bg-orange-500" />
                    <span>跨国企业</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 专业企业卡片列表 -->
            <div class="grid gap-4 lg:grid-cols-3 md:grid-cols-2">
              <div
                v-for="(company, index) in currentCountry?.companies" :key="index"
                class="group border border-gray-200 rounded-lg bg-white p-5 transition-all duration-200 hover:border-blue-300 hover:shadow-lg"
              >
                <!-- 企业头部 -->
                <div class="mb-4 flex items-start gap-3">
                  <div class="h-12 w-12 flex items-center justify-center border border-gray-100 rounded-lg bg-gray-50 transition-colors group-hover:border-blue-200">
                    <span class="text-lg">{{ getCompanyIcon(company.type) }}</span>
                  </div>
                  <div class="min-w-0 flex-1">
                    <h3 class="mb-1 text-base text-gray-800 font-semibold leading-tight" :title="company.title">
                      {{ company.title }}
                    </h3>
                    <a-tag :color="getCompanyTypeColor(company.type)" size="small">
                      {{ company.type }}
                    </a-tag>
                  </div>
                </div>

                <!-- 企业信息 -->
                <div class="space-y-3">
                  <!-- 行业信息 -->
                  <div class="flex items-center gap-2 rounded bg-gray-50 p-2 text-sm">
                    <span class="text-blue-500">🏭</span>
                    <span class="text-gray-700 font-medium">行业：</span>
                    <span class="text-gray-600">{{ company.sector }}</span>
                  </div>

                  <!-- 底部装饰线 -->
                  <div class="flex items-center justify-between border-t border-gray-100 pt-2">
                    <div class="flex gap-1">
                      <div class="h-2 w-2 rounded-full bg-blue-300" />
                      <div class="h-2 w-2 rounded-full bg-blue-200" />
                      <div class="h-2 w-2 rounded-full bg-blue-100" />
                    </div>
                    <span class="text-xs text-gray-400">No.{{ String(index + 1).padStart(2, '0') }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 专业空状态 -->
            <div
              v-if="!currentCountry?.companies || currentCountry.companies.length === 0"
              class="border-2 border-gray-200 rounded-lg border-dashed bg-gray-50 py-12 text-center"
            >
              <div class="mx-auto mb-4 h-20 w-20 flex items-center justify-center border border-gray-100 rounded-full bg-white shadow-sm">
                <span class="text-3xl text-gray-400">🏢</span>
              </div>
              <h3 class="mb-2 text-lg text-gray-600 font-semibold">暂无企业数据</h3>
              <p class="mx-auto max-w-sm text-gray-500">该国家的重点企业信息正在收集整理中，请稍后查看</p>
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="news">
          <template #tab>
            <span class="flex items-center gap-1">
              <FileTextOutlined />
              <span class="hidden sm:inline">网络舆情</span>
            </span>
          </template>
          <div class="space-y-4">
            <div v-for="(item, index) in currentCountry?.news" :key="index" class="border-(1px #d9d9d9 solid) py-2 pl-4">
              <div class="mb-2">
                <h3 class="font-semibold">{{ item.titleCn }}</h3>
                <div class="space-x-2">
                  <span>发布于：{{ item.time }}</span>
                  <span>发布渠道：{{ item.source }}</span>
                  <span class="c-primary">涉及领域：{{ item.domain?.join('、') }}</span>
                  <a-tag color="green">{{ item.emotion }}</a-tag>
                </div>
              </div>
              <ExpandableText :content="item.content" />
              <!-- <div v-for="(p, idx) in item.content?.split('\n')" :key="idx" class="text-gray-600">{{ p }}</div> -->
            </div>

            <!-- 空状态 -->
            <div
              v-if="!currentCountry?.news || currentCountry.news.length === 0"
              class="border-2 border-gray-200 rounded-lg border-dashed bg-gray-50 py-12 text-center"
            >
              <div class="mx-auto mb-4 h-20 w-20 flex items-center justify-center border border-gray-100 rounded-full bg-white shadow-sm">
                <span class="text-3xl text-gray-400">📰</span>
              </div>
              <h3 class="mb-2 text-lg text-gray-600 font-semibold">暂无舆情数据</h3>
              <p class="mx-auto max-w-sm text-gray-500">该国家的网络舆情信息正在收集整理中，请稍后查看</p>
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="reports">
          <template #tab>
            <span class="flex items-center gap-1">
              <BookOutlined />
              <span class="hidden sm:inline">研判报告</span>
            </span>
          </template>
          <div class="space-y-4">
            <div v-for="(report, index) in currentCountry?.reports" :key="index" class="border-(1px #d9d9d9 solid) rounded-lg p-4">
              <div class="mb-2 flex items-center justify-between">
                <h3 class="font-semibold">{{ report.title }}</h3>
                <a-tag color="default">{{ report.tags }}</a-tag>
              </div>
              <!-- <p class="mb-2 text-gray-600">{{ report.summary }}</p> -->
              <p class="text-sm text-gray-500">发布日期: {{ report.time }}</p>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!currentCountry?.reports || currentCountry.reports.length === 0"
              class="border-2 border-gray-200 rounded-lg border-dashed bg-gray-50 py-12 text-center"
            >
              <div class="mx-auto mb-4 h-20 w-20 flex items-center justify-center border border-gray-100 rounded-full bg-white shadow-sm">
                <span class="text-3xl text-gray-400">📊</span>
              </div>
              <h3 class="mb-2 text-lg text-gray-600 font-semibold">暂无研判报告</h3>
              <p class="mx-auto max-w-sm text-gray-500">该国家的研判报告正在整理分析中，请稍后查看</p>
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="china-relations">
          <template #tab>
            <span class="flex items-center gap-1">
              <TeamOutlined />
              <span class="hidden sm:inline">涉我关系</span>
            </span>
          </template>
          <div class="space-y-4">
            <div v-for="(relation, index) in currentCountry?.chinaRelations" :key="index" class="border-(1px #d9d9d9 solid) py-2 pl-4">
              <div class="mb-2">
                <h3 class="font-semibold">{{ relation.titleCn }}</h3>
                <div class="space-x-2">
                  <span>发布于：{{ relation.time }}</span>
                  <span>发布渠道：{{ relation.source }}</span>
                  <span class="c-primary">涉及领域：{{ relation.domain?.join('、') }}</span>
                  <a-tag color="green">{{ relation.emotion }}</a-tag>
                </div>
              </div>
              <ExpandableText :content="relation.content" />
              <!-- <p class="text-gray-600">{{ relation.content }}</p> -->
            </div>

            <!-- 空状态 -->
            <div
              v-if="!currentCountry?.chinaRelations || currentCountry.chinaRelations.length === 0"
              class="border-2 border-gray-200 rounded-lg border-dashed bg-gray-50 py-12 text-center"
            >
              <div class="mx-auto mb-4 h-20 w-20 flex items-center justify-center border border-gray-100 rounded-full bg-white shadow-sm">
                <span class="text-3xl text-gray-400">🤝</span>
              </div>
              <h3 class="mb-2 text-lg text-gray-600 font-semibold">暂无涉我关系数据</h3>
              <p class="mx-auto max-w-sm text-gray-500">该国家与中国的相关信息正在收集整理中，请稍后查看</p>
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="us-relations">
          <template #tab>
            <span class="flex items-center gap-1">
              <SafetyOutlined />
              <span class="hidden sm:inline">涉美关系</span>
            </span>
          </template>
          <div class="space-y-4">
            <div v-for="(relation, index) in currentCountry?.usRelations" :key="index" class="border-(1px #d9d9d9 solid) py-2 pl-4">
              <div class="mb-2">
                <h3 class="font-semibold">{{ relation.titleCn }}</h3>
                <div class="space-x-2">
                  <span>发布于：{{ relation.time }}</span>
                  <span>发布渠道：{{ relation.source }}</span>
                  <span class="c-primary">涉及领域：{{ relation.domain?.join('、') }}</span>
                  <a-tag color="green">{{ relation.emotion }}</a-tag>
                </div>
              </div>
              <!-- <p class="text-gray-600">{{ relation.content }}</p> -->
              <ExpandableText :content="relation.content" />
            </div>

            <!-- 空状态 -->
            <div
              v-if="!currentCountry?.usRelations || currentCountry.usRelations.length === 0"
              class="border-2 border-gray-200 rounded-lg border-dashed bg-gray-50 py-12 text-center"
            >
              <div class="mx-auto mb-4 h-20 w-20 flex items-center justify-center border border-gray-100 rounded-full bg-white shadow-sm">
                <span class="text-3xl text-gray-400">🇺🇸</span>
              </div>
              <h3 class="mb-2 text-lg text-gray-600 font-semibold">暂无涉美关系数据</h3>
              <p class="mx-auto max-w-sm text-gray-500">该国家与美国的相关信息正在收集整理中，请稍后查看</p>
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="think-tanks">
          <template #tab>
            <span class="flex items-center gap-1">
              <ReadOutlined />
              <span class="hidden sm:inline">智库研究</span>
            </span>
          </template>
          <div class="space-y-6">
            <!-- 智库概览标题 -->
            <div class="border border-indigo-100 rounded-lg from-indigo-50 to-blue-50 bg-gradient-to-r p-4">
              <div class="flex items-center gap-3">
                <div class="h-10 w-10 flex items-center justify-center rounded-full bg-indigo-100">
                  <span class="text-xl">🎓</span>
                </div>
                <div>
                  <h2 class="text-lg text-gray-800 font-bold">{{ currentCountry?.name }}智库机构</h2>
                  <p class="text-sm text-indigo-600">共 {{ currentCountry?.thinkTanks?.length || 0 }} 家智库机构</p>
                </div>
              </div>
            </div>

            <!-- 智库列表 -->
            <div class="grid gap-6 lg:grid-cols-2">
              <div
                v-for="(tank, index) in currentCountry?.thinkTanks" :key="index"
                class="border border-gray-200 rounded-lg bg-white p-6 shadow-sm transition-shadow hover:shadow-md"
              >
                <!-- 智库标题和图标 -->
                <div class="mb-4 flex items-start gap-4">
                  <div class="h-12 w-12 flex flex-shrink-0 items-center justify-center border border-blue-100 rounded-full bg-blue-50">
                    <span class="text-lg">🏛️</span>
                  </div>
                  <div class="flex-1">
                    <h3 class="mb-2 text-lg text-gray-800 font-semibold leading-tight">{{ tank.title }}</h3>

                    <!-- 机构类型标签 -->
                    <div class="mb-3 flex flex-wrap gap-2">
                      <a-tag
                        v-for="item in tank.domain" :key="item"
                        :color="getDomainColor(item)"
                        size="small"
                      >
                        {{ item }}
                      </a-tag>
                    </div>
                  </div>
                </div>

                <!-- 研究重点 -->
                <div class="border border-gray-100 rounded-lg bg-gray-50 p-4">
                  <div class="mb-2 flex items-center gap-2">
                    <span class="text-blue-500">🔍</span>
                    <span class="text-sm text-gray-700 font-medium">研究重点</span>
                  </div>
                  <p class="text-sm text-gray-600 leading-relaxed">{{ tank.focus }}</p>
                </div>

                <!-- 底部装饰 -->
                <div class="mt-4 border-t border-gray-100 pt-3">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2 text-xs text-gray-500">
                      <span>📊</span>
                      <span>智库机构</span>
                    </div>
                    <div class="flex gap-1">
                      <div class="h-2 w-2 rounded-full bg-blue-300" />
                      <div class="h-2 w-2 rounded-full bg-blue-200" />
                      <div class="h-2 w-2 rounded-full bg-blue-100" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div
              v-if="!currentCountry?.thinkTanks || currentCountry.thinkTanks.length === 0"
              class="py-12 text-center"
            >
              <div class="mx-auto mb-4 h-20 w-20 flex items-center justify-center rounded-full bg-gray-100">
                <span class="text-3xl text-gray-400">🎓</span>
              </div>
              <h3 class="mb-2 text-lg text-gray-600 font-medium">暂无智库数据</h3>
              <p class="text-gray-500">该国家的智库信息正在收集中...</p>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import ExpandableText from '@/components/ExpandableText.vue'

import {
  BankOutlined,
  BookOutlined,
  FileTextOutlined,
  GlobalOutlined,
  ReadOutlined,
  RiseOutlined,
  SafetyOutlined,
  TeamOutlined,
} from '@ant-design/icons-vue'
import { computed, ref } from 'vue'
import RelatedUSList from '../data/最新涉美.json'
import HotTopicList from '../data/热点舆情清单.json'
import ReportList from '../data/研判报告清单.json'
import InvolvinMeList from '../data/越南最新涉我.json'
import logoIndonesia from '../images/logo/印尼.png'
import logoBrunei from '../images/logo/文莱.png'
import logoSingapore from '../images/logo/新加坡.png'
import logoCambodia from '../images/logo/柬埔寨.png'
import logoThailand from '../images/logo/泰国.png'
import logoMyanmar from '../images/logo/缅甸.png'
import logoLaos from '../images/logo/老挝.png'
import logoPhilippines from '../images/logo/菲律宾.png'
import logoVietnam from '../images/logo/越南.png'
import logoMalaysia from '../images/logo/马来西亚.png'

// 类型定义

interface Government {
  type: string
  president: string
  primeMinister: string
  structure: string[]
  // 三权分立体系
  legislative: {
    name: string
    description: string
  }
  executive: {
    name: string
    description: string
  }
  judicial: {
    name: string
    description: string
  }
  // 政治制度特色
  electoral: {
    system: string
    description: string
  }
  party: {
    system: string
    description: string
  }
  local: {
    system: string
    description: string
  }
}

interface Company {
  title: string
  type: string
  sector: string
}

interface Report {
  title: string
  source?: string
  time: string
  tags: string
  link?: string
  date?: string
  type?: string
  summary?: string
}

interface ThinkTank {
  title: string
  domain: string[]
  focus: string
}

interface CountryData {
  name: string
  englishName: string
  flag: string
  capital: string
  population: string
  area: string
  gdp: string
  currency: string
  economy: string
  /** 政府架构 */
  government: Government
  /** 政企 */
  companies: Company[]
  /** 舆情 */
  news: any[]
  /** 报告 */
  reports: Report[]
  /** 涉我 */
  chinaRelations?: any[]
  /** 涉美 */
  usRelations?: any[]
  /** 智库 */
  thinkTanks?: ThinkTank[]
}

const countries = [
  { name: '文莱', logo: logoBrunei },
  { name: '柬埔寨', logo: logoCambodia },
  { name: '印度尼西亚', logo: logoIndonesia },
  { name: '老挝', logo: logoLaos },
  { name: '马来西亚', logo: logoMalaysia },
  { name: '缅甸', logo: logoMyanmar },
  { name: '菲律宾', logo: logoPhilippines },
  { name: '新加坡', logo: logoSingapore },
  { name: '泰国', logo: logoThailand },
  { name: '越南', logo: logoVietnam },
]

const currentNav = ref('越南')

// 响应式数据
const activeTab = ref<string>('overview')

// 国家详细数据
const countryData: Record<string, CountryData> = {
  越南: {
    name: '越南',
    englishName: 'Vietnam',
    flag: '/placeholder.svg?height=80&width=120&text=🇻🇳',
    capital: '河内',
    population: '9,762万',
    area: '33.1万平方公里',
    gdp: '4,087亿美元',
    currency: '越南盾 (VND)',
    economy: '',
    government: {
      type: '社会主义共和国',
      president: '武文赏',
      primeMinister: '范明政',
      structure: ['国会（立法机关）', '国家主席（国家元首）', '政府（行政机关）', '最高人民法院（司法机关）'],
      legislative: {
        name: '国会',
        description: '越南社会主义共和国的最高国家权力机关',
      },
      executive: {
        name: '政府',
        description: '由总理领导的国家行政机关',
      },
      judicial: {
        name: '最高人民法院',
        description: '国家最高审判机关',
      },
      electoral: {
        system: '间接选举制',
        description: '由各级人民代表大会选举产生',
      },
      party: {
        system: '一党制',
        description: '越南共产党领导',
      },
      local: {
        system: '四级行政体系',
        description: '省、市、县、乡四级行政体系',
      },
    },
    companies: [
      { title: '越南石油集团', type: '国有企业', sector: '能源' },
      { title: '越南电力集团', type: '国有企业', sector: '电力' },
      { title: '军队电信集团', type: '国有企业', sector: '电信' },
      { title: 'FPT集团', type: '私营企业', sector: '科技' },
      { title: 'VinGroup', type: '私营企业', sector: '房地产' },
    ],
    news: HotTopicList,
    reports: ReportList,
    chinaRelations: InvolvinMeList,
    usRelations: RelatedUSList,
    thinkTanks: [
      {
        title: '越南战略与国际研究院',
        domain: ['国有企业', '外交政策', '区域经济', '国防安全'],
        focus: '为越南政府提供外交、安全及宏观经济政策建议',
      },
      {
        title: '越南中央经济管理研究院',
        domain: ['宏观经济', '公共政策', '国企改革', '数字经济'],
        focus: '制定经济改革与发展战略，服务政府决策与企业投资',
      },
      {
        title: '越南社会科学院',
        domain: ['社会科学', '历史文化', '民族学', '语言学', '国际关系'],
        focus: '开展基础理论研究，推动越南社会文化与国际研究',
      },
      {
        title: '越南汉喃研究院',
        domain: ['汉字与喃字文献', '越南文字史', '东亚汉字文化圈比较'],
        focus: '整理与研究越南汉喃文献，推动越南汉字与喃字资源的保护与数字化[^34^]',
      },
      {
        title: '越南外交学院战略研究所',
        domain: ['国际关系', '南海问题', '区域合作', '公共外交'],
        focus: '服务越南外交战略与区域安全研究',
      },
    ],
  },
  泰国: {
    name: '泰国',
    englishName: 'Thailand',
    flag: '/placeholder.svg?height=80&width=120&text=🇹🇭',
    capital: '曼谷',
    population: '7,000万',
    area: '51.3万平方公里',
    gdp: '5,340亿美元',
    currency: '泰铢 (THB)',
    economy: '',
    government: {
      type: '君主立宪制',
      president: '玛哈·哇集拉隆功国王',
      primeMinister: '赛塔·他威信',
      structure: ['国王（国家元首）', '总理（政府首脑）', '国会（立法机关）', '最高法院（司法机关）'],
      legislative: {
        name: '国会',
        description: '由参议院和众议院组成的两院制立法机构',
      },
      executive: {
        name: '内阁',
        description: '由总理领导的政府执行机构',
      },
      judicial: {
        name: '最高法院',
        description: '泰王国最高司法机构',
      },
      electoral: {
        system: '君主立宪制下的议会选举制',
        description: '君主立宪制下的议会选举制',
      },
      party: {
        system: '多党制',
        description: '政党轮流执政',
      },
      local: {
        system: '四级地方政府',
        description: '府、县、区、村四级地方政府',
      },
    },
    companies: [
      {
        title: 'PTT Public Co., Ltd.',
        type: '国有企业',
        sector: '能源',
      },
      {
        title: 'Electricity Generating Authority of Thailand (EGAT)',
        type: '国有企业',
        sector: '能源',
      },
      {
        title: 'Metropolitan Electricity Authority (MEA)',
        type: '国有企业',
        sector: '能源/公用事业',
      },
      {
        title: 'Thailand Post Co., Ltd.',
        type: '国有企业',
        sector: '通信／物流',
      },
      {
        title: 'Airports of Thailand Public Co., Ltd.',
        type: '国有企业',
        sector: '基础设施／航空',
      },
    ],
    news: [
      {
        ai: '2025年1月，中国演员王星在泰国被诈骗团伙拐骗事件引发中国网民广泛关注并对泰国旅游安全表达担忧。',
        domain: [
          '中国',
          '泰国',
          '旅游',
          '安全',
        ],
        emotion: '负面',
        link: '',
        region: [
          '中国',
          '泰国',
        ],
        source: '卫报',
        author: '',
        time: '2025-1-14',
        title: 'Chinese actor Wang Xing abducted in Thailand scam case',
        titleCn: '中国演员王星在泰国被骗拐事件引发关注',
        content: '王星被误导赴泰后被带至缅甸从事诈骗，事件曝光后引发中方高度关注。',
      },
      {
        ai: '2025年1月，中国公安缉捕跨境诈骗和人口贩运嫌疑人，与泰国警方合作调查。',
        domain: [
          '中国',
          '泰国',
          '安全',
          '跨境犯罪',
        ],
        emotion: '中性',
        link: '',
        region: [
          '中国',
          '泰国',
        ],
        source: '美联社',
        author: '',
        time: '2025-1-27',
        title: 'China detains suspect in human trafficking cases linked to online scam networks in Myanmar and Thailand',
        titleCn: '中国拘捕涉缅泰诈骗网络人口贩卖嫌犯',
        content: '中国与泰国合作行动捣毁诈骗网络，抓捕关键嫌犯。',
      },
      {
        ai: '2025年1月，泰国签证免签政策因安全事件被质疑，但政府表示暂无变更计划。',
        domain: [
          '泰国',
          '中国',
          '旅游',
          '政策',
        ],
        emotion: '谨慎',
        link: '',
        region: [
          '泰国',
          '中国',
        ],
        source: '路透社',
        author: '',
        time: '2025-1-21',
        title: 'Thailand\'s visa waiver for Chinese tourists under fire after string of crimes',
        titleCn: '泰国对中国游客免签政策遭质疑',
        content: '连串治安案件引发对免签政策安全性的讨论。',
      },
      {
        ai: '2025年6月，中国赴泰旅游意愿下降，媒体称游客安全忧虑升高。',
        domain: [
          '中国',
          '泰国',
          '旅游',
          '舆情',
        ],
        emotion: '负面',
        link: '',
        region: [
          '中国',
          '泰国',
        ],
        source: 'Washington Post',
        author: '',
        time: '2025-7-15',
        title: 'Why tourists are turning away from Thailand',
        titleCn: '为何游客远离泰国：安全担忧与负面体验',
        content: '旅游人数下降主要因安全担忧与物价、歧视等问题。',
      },
      {
        ai: '2025年6月，中国社交媒体对坤敬省冲突中柬埔寨领导人言行表示质疑。',
        domain: [
          '中国',
          '泰国',
          '柬埔寨',
          '舆论',
        ],
        emotion: '争议',
        link: '',
        region: [
          '中国',
          '泰国',
          '柬埔寨',
        ],
        source: 'The Nation Thailand',
        author: '',
        time: '2025-6-29',
        title: 'Chinese netizens question Hun Sen’s judgement after fallout in Thai‑Cambodian crisis',
        titleCn: '泰柬危机中中国网民质疑洪森判断',
        content: '中国网友批评柬埔寨洪森公开事件处理方式。',
      },
    ],
    reports: [
      {
        title: 'State of Southeast Asia Survey 2025',
        tags: 'ISEAS‑Yusof Ishak Institute',
        source: 'ISEAS‑Yusof Ishak Institute',
        time: '2025年1‑2月',
        link: '',
      },
      {
        title: 'OECD Review of Corporate Governance of SOEs in Thailand',
        tags: 'OECD',
        source: 'OECD',
        time: '2025年1月',
        link: '',
      },
      {
        title: 'Characterizing Chinese Influence in Thailand',
        tags: 'Air University / JIPA',
        source: 'Air University',
        time: '2023‑2024（近期分析）',
        link: '',
      },
      {
        title: 'Thailand in the Emerging World Order',
        tags: 'Carnegie Endowment',
        source: 'Carnegie Endowment',
        time: '2023年10月',
        link: '',
      },
      {
        title: 'Is China Replacing the US as Thailand\'s Main Security Partner?',
        tags: 'Lowy Institute',
        source: 'Lowy Institute',
        time: '2023年12月',
        link: '',
      },
    ],
    chinaRelations: [
      {
        ai: '2025年1月，中国演员王星在泰国被诈骗团伙拐骗，引发中国教育部和媒体关注。',
        domain: [
          '中国',
          '泰国',
          '安全',
          '留学生/旅客',
        ],
        emotion: '负面',
        link: '',
        region: [
          '中国',
          '泰国',
        ],
        source: '卫报',
        author: '',
        time: '2025-1-14',
        title: 'Chinese actor Wang Xing abducted in Thailand scam case',
        titleCn: '中国演员王星被骗拐事件引中国关注',
        content: '该事件导致中方对赴泰安全发出警示。',
      },
      {
        ai: '2025年1月，中国公安逮捕涉嫌涉泰国诈骗网络嫌犯，泰中警方合作加强打击。',
        domain: [
          '中国',
          '泰国',
          '安全',
          '执法',
        ],
        emotion: '中性',
        link: '',
        region: [
          '中国',
          '泰国',
        ],
        source: '美联社',
        author: '',
        time: '2025-1-27',
        title: 'China detains suspect in human trafficking cases linked to online scam networks in Myanmar and Thailand',
        titleCn: '中方拘捕涉泰缅诈骗贩卖嫌犯',
        content: '突显中泰协作警示旅游风险。',
      },
      {
        ai: '2025年1月，中国社交媒体就旅安问题大量讨论赴泰旅游及诈骗案例。',
        domain: [
          '中国',
          '泰国',
          '舆论',
          '留学生/旅客',
        ],
        emotion: '中性',
        link: '',
        region: [
          '中国',
          '泰国',
        ],
        source: '社交媒体摘要',
        author: '',
        time: '2025-1-2025年1月',
        title: 'Chinese social media discusses Thailand travel safety',
        titleCn: '中国社交媒体热议赴泰旅游安全',
        content: '微博、小红书上大量负面评论与游客警示。',
      },
      {
        ai: '2025年7月，泰柬边境冲突加剧，中国呼吁外交斡旋以保护涉华民众。',
        domain: [
          '中国',
          '柬埔寨',
          '泰国',
          '外交',
        ],
        emotion: '紧张',
        link: '',
        region: [
          '中国',
          '泰国',
          '柬埔寨',
        ],
        source: '新华社等',
        author: '',
        time: '2025-7-25',
        title: 'China urges dialogue amid Thai‑Cambodian clashes to protect nationals',
        titleCn: '中方呼吁泰柬外交对话保护华人',
        content: '中方关注中国公民安全与冲突升级风险。',
      },
      {
        ai: '2025年7月，中国游客人数锐减，中国大使与泰方会谈涉及安全与签证问题。',
        domain: [
          '中国',
          '泰国',
          '旅游',
          '外交',
        ],
        emotion: '中性',
        link: '',
        region: [
          '中国',
          '泰国',
        ],
        source: '多家媒体',
        author: '',
        time: '2025-7-2025年7月',
        title: 'Chinese envoy discusses tourism safety with Thai officials',
        titleCn: '中国大使就旅游安全与泰方会谈',
        content: '会议涉及签证政策与游客安全保障',
      },
    ],
    usRelations: [
      {
        ai: '2025年，ISEAS调研显示东盟国家包括泰国对美国的信任度超过中国。',
        domain: [
          '泰国',
          '美国',
          '中国',
          '信任度',
        ],
        emotion: '正面',
        link: '',
        region: [
          '泰国',
          '美国',
          '中国',
        ],
        source: 'ISEAS‑Yusof Ishak Institute',
        author: '',
        time: '2025年1‑2月',
        title: 'Greater trust in U.S. than China among ASEAN countries',
        titleCn: '东盟国家包括泰国对美国信任超过中国',
        content: '调查显示美国信任度提升，泰国倾向对美靠拢。',
      },
      {
        ai: '2023‑2024年，美国长期盟友泰国维持对美军事合作但同时平衡中国影响。',
        domain: [
          '泰国',
          '美国',
          '中国',
          '安全',
        ],
        emotion: '中性',
        link: '',
        region: [
          '泰国',
          '美国',
          '中国',
        ],
        source: 'Carnegie Endowment',
        author: '',
        time: '2023年10月',
        title: 'Thailand in the Emerging World Order',
        titleCn: '泰国在新兴世界秩序中的定位',
        content: '泰国并不愿牺牲对中关系而加强对美绑定。',
      },
      {
        ai: '2023年冷战研究强调泰国在印太战略中犹豫对美反制中国。',
        domain: [
          '泰国',
          '美国',
          '中国',
          '战略',
        ],
        emotion: '谨慎',
        link: '',
        region: [
          '泰国',
          '美国',
          '中国',
        ],
        source: 'JIPA / Air University',
        author: '',
        time: '2023‑2024',
        title: 'Thailand’s Indo‑Pacific Adrift?: A Reluctant Realignment with the United States',
        titleCn: '泰国在印太地区的动摇：对美重构谨慎',
        content: '研究指出泰国虽盟美却不愿强对抗中国。',
      },
      {
        ai: '近期分析指出泰美仍为主要安全合作伙伴，美国提供军售与联合演习。',
        domain: [
          '泰国',
          '美国',
          '军事',
          '安全合作',
        ],
        emotion: '积极',
        link: '',
        region: [
          '泰国',
          '美国',
        ],
        source: 'Lowy Institute',
        author: '',
        time: '2023年12月',
        title: 'Is China Replacing the US as Thailand\'s Main Security Partner?',
        titleCn: '中国是否正在取代美国成为泰国首要安全伙伴？',
        content: '短期内美国仍为泰国主要安全伙伴。',
      },
      {
        ai: '泰国为美国亚洲最老盟友之一，美泰关系虽波动但战术合作持续。',
        domain: [
          '泰国',
          '美国',
          '外交',
          '历史',
        ],
        emotion: '中性',
        link: '',
        region: [
          '泰国',
          '美国',
        ],
        source: 'DACOR 等',
        author: '',
        time: '长期',
        title: 'U.S.-Thailand alliance remains amid changing geopolitics',
        titleCn: '美泰盟友关系在地缘政治变动中持续',
        content: '尽管政变影响，双方仍维持军事与外交合作。',
      },
    ],
    thinkTanks: [
      {
        title: 'ISEAS‑Yusof Ishak Institute',
        domain: [
          '区域信任',
          '东盟态度',
          '国际关系',
        ],
        focus: '东盟对美中信任度与战略倾向分析',
      },
      {
        title: 'Air University / JIPA',
        domain: [
          '安全战略',
          '中国影响',
          '印太',
        ],
        focus: '中国在泰国的影响力特征与对美关系影响',
      },
      {
        title: 'OECD',
        domain: [
          '国企治理',
          '政策改革',
        ],
        focus: '泰国国有企业治理评估与改革建议',
      },
      {
        title: 'Carnegie Endowment',
        domain: [
          '战略政策',
          '多边关系',
        ],
        focus: '泰国在美中竞争格局中的战略定位',
      },
      {
        title: 'Lowy Institute',
        domain: [
          '安全合作',
          '军事同盟',
        ],
        focus: '泰美军事关系与中国介入风险评估',
      },
    ],
  },
  新加坡: {
    name: '新加坡',
    englishName: 'Singapore',
    flag: '/placeholder.svg?height=80&width=120&text=🇸🇬',
    capital: '新加坡市（Singapore City）',
    population: '590万',
    area: '728平方公里',
    gdp: '3,970亿美元',
    currency: '新加坡元 (SGD)',
    economy: '',
    government: {
      type: '议会共和制',
      president: '哈莉玛·雅各布',
      primeMinister: '李显龙',
      structure: ['总统（国家元首）', '总理（政府首脑）', '国会（立法机关）', '最高法院（司法机关）'],
      legislative: {
        name: '国会',
        description: '一院制议会，行使立法权',
      },
      executive: {
        name: '内阁',
        description: '由总理领导的政府行政部门',
      },
      judicial: {
        name: '最高法院',
        description: '新加坡共和国最高司法权威',
      },
      electoral: {
        system: '议会制共和国',
        description: '定期举行全国大选',
      },
      party: {
        system: '多党制',
        description: '人民行动党长期执政',
      },
      local: {
        system: '统一制国家',
        description: '中央集权管理',
      },
    },
    companies: [
      {
        title: 'Singapore Power Group',
        type: '国有企业',
        sector: '能源',
      },
      {
        title: 'SMRT Corporation',
        type: '国有企业',
        sector: '公共交通',
      },
      {
        title: 'Mediacorp Pte. Ltd.',
        type: '国有企业',
        sector: '媒体/通信',
      },
      {
        title: 'Sembcorp Industries',
        type: '国有企业',
        sector: '基础设施/能源',
      },
      {
        title: 'Singapore Post',
        type: '国有企业',
        sector: '物流/通信',
      },
    ],
    news: [
      {
        ai: '2025年5月，新加坡公众抨击美议员在TikTok听证会中对CEO Chew Shou Zi的国籍与忠诚问题表现无知。',
        domain: [
          '新加坡',
          '美国',
          '外交',
          '公众舆论',
        ],
        emotion: '负面',
        link: 'https://apnews.com/article/af72f8d53686f8bb378aec1193cdee6c',
        region: [
          '新加坡',
          '美国',
        ],
        source: '美联社',
        author: '',
        time: '2025-5-26',
        title: 'Singaporeans bemoan U.S. Senator\'s \'ignorant\' grilling of TikTok CEO',
        titleCn: '新加坡人批评美国议员质询TikTok CEO“无知”',
        content: '公众称美国议员对新加坡背景缺乏了解，误将TikTok CEO视为中共成员，引发民族与国别混淆批评。',
      },
      {
        ai: '2025年香格里拉对话前夕，美防长警告中国威胁“迫在眉睫”，引发新加坡媒体警觉。',
        domain: [
          '美国',
          '中国',
          '新加坡',
          '国防',
        ],
        emotion: '紧张',
        link: 'https://www.theguardian.com/us-news/2025/may/31/pete-hegseth-calls-on-asia-to-boost-military-spending-in-face-of-imminent-threat-from-china-us-defence-secretary',
        region: [
          '新加坡',
          '美国',
          '中国',
        ],
        source: '卫报',
        author: '',
        time: '2025-5-30',
        title: 'Pete Hegseth calls on Asia to boost military spending',
        titleCn: '美国防长呼吁亚洲提升军费应对中国威胁',
        content: '美国表态引发新加坡战略学界对冲突升级风险的公开担忧。',
      },
      {
        ai: '2025年5月，路透社称新加坡主办的香会面临“特朗普再当选”的地区政策不确定性。',
        domain: [
          '新加坡',
          '美国',
          '外交',
          '地缘政治',
        ],
        emotion: '中性',
        link: 'https://www.reuters.com/world/china/uncertainties-over-trump-ukraine-loom-large-ahead-asian-defence-meeting-2025-05-26/',
        region: [
          '新加坡',
          '美国',
        ],
        source: '路透社',
        author: '',
        time: '2025-5-26',
        title: 'Uncertainties over Trump loom large at Singapore dialogue',
        titleCn: '特朗普因素影响香格里拉对话氛围',
        content: '各国代表私下表示对美政策连续性担忧，新加坡保持中立东道立场。',
      },
      {
        ai: '2025年ISEAS发布地区民调，新加坡公众对美信任度首次超过对中国的信任。',
        domain: [
          '新加坡',
          '中国',
          '美国',
          '民意',
        ],
        emotion: '中性',
        link: 'https://time.com/7274803/southeast-asia-2025-survey-us-china-trust-asean-iseas-trump/',
        region: [
          '新加坡',
        ],
        source: 'TIME',
        author: '',
        time: '2025-7',
        title: 'ASEAN Trust Survey: U.S. favored over China',
        titleCn: '东南亚信任度调查：新加坡更偏向美国',
        content: '调查显示若必须选边，多数新加坡人倾向信任美国。',
      },
      {
        ai: '2025年Ipsos调查，新加坡人对中国影响力持“谨慎支持”态度。',
        domain: [
          '新加坡',
          '中国',
          '民调',
        ],
        emotion: '中性',
        link: '',
        region: [
          '新加坡',
        ],
        source: 'Ipsos',
        author: '',
        time: '2025-4',
        title: 'Ipsos Singapore China Influence Poll',
        titleCn: '新加坡公众对中国影响认知调查',
        content: '63%认为中国经济影响重大但政治影响须防控，显示务实态度。',
      },
    ],
    reports: [
      {
        title: 'A Small State Heavyweight? How Singapore Handles U.S.-China Rivalry',
        tags: 'USIP',
        source: 'United States Institute of Peace',
        time: '2024年4月',
        link: 'https://www.usip.org/publications/2024/04/small-state-heavyweight-how-singapore-handles-us-china-rivalry',
      },
      {
        title: 'Singapore\'s Efforts to Navigate US–China Strategic Rivalry',
        tags: 'Springer Book Chapter',
        source: 'Springer',
        time: '2023年',
        link: 'https://link.springer.com/chapter/10.1007/978-3-031-15389-1_12',
      },
      {
        title: 'Singapore’s Balancing Act Amidst US‑China Indo‑Pacific Competition',
        tags: 'LSE Grimshaw Review',
        source: 'London School of Economics',
        time: '2025年',
        link: 'https://grimshawreview.lse.ac.uk/articles/25',
      },
      {
        title: 'Public Opinion vs. Official Policy: Singapore’s China Dilemma',
        tags: 'Opinion Analysis',
        source: 'Riotimes',
        time: '2024年10月',
        link: 'https://www.riotimesonline.com/public-opinion-vs-official-policy-singapores-china-dilemma',
      },
      {
        title: 'US‑Singapore cooperation on tech and security',
        tags: 'arXiv Paper',
        source: 'arXiv',
        time: '2024年8月',
        link: 'https://arxiv.org/abs/2408.07946',
      },
    ],
    chinaRelations: [
      {
        ai: '2025年香格里拉对话中，中国代表与新加坡就台湾表态存在分歧。',
        domain: [
          '中国',
          '新加坡',
          '台湾',
          '外交',
        ],
        emotion: '争议',
        link: '',
        region: [
          '中国',
          '新加坡',
        ],
        source: '新加坡媒体联合早报',
        author: '',
        time: '2025-6',
        title: 'Singapore–China divergence over Taiwan at Shangri-La',
        titleCn: '香会期间中新就台湾表态存分歧',
        content: '中新在闭门会议中讨论区域敏感议题，外交上保持克制但分歧明显。',
      },
      {
        ai: '2025年ISEAS报告指出中国对新加坡“认知战”尝试加强，表现为对媒体、商界影响渗透。',
        domain: [
          '中国',
          '新加坡',
          '认知战',
          '舆情',
        ],
        emotion: '警惕',
        link: '',
        region: [
          '新加坡',
        ],
        source: 'ISEAS',
        author: '',
        time: '2025-6',
        title: 'China’s Influence Strategy in Singapore: Soft, Sharp, Smart',
        titleCn: '中国在新加坡的影响战略研究',
        content: '报告列出中方在新加坡影响路径，包括商业赞助与学术合作渗透。',
      },
      {
        ai: '2025年初，新加坡社交平台出现亲中论调争议，部分疑似“信息放大账户”受到质疑。',
        domain: [
          '中国',
          '新加坡',
          '社交媒体',
        ],
        emotion: '争议',
        link: '',
        region: [
          '新加坡',
        ],
        source: '南华早报',
        author: '',
        time: '2025-2',
        title: 'Pro-China narratives spark debate in Singapore social media',
        titleCn: '新加坡社媒亲中言论引发争议',
        content: '部分网民质疑舆论引导背后有境外力量介入。',
      },
      {
        ai: '新加坡多所高校警惕与中方合作中的“数据主权问题”。',
        domain: [
          '中国',
          '新加坡',
          '教育',
          '数据',
        ],
        emotion: '警惕',
        link: '',
        region: [
          '新加坡',
        ],
        source: '学术交流圈',
        author: '',
        time: '2025-5',
        title: 'Singapore universities wary of China research links',
        titleCn: '新加坡高校警惕与中方科研合作数据主权风险',
        content: '部分联合项目暂停或重审数据跨境流动条款。',
      },
      {
        ai: '新加坡官员称希望中方理解本国“不选边”立场并尊重主权选择。',
        domain: [
          '中国',
          '新加坡',
          '外交',
        ],
        emotion: '中性',
        link: '',
        region: [
          '新加坡',
        ],
        source: '联合早报',
        author: '',
        time: '2025-7',
        title: 'Singapore reiterates non-aligned stance to China',
        titleCn: '新加坡重申不选边立场',
        content: '强调愿与中美均保持合作，但主权决策应被尊重。',
      },
    ],
    usRelations: [
      {
        ai: '2025年美国防长在香会强调加强与新加坡的科技与安全合作。',
        domain: [
          '新加坡',
          '美国',
          '安全',
          '科技',
        ],
        emotion: '积极',
        link: '',
        region: [
          '新加坡',
          '美国',
        ],
        source: '美国国防部',
        author: '',
        time: '2025-5',
        title: 'U.S. to deepen tech and defense ties with Singapore',
        titleCn: '美国将深化与新加坡的科技与防务合作',
        content: '包括AI、量子通信与海事巡航合作框架。',
      },
      {
        ai: '2025年5月，《TIME》报道ASEAN民调显示新加坡对美信任持续上升。',
        domain: [
          '新加坡',
          '美国',
          '舆情',
        ],
        emotion: '积极',
        link: 'https://time.com/7274803/southeast-asia-2025-survey-us-china-trust-asean-iseas-trump/',
        region: [
          '新加坡',
          '美国',
        ],
        source: 'TIME',
        author: '',
        time: '2025-5',
        title: 'Survey: Singaporeans trust U.S. more than China',
        titleCn: '调查显示新加坡更信任美国',
        content: 'ASEAN 国家中信任美方最高者为新加坡。',
      },
      {
        ai: '2025年美新联合演习“Pacific Guardian”成功举行，加强地区互操作性。',
        domain: [
          '美国',
          '新加坡',
          '军演',
        ],
        emotion: '中性',
        link: '',
        region: [
          '新加坡',
          '美国',
        ],
        source: '星报',
        author: '',
        time: '2025-6',
        title: 'U.S.–Singapore joint drill reinforces Indo-Pacific readiness',
        titleCn: '美新联合军演强化印太备战能力',
        content: '双方强调维持地区秩序与航行自由。',
      },
      {
        ai: '美国务院称新加坡是印太“可信赖合作伙伴”，将推动更多外交支持。',
        domain: [
          '美国',
          '新加坡',
          '外交',
        ],
        emotion: '积极',
        link: '',
        region: [
          '新加坡',
          '美国',
        ],
        source: '美国国务院',
        author: '',
        time: '2025-4',
        title: 'U.S. labels Singapore key Indo-Pacific partner',
        titleCn: '美国称新加坡为印太关键伙伴',
        content: '报告中高度评价其“中立稳定外交角色”。',
      },
      {
        ai: '新加坡评论员称应防止在中美之间被“工具化”，美国需尊重小国角色。',
        domain: [
          '新加坡',
          '美国',
          '外交',
          '中立',
        ],
        emotion: '谨慎',
        link: '',
        region: [
          '新加坡',
        ],
        source: '联合早报评论',
        author: '',
        time: '2025-6',
        title: 'Singapore must avoid being a pawn',
        titleCn: '新加坡应避免沦为棋子',
        content: '评论呼吁美方理解东盟“战略自主”的必要。',
      },
    ],
    thinkTanks: [
      {
        title: 'ISEAS-Yusof Ishak Institute',
        domain: [
          '东南亚政策',
          '对中美态度',
          '舆情',
        ],
        focus: '新加坡及东盟国家对中美信任度调查与政策走向',
      },
      {
        title: 'Rajaratnam School of International Studies (RSIS)',
        domain: [
          '安全',
          '海事战略',
          '地区外交',
        ],
        focus: '新加坡防务政策与区域安全架构',
      },
      {
        title: 'Singapore Institute of International Affairs (SIIA)',
        domain: [
          '外交政策',
          '可持续发展',
        ],
        focus: '区域外交与绿色发展议程',
      },
      {
        title: 'Institute of Policy Studies (IPS)',
        domain: [
          '国家治理',
          '社会信任',
        ],
        focus: '新加坡民意、国家认同与战略韧性',
      },
      {
        title: 'USIP – Southeast Asia Program',
        domain: [
          '地区战略',
          '大国竞争',
        ],
        focus: '新加坡在印太美中竞争中的角色研究',
      },
    ],
  },
  文莱: {
    name: '文莱',
    englishName: 'Brunei Darussalam',
    flag: '/placeholder.svg?height=80&width=120&text=🇸🇬',
    capital: '斯里巴加湾市（Bandar Seri Begawan）',
    population: '44.3万',
    area: '5,769平方公里',
    gdp: '14.0亿美元',
    currency: '文莱元（B$）',
    economy: '以石油和天然气产业为主导（占GDP超60%），金融业以伊斯兰金融为特色',
    government: {
      type: '绝对君主制（苏丹为国家元首、政府首脑及宗教领袖）',
      president: '苏丹哈吉·哈桑纳尔·博尔基亚',
      primeMinister: '由苏丹兼任',
      structure: ['国家元首', '立法委员会（立法机构）', '内阁（行政机构）', '最高法院（司法机构）'],
      legislative: {
        name: '立法委员会',
        description: '协助苏丹制定法律的咨询机构',
      },
      executive: {
        name: '内阁',
        description: '由苏丹直接领导的政府机构',
      },
      judicial: {
        name: '最高法院',
        description: '文莱达鲁萨兰国最高司法机构',
      },
      electoral: {
        system: '绝对君主制',
        description: '苏丹世袭统治',
      },
      party: {
        system: '无政党制度',
        description: '苏丹直接统治',
      },
      local: {
        system: '四个县级行政区划',
        description: '四个县级行政区划',
      },
    },
    companies: [
      { title: 'Electricité du Cambodge', type: '国有企业', sector: '能源／电力' },
      { title: 'Phnom Penh Water Supply Authority', type: '国有企业', sector: '公用事业／供水' },
      { title: 'Sihanoukville Autonomous Port', type: '国有企业', sector: '港口／海运' },
      { title: 'Telecom Cambodia', type: '国有企业', sector: '电信／通信' },
      { title: 'Cambodia Shipping Agency', type: '国有企业', sector: '航运服务' },
      { title: 'Cambodia Postal Services', type: '国有企业', sector: '邮政服务' },
      { title: 'Rural Development Bank', type: '国有企业', sector: '金融／农村发展贷款' },
      { title: 'Green Trade Company', type: '国有企业', sector: '粮食储备与交易' },
      { title: 'Printing House (government)', type: '国有企业', sector: '印刷出版' },
      { title: 'Siem Reap Water Supply Authority', type: '国有企业', sector: '公用事业／供水' },
      { title: 'Construction and Public Work Lab', type: '国有企业', sector: '建设技术／公共工程' },
      { title: 'Phnom Penh Autonomous Port', type: '国有企业', sector: '港口／海运' },
      { title: 'Kampcheary Insurance (Camden‑still)', type: '国有企业', sector: '保险业' },
      { title: 'Cambodia Life Insurance', type: '国有企业', sector: '保险业' },
      { title: 'Cambodia Securities Exchange', type: '国有企业', sector: '证券交易所' },
      { title: 'Bank for Investment and Development of Cambodia', type: '国有企业', sector: '银行／金融' },
      { title: 'Printing and Publishing House of Cambodia', type: '国有企业', sector: '传媒／印刷' },
      { title: 'National Television of Cambodia', type: '政府机构', sector: '媒体／广播电视' },
      { title: 'National Radio of Cambodia', type: '政府机构', sector: '媒体／广播' },
      { title: 'Cambodia Airports (政府控股)', type: '国有企业', sector: '航空基础设施' },
    ],
    news: [],
    reports: [
      {
        title: '新加坡数字经济发展报告',
        time: '2024-01-03',
        tags: '年度报告',
        summary: '评估新加坡数字化转型成果',
      },
    ],
    chinaRelations: [
      {
        ai: '中方与文莱发表联合声明，确立深化战略合作伙伴关系，推动一带一路、高质量经贸能源投资以及免签互惠等多领域合作，文莱支持一个中国原则。',
        domain: ['外交', '战略合作', '投资', '一带一路', '能源', '农业', '旅游'],
        emotion: '正面',
        link: '',
        region: ['中国', '文莱'],
        source: '中华人民共和国外交部 / 新浪财经',
        author: '外交部、北京日报客户端',
        time: '2025-02-06',
        title: '中华人民共和国和文莱达鲁萨兰国关于深化战略合作伙伴关系、推进中文命运共同体建设的联合声明（全文）',
        titleCn: '中华人民共和国和文莱达鲁萨兰国关于深化战略合作伙伴关系、推进中文命运共同体建设的联合声明（全文）',
        content: '详述习近平主席与文莱苏丹举行高层会晤并签署联合声明，覆盖经贸、能源、农业、旅游、港口、互免签证等多项合作内容。',
      },
      {
        ai: '习近平强调中—文两国历史友谊与战略契合，呼吁对接发展策略、深化关键领域合作，以造福两国民众。',
        domain: ['外交', '双边关系', '领导人会晤'],
        emotion: '正面',
        link: '',
        region: ['中国', '文莱'],
        source: 'China Daily',
        author: 'China Daily 记者',
        time: '2025-02-07',
        title: 'China, Brunei deepen partnership',
        titleCn: '中国与文莱深化合作伙伴关系',
        content: '报道习近平与文莱苏丹在北京会晤，重申两国战略合作关系和未来合作方向，强调友谊与合作带来的民众利益。',
      },
      {
        ai: '文莱苏丹国事访问期间签署多项文件，强化战略合作，推出互免签证政策，促进中国游客前往文莱，推动文化和商务交流。',
        domain: ['外交', '签证互惠', '文化交流', '旅游'],
        emotion: '正面',
        link: '',
        region: ['文莱', '中国', '东盟'],
        source: 'Asia News Network',
        author: 'Asia News Network 记者',
        time: '约 2025-02',
        title: 'Sultan Hassanal’s state visit highlights Brunei‑China bilateral cooperation ties',
        titleCn: '哈桑纳尔苏丹国事访问凸显文莱‑中国双边合作关系',
        content: '期间签署互免签政策，文莱为中国游客提供14天免签，中国提供文莱公民停留待遇，推动人员往来与交流合作。',
      },
      {
        ai: '文莱高度重视与中国的关系，坚定遵守一个中国政策，把中国视为关键合作伙伴，在经济与文化交流方面与中国互动频繁。',
        domain: ['文化交流', '经济合作', '政策'],
        emotion: '正面',
        link: '',
        region: ['文莱', '中国', '东盟'],
        source: 'The Star',
        author: 'The Star 记者',
        time: '2025-03-28',
        title: 'Brunei‑China relations flourish through economic, cultural exchanges',
        titleCn: '文莱‑中国关系通过经贸与文化交流蓬勃发展',
        content: '强调文莱执行一中政策，将中国视为主要合作伙伴，经贸文化交流活跃，合作趋势持续向好。',
      },
      {
        ai: '尽管文莱与中国在南海部分海域主权主张重叠，但在中国对菲律宾等国家采取强硬姿态时，文莱保持谨慎，并与中国展开油气合作，此举可能被外界解读为地区安全合作或军事意图的一部分。',
        domain: ['地缘政治', '能源', '南海争端', '防务'],
        emotion: '中性',
        link: '',
        region: ['南中国海', '文莱', '中国', '东盟'],
        source: 'VOA 中文',
        author: 'VOA 特约记者',
        time: '2025-02-11',
        title: '南中国海博弈升级 中国与文莱联手拓油气为台海战争布局？',
        titleCn: '南中国海博弈升级：中国与文莱联手拓油气是否为台海战争布局？',
        content: '分析文莱与中国在南海油气开发合作背景下可能的地缘政治动机，探讨主权争议、安全风险与战略合作之间的复杂关系。',
      },
    ],
    usRelations: [
      {
        ai: '美国宣布对文莱出口征收 24% 报复性关税，文莱财政部正在评估影响并与美方沟通。',
        domain: ['关税', '贸易', '经济'],
        emotion: '中性',
        link: 'https://www.thestar.com.my/aseanplus/aseanplus‑news/2025/04/03/us‑imposes‑24‑per‑cent‑reciprocal‑tariff‑on‑brunei‑exports',
        region: ['文莱', '美国'],
        source: 'The Star / Borneo Bulletin',
        author: 'Unknown',
        time: '2025-04-03',
        title: 'US imposes 24 per cent reciprocal tariff on Brunei exports',
        titleCn: '美国对文莱出口征收24%报复性关税',
        content: 'The United States has announced a 24 per cent reciprocal tariff on exports from Brunei Darussalam as part of a sweeping tariff policy unveiled by President Donald Trump… US goods trade with Brunei was estimated at USD366 million in 2024…',
      },
      {
        ai: '文莱财政与经济部正式确认正在评估美国新关税对本国出口的冲击，并会持续与出口商及美国相关方对接。',
        domain: ['政策', '经济', '政府声明'],
        emotion: '中性',
        link: 'https://www.mofe.gov.bn/Lists/News/NewDispForm.aspx?ID=799',
        region: ['文莱', '美国'],
        source: 'Brunei MOFE',
        author: 'Ministry of Finance and Economy',
        time: '2025-04-03',
        title: 'Brunei Darussalam Assessing The Potential Impacts of the United States\' Tariff Regime on Its Exports to the United States',
        titleCn: '文莱评估美国关税制度对其出口的潜在影响',
        content: 'Following the recent announcement made by the United States … Brunei Darussalam Assessing The Potential Impacts … Brunei’s total trade with the US stood at BND 537.1 million in 2024 … exports… imports … major exports include Chemicals, Mineral Fuels…',
      },
      {
        ai: '文莱与美国继续深化防务合作，美国国防部长奥斯汀与文莱国防部长在五角大楼会晤，签署多项合作协议。',
        domain: ['军事', '安全', '外交'],
        emotion: '中性',
        link: 'https://bn.usembassy.gov/austin‑bruneis‑defense‑minister‑commit‑to‑continuing‑cooperation‑between‑nations/',
        region: ['文莱', '美国'],
        source: 'U.S. Embassy in Brunei',
        author: 'U.S. Mission Brunei',
        time: '2024-08-13',
        title: 'Austin, Brunei’s Defense Minister Commit to Continuing Cooperation Between Nations',
        titleCn: '奥斯汀与文莱国防部长承诺继续加强合作',
        content: 'Secretary of Defense Lloyd J. Austin III today hosted Brunei’s Minister of Defense II Pehin Halbi for talks at the Pentagon… discussed security cooperation, maritime domain awareness, information sharing… new Acquisition Cross‑Servicing Agreement in April and a 505 Agreement in December… strategic partnership…',
      },
      {
        ai: '文莱主办 CARAT 2024 海上联合演习，美军与文莱皇家武装部队进行海域意识、反水面战、水下爆破等联合训练。',
        domain: ['军事演习', '区域安全', '合作'],
        emotion: '中性',
        link: 'https://www.usasean.org/article/us‑brunei‑security‑cooperation‑continues‑deepen',
        region: ['文莱', '美国', '东南亚'],
        source: 'US‑ASEAN Business Council',
        author: 'Logan Carnicelli / Mega Valentina Maneesha Khalae',
        time: '2024-12-20',
        title: 'US‑Brunei Security Cooperation Continues to Deepen',
        titleCn: '美文安全合作持续深化',
        content: 'In November 2024, Brunei hosted the joint Cooperation Afloat Readiness and Training (CARAT)… to advance regional security, cooperation, and interoperability… served as a strictly bilateral series… aims to advance regional security…',
      },
      {
        ai: '美国“解放日”关税政策升级，对文莱等多个国家发布更高关税预警，文莱面临25%关税威胁。',
        domain: ['关税政策', '国际贸易', '政治'],
        emotion: '中性',
        link: 'https://www.businessinsider.com/trump‑trade‑deals‑status‑2025‑7',
        region: ['美国', '文莱'],
        source: 'Business Insider',
        author: 'Not specified',
        time: '2025-07‑09',
        title: 'Here’s where talks for all Trump\'s trade deals stand, amid a flurry of tariff letters and a new deadline',
        titleCn: '特朗普贸易协议最新进展与关税信函风波',
        content: 'As of July 9, 2025, President Donald Trump has intensified his tariff‑driven trade policy … Following a self‑imposed 90‑day pause, Trump issued letters to leaders of 22 countries … exporters in Brunei … face increased tariffs … Brunei and the Philippines saw increases.',
      },
    ],
    thinkTanks: [
      {
        title: '文莱战略与政策研究中心',
        domain: ['国家经济增长与发展', '能源与环境', '交通与电信', '金融与投资', '劳工与教育', '社会发展', '健康政策', '战略规划及未来学'],
        focus: '为文莱政府提供国家经济社会发展的政策建议',
      },
      {
        title: '文莱国家能源研究所',
        domain: ['能源研究'],
        focus: '支持东盟与东亚经济研究所的能源研究活动',
      },
      {
        title: '文莱大学亚洲研究所',
        domain: ['亚洲历史', '文化', '社会问题'],
        focus: '东亚、东盟、南亚及婆罗洲区域研究',
      },
      {
        title: '文莱大学电子政务创新中心',
        domain: ['治理', '立法', '政策与标准', '电子政务', '在线安全', '业务流程再造'],
        focus: '推动文莱电子政务实施与创新',
      },
      {
        title: '文莱技术学院',
        domain: ['工程', '商业', '计算机', '信息系统', '可持续工程'],
        focus: '满足国家人力资源需求，推动产学研合作',
      },
    ],
  },
  柬埔寨: {
    name: '柬埔寨',
    englishName: 'Cambodia',
    flag: '/placeholder.svg?height=80&width=120&text=🇸🇬',
    capital: '金边（Phnom Penh）',
    population: '1,545.8万',
    area: '18.1万平方公里',
    gdp: '30.9亿美元',
    currency: '瑞尔（Riel）',
    economy: '农业（稻米、橡胶）和纺织业为主，旅游业快速发展，中国为最大投资国',
    government: {
      type: '君主立宪制（多党民主）',
      president: '国王诺罗敦·西哈莫尼',
      primeMinister: '首相洪森',
      structure: ['国家元首', '立法委员会（立法机构）', '内阁（行政机构）', '最高法院（司法机构）'],
      legislative: {
        name: '国会',
        description: '由国民议会和参议院组成',
      },
      executive: {
        name: '王国政府',
        description: '由首相领导的行政机构',
      },
      judicial: {
        name: '最高法院',
        description: '柬埔寨王国最高司法机关',
      },
      electoral: {
        system: '君主立宪制下的多党选举制',
        description: '君主立宪制下的多党选举制',
      },
      party: {
        system: '多党制',
        description: '柬埔寨人民党主导',
      },
      local: {
        system: '四级地方政府',
        description: '省、市、县、乡四级地方政府',
      },
    },
    companies: [
      { title: 'Electricité du Cambodge', type: '国有企业', sector: '能源／电力' },
      { title: 'Phnom Penh Water Supply Authority', type: '国有企业', sector: '公用事业／供水' },
      { title: 'Sihanoukville Autonomous Port', type: '国有企业', sector: '港口／海运' },
      { title: 'Telecom Cambodia', type: '国有企业', sector: '电信／通信' },
      { title: 'Cambodia Shipping Agency', type: '国有企业', sector: '航运服务' },
      { title: 'Cambodia Postal Services', type: '国有企业', sector: '邮政服务' },
      { title: 'Rural Development Bank', type: '国有企业', sector: '金融／农村发展贷款' },
      { title: 'Green Trade Company', type: '国有企业', sector: '粮食储备与交易' },
      { title: 'Printing House (government)', type: '国有企业', sector: '印刷出版' },
      { title: 'Siem Reap Water Supply Authority', type: '国有企业', sector: '公用事业／供水' },
      { title: 'Construction and Public Work Lab', type: '国有企业', sector: '建设技术／公共工程' },
      { title: 'Phnom Penh Autonomous Port', type: '国有企业', sector: '港口／海运' },
      { title: 'Kampcheary Insurance (Camden‑still)', type: '国有企业', sector: '保险业' },
      { title: 'Cambodia Life Insurance', type: '国有企业', sector: '保险业' },
      { title: 'Cambodia Securities Exchange', type: '国有企业', sector: '证券交易所' },
      { title: 'Bank for Investment and Development of Cambodia', type: '国有企业', sector: '银行／金融' },
      { title: 'Printing and Publishing House of Cambodia', type: '国有企业', sector: '传媒／印刷' },
      { title: 'National Television of Cambodia', type: '政府机构', sector: '媒体／广播电视' },
      { title: 'National Radio of Cambodia', type: '政府机构', sector: '媒体／广播' },
      { title: 'Cambodia Airports (政府控股)', type: '国有企业', sector: '航空基础设施' },
    ], // 政企清单
    news: [],
    reports: [],
    chinaRelations: [],
    usRelations: [],
    thinkTanks: [
      {
        title: '柬埔寨发展资源研究所',
        domain: ['农业', '经济', '教育', '环境', '治理', '健康'],
        focus: '通过独立研究和能力建设促进柬埔寨包容性与可持续发展[^13^]',
      },
      {
        title: '柬埔寨和平与合作研究所',
        domain: ['和平', '民主', '公民社会', '安全', '外交政策', '冲突解决', '经济', '国家发展'],
        focus: '促进政府、国内外组织、学者及私营部门间对话，开展社会经济发展、安全、战略研究与国际关系等领域的调查和研究[^15^][^19^]',
      },
      {
        title: '柬埔寨王家学院国际关系研究所',
        domain: ['国际关系', '立法', '公共政策'],
        focus: '研究影响柬埔寨社会的各类问题，通过改进政府立法和行为提升民众生活品质[^20^]',
      },
      {
        title: '南亚商业研究院',
        domain: ['商业政策', '法规', '投资环境', '特许经营许可'],
        focus: '深度研究柬埔寨商业生态、市场动向及政策法规，为企业识别与化解政策性障碍与法律挑战[^11^]',
      },
    ],
  },
  老挝: {
    name: '老挝',
    englishName: 'Lao PDR',
    flag: '/placeholder.svg?height=80&width=120&text=🇸🇬',
    capital: '万象（Vientiane', // 首都
    population: '647.2万',
    area: '23.68万平方公里',
    gdp: '14.0亿美元',
    currency: '基普（Kip）',
    economy: '农业为主，中老铁路推动基建和旅游业，国有经济占主导但信贷支持私营部门',
    government: {
      type: '社会主义共和国（一党制）',
      president: '主席通伦·西苏里（2023年数据）',
      primeMinister: '总理宋赛·西潘敦',
      structure: ['国家元首', '立法委员会（立法机构）', '内阁（行政机构）', '最高法院（司法机构）'],
      legislative: {
        name: '国会',
        description: '老挝人民民主共和国最高国家权力机关',
      },
      executive: {
        name: '政府',
        description: '由总理领导的国家行政机关',
      },
      judicial: {
        name: '最高人民法院',
        description: '老挝最高司法审判机关',
      },
      electoral: {
        system: '一党制下的人民民主选举',
        description: '一党制下的人民民主选举',
      },
      party: {
        system: '一党制',
        description: '老挝人民革命党领导',
      },
      local: {
        system: '四级行政体系',
        description: '省、县、区、村四级行政体系',
      },
    },
    companies: [
      {
        title: '老挝石油化工股份有限公司',
        type: '中外合资企业',
        sector: '石油炼化、精细化工',
      },
      {
        title: '老挝国家电力公司（EDL）',
        type: '国有企业',
        sector: '电力',
      },
      {
        title: '老挝航空',
        type: '国有企业',
        sector: '航空运输',
      },
      {
        title: '老挝国有燃油企业',
        type: '国有企业（改革中）',
        sector: '燃油供应',
      },
    ],
    news: [
      {
        title: '中老签署数字资产管理合作意向书',
        titleCn: '越南和新加坡加强对资本和数字资产市场的管理和监管',
        source: '越通社',
        time: '2025-03-12',
        content: '越南国家证券委员会与新加坡金融管理局签署数字资产管理法律框架意向书，老挝财政部长阮文胜出席仪式。',
        domain: ['经济合作'],
        region: ['越南', '新加坡'],
        emotion: '中性',
        link: '',
      },
      {
        title: '老挝航空接收中国商飞C909客机',
        titleCn: '老挝航空引入中国支线客机拓展中老航线',
        source: '老挝国家广播电台',
        time: '2025-03-31',
        content: '首架C909客机交付，涂装老挝国花占芭花，计划执飞万象-昆明/广州航线。',
        domain: ['航空合作'],
        region: ['老挝', '中国'],
        emotion: '积极',
        link: '',
      },
    ],
    reports: [
      {
        title: '老挝矿业投资趋势分析（2025年）',
        tags: '能源矿产部',
        source: '老挝能源矿产部',
        time: '2025年2月',
        link: '',
      },
      {
        title: '中老铁路对区域经济的影响评估',
        tags: '计划投资部',
        source: '老挝计划投资部',
        time: '2024年',
        link: '',
      },
    ],
    chinaRelations: [],
    usRelations: [],
    thinkTanks: [
      {
        title: '越南战略与国际研究院',
        domain: ['外交政策', '国有企业改革'],
        focus: '老挝能源政策与中老合作',
      },
      {
        title: '老挝能源矿产部2030战略',
        domain: ['矿业发展'],
        focus: '2040年愿景中矿业投资占比提升与生态保护',
      },
    ],
  },
  印度尼西亚: {
    name: '印度尼西亚',
    englishName: 'Indonesia',
    flag: '/placeholder.svg?height=80&width=120&text=🇸🇬',
    capital: '雅加达（Jakarta）', // 首都
    population: '2.54亿',
    area: '191.9万平方公里',
    gdp: '14.0亿美元',
    currency: '印尼盾（Rupiah）',
    economy: '东南亚最大经济体，资源出口（煤炭、棕榈油）、制造业和数字经济为支柱，农村金融体系完善',
    government: {
      type: '总统制共和国',
      president: '总统佐科·维多多',
      primeMinister: '由总统兼任',
      structure: ['国家元首', '立法委员会（立法机构）', '内阁（行政机构）', '最高法院（司法机构）'],
      legislative: {
        name: '人民协商会议',
        description: '包括人民代表会议和地方代表理事会',
      },
      executive: {
        name: '内阁',
        description: '由总统领导的政府执行机构',
      },
      judicial: {
        name: '最高法院',
        description: '印尼共和国最高司法机构',
      },
      electoral: {
        system: '总统制共和国',
        description: '直接选举总统',
      },
      party: {
        system: '多党制',
        description: '政党联盟执政',
      },
      local: {
        system: '多级自治',
        description: '省、县/市、区、村多级自治',
      },
    },
    companies: [
      {
        title: '印度尼西亚国家石油公司（Pertamina）',
        type: '国有企业',
        sector: '能源',
      },
      {
        title: '印尼国有电力公司（PLN）',
        type: '国有企业',
        sector: '能源',
      },
      {
        title: '印尼国家银行（Bank Negara Indonesia, BNI）',
        type: '国有企业',
        sector: '金融',
      },
      {
        title: '印尼邮政公司（Pos Indonesia）',
        type: '国有企业',
        sector: '通信／物流',
      },
      {
        title: '印尼基础设施建设公司（Wijaya Karya）',
        type: '国有企业',
        sector: '基础设施／建设',
      },
    ],
    news: [
      {
        ai: '2025年5月20日，印尼总统强调加强与中国的经贸合作，推动区域稳定发展。',
        domain: [
          '印度尼西亚',
          '中国',
          '经贸',
          '合作',
        ],
        emotion: '积极',
        link: '',
        region: [
          '印度尼西亚',
          '中国',
        ],
        source: '雅加达邮报',
        author: '',
        time: '2025-5-20',
        title: 'Indonesian president calls for stronger economic ties with China',
        titleCn: '印尼总统呼吁加强与中国经济合作',
        content: '印尼总统表示愿与中国深化合作，促进双边贸易和区域和平。',
      },
      {
        ai: '2025年6月10日，印尼民众对中国在南海的活动表达担忧，呼吁维护海洋权益。',
        domain: [
          '印度尼西亚',
          '中国',
          '南海',
          '海洋权益',
        ],
        emotion: '担忧',
        link: '',
        region: [
          '印度尼西亚',
          '中国',
        ],
        source: '雅加达新闻',
        author: '',
        time: '2025-6-10',
        title: 'Indonesians express concern over China\'s South China Sea activities',
        titleCn: '印尼民众担忧中国南海活动',
        content: '部分印尼社会团体呼吁政府加强海洋主权保护。',
      },
      {
        ai: '2025年7月5日，印尼加强网络安全措施以防范外国网络攻击。',
        domain: [
          '印度尼西亚',
          '网络安全',
          '防御',
          '外国攻击',
        ],
        emotion: '中性',
        link: '',
        region: [
          '印度尼西亚',
        ],
        source: '印尼安全时报',
        author: '',
        time: '2025-7-5',
        title: 'Indonesia strengthens cybersecurity defenses against foreign threats',
        titleCn: '印尼加强网络安全防御',
        content: '政府宣布投入更多资源应对潜在网络安全威胁。',
      },
      {
        ai: '2025年4月15日，印尼社会媒体对美国印太政策表达不同声音，有支持也有质疑。',
        domain: [
          '印度尼西亚',
          '美国',
          '印太政策',
          '舆论',
        ],
        emotion: '混合',
        link: '',
        region: [
          '印度尼西亚',
          '美国',
        ],
        source: '社交媒体汇编',
        author: '',
        time: '2025-4-15',
        title: 'Mixed reactions to U.S. Indo-Pacific policy in Indonesia',
        titleCn: '印尼对美国印太政策反应复杂',
        content: '部分网民支持加强与美国合作，另有部分质疑美国意图。',
      },
      {
        ai: '2025年3月30日，印尼媒体报道中印尼海上边界谈判取得进展。',
        domain: [
          '印度尼西亚',
          '中国',
          '边界谈判',
          '海洋',
        ],
        emotion: '积极',
        link: '',
        region: [
          '印度尼西亚',
          '中国',
        ],
        source: '印尼环球时报',
        author: '',
        time: '2025-3-30',
        title: 'Progress in Indonesia-China maritime boundary negotiations',
        titleCn: '印尼中海上边界谈判取得进展',
        content: '双方同意加强沟通与合作，缓解海上争议。',
      },
    ],
    reports: [
      {
        title: '印尼对南海局势的战略评估',
        tags: '印尼战略研究中心',
        source: 'Indonesian Strategic Studies',
        time: '2025年3月',
        link: '',
      },
      {
        title: '印度尼西亚应对区域安全挑战的外交政策转型',
        tags: '区域安全分析机构',
        source: 'ASEAN Security Journal',
        time: '2025年1月',
        link: '',
      },
      {
        title: '印尼数字主权与网络安全风险研究',
        tags: '印尼网络安全研究院',
        source: 'Indonesian Cybersecurity Institute',
        time: '2025年4月',
        link: '',
      },
      {
        title: '印尼与中国贸易关系的未来走向',
        tags: '经济研究中心',
        source: 'Economic Outlook Indonesia',
        time: '2024年12月',
        link: '',
      },
      {
        title: '印尼在印太战略中的地缘政治地位',
        tags: '地缘政治研究所',
        source: 'GeoPolitical Review',
        time: '2025年2月',
        link: '',
      },
    ],
    chinaRelations: [
      {
        ai: '2025年6月1日，中国驻印尼大使馆发布安全提醒，提醒中国公民注意当地治安形势。',
        domain: [
          '中国',
          '印度尼西亚',
          '安全',
          '领事提醒',
        ],
        emotion: '警示',
        link: '',
        region: [
          '中国',
          '印度尼西亚',
        ],
        source: '中国驻印尼大使馆',
        author: '',
        time: '2025-6-1',
        title: 'China embassy issues safety alert for nationals in Indonesia',
        titleCn: '中国驻印尼大使馆发布安全提醒',
        content: '因近期局部治安问题，中国使馆提醒在印中国公民提高警惕。',
      },
      {
        ai: '2025年5月10日，印尼警方逮捕数名涉嫌针对中国企业的盗窃嫌疑人。',
        domain: [
          '印度尼西亚',
          '中国',
          '安全',
          '犯罪',
        ],
        emotion: '积极',
        link: '',
        region: [
          '印度尼西亚',
        ],
        source: '雅加达警方通报',
        author: '',
        time: '2025-5-10',
        title: 'Indonesian police arrest suspects targeting Chinese firms',
        titleCn: '印尼警方逮捕针对中国企业的嫌疑人',
        content: '警方加强保护外资企业安全，打击针对中国企业的犯罪活动。',
      },
      {
        ai: '2025年4月20日，印尼社交媒体流传针对中国投资项目的质疑声音。',
        domain: [
          '印度尼西亚',
          '中国',
          '投资',
          '舆论',
        ],
        emotion: '争议',
        link: '',
        region: [
          '印度尼西亚',
        ],
        source: '社交媒体汇总',
        author: '',
        time: '2025-4-20',
        title: 'Public debates on Chinese investment projects in Indonesia',
        titleCn: '印尼公众质疑中国投资项目',
        content: '部分民众担忧投资带来的环境与经济影响，引发讨论。',
      },
      {
        ai: '2025年3月15日，印尼政府回应关于中国技术转让和本地化生产的质疑。',
        domain: [
          '印度尼西亚',
          '中国',
          '技术转让',
          '政府回应',
        ],
        emotion: '中性',
        link: '',
        region: [
          '印度尼西亚',
          '中国',
        ],
        source: '印尼政府公告',
        author: '',
        time: '2025-3-15',
        title: 'Indonesia government addresses concerns on Chinese tech transfer',
        titleCn: '印尼政府回应中国技术转让质疑',
        content: '政府强调双方合作符合国家利益，促进本地产业发展。',
      },
      {
        ai: '2025年2月28日，印尼媒体报道中国专家参与印尼基础设施建设项目。',
        domain: [
          '印度尼西亚',
          '中国',
          '基础设施',
          '合作',
        ],
        emotion: '积极',
        link: '',
        region: [
          '印度尼西亚',
          '中国',
        ],
        source: '印尼建设日报',
        author: '',
        time: '2025-2-28',
        title: 'Chinese experts participate in Indonesia infrastructure projects',
        titleCn: '中国专家参与印尼基础设施建设',
        content: '多项合作项目推动印尼基础设施现代化进程。',
      },
    ],
    usRelations: [
      {
        ai: '2025年6月12日，美国国务卿访印尼，强调加强印太安全合作。',
        domain: [
          '美国',
          '印度尼西亚',
          '安全合作',
          '印太',
        ],
        emotion: '积极',
        link: '',
        region: [
          '美国',
          '印度尼西亚',
        ],
        source: '美国国务院新闻稿',
        author: '',
        time: '2025-6-12',
        title: 'US Secretary of State visits Indonesia to strengthen Indo-Pacific cooperation',
        titleCn: '美国国务卿访印尼加强印太合作',
        content: '双方讨论加强区域安全和经济合作事宜。',
      },
      {
        ai: '2025年5月22日，印尼与美国签署新贸易合作协议，促进双边经贸发展。',
        domain: [
          '印度尼西亚',
          '美国',
          '贸易',
          '合作',
        ],
        emotion: '积极',
        link: '',
        region: [
          '印度尼西亚',
          '美国',
        ],
        source: '印尼商务部',
        author: '',
        time: '2025-5-22',
        title: 'Indonesia and US sign new trade cooperation agreement',
        titleCn: '印尼与美国签署贸易合作协议',
        content: '协议旨在扩大双边贸易和投资规模。',
      },
      {
        ai: '2025年4月5日，印尼社会舆论对美国军售计划存在分歧。',
        domain: [
          '印度尼西亚',
          '美国',
          '军售',
          '舆论',
        ],
        emotion: '争议',
        link: '',
        region: [
          '印度尼西亚',
          '美国',
        ],
        source: '雅加达时报',
        author: '',
        time: '2025-4-5',
        title: 'Public debate on US arms sales to Indonesia',
        titleCn: '印尼舆论对美军售意见分歧',
        content: '部分社会团体支持军售，另有团体持保留意见。',
      },
      {
        ai: '2025年3月18日，美国智库发布印尼战略评估报告，分析印尼在区域安全中的角色。',
        domain: [
          '美国',
          '印度尼西亚',
          '智库',
          '安全',
        ],
        emotion: '中性',
        link: '',
        region: [
          '美国',
          '印度尼西亚',
        ],
        source: '美国国际战略研究中心',
        author: '',
        time: '2025-3-18',
        title: 'US think tank releases strategic assessment on Indonesia',
        titleCn: '美国智库发布印尼战略评估报告',
        content: '报告分析印尼在印太地区安全格局中的影响力。',
      },
      {
        ai: '2025年2月25日，印尼议会讨论美国在印太地区的影响力及合作前景。',
        domain: [
          '印度尼西亚',
          '美国',
          '议会',
          '印太',
        ],
        emotion: '中性',
        link: '',
        region: [
          '印度尼西亚',
          '美国',
        ],
        source: '印尼议会新闻',
        author: '',
        time: '2025-2-25',
        title: 'Indonesia parliament debates US influence in Indo-Pacific',
        titleCn: '印尼议会讨论美在印太影响力',
        content: '议员们就合作和主权保护展开讨论。',
      },
    ],
    thinkTanks: [],
  },
  马来西亚: {
    name: '马来西亚',
    englishName: 'Malaysia',
    flag: '/placeholder.svg?height=80&width=120&text=🇸🇬',
    capital: '吉隆坡（Kuala Lumpur）', // 首都
    population: '3,007.3万',
    area: '32.98万平方公里',
    gdp: '14.0亿美元',
    currency: '林吉特（Ringgit）',
    economy: '多元化经济（电子、石油、棕榈油），金融业发达（伊斯兰保险占比较高），与中国贸易紧密',
    government: {
      type: '联邦议会君主立宪制（选举君主制）',
      president: '阿卜杜拉·苏丹艾哈迈德·沙阿',
      primeMinister: '阿卜杜拉·苏丹艾哈迈德·沙',
      structure: ['国家元首', '立法委员会（立法机构）', '内阁（行政机构）', '最高法院（司法机构）'],
      legislative: {
        name: '国会',
        description: '由上议院和下议院组成的联邦立法机构',
      },
      executive: {
        name: '内阁',
        description: '由首相领导的联邦政府',
      },
      judicial: {
        name: '联邦法院',
        description: '马来西亚最高司法权威',
      },
      electoral: {
        system: '联邦制君主立宪',
        description: '议会选举制',
      },
      party: {
        system: '多党制',
        description: '国民阵线长期执政',
      },
      local: {
        system: '多级联邦制',
        description: '州、县、市多级联邦制',
      },
    },
    companies: [
      {
        title: '马来西亚国家石油公司（Petronas）',
        type: '国有企业',
        sector: '能源',
      },
      {
        title: '宝腾（Proton）',
        type: '合资企业（中马合资）',
        sector: '汽车制造',
      },
      {
        title: '马来西亚国家电力公司（TNB）',
        type: '国有企业',
        sector: '电力',
      },
      {
        title: 'CelcomDigi',
        type: '私营企业',
        sector: '通信',
      },
    ],
    news: [
      {
        title: '马来西亚启动国家级AI基础设施计划',
        titleCn: '马来西亚推出人工智能国家战略',
        source: '腾讯新闻',
        time: '2025-07-09',
        content: '马来西亚公布国家级人工智能基础设施计划，覆盖医疗、教育等领域，目标成为东盟AI中心。',
        domain: ['数字经济', '人工智能'],
        region: ['马来西亚'],
        emotion: '积极',
        link: '',
      },
      {
        title: '中马共建东海岸铁路项目取得新进展',
        titleCn: '中马东海岸铁路项目完成50%工程量',
        source: '新华社',
        time: '2025-06-15',
        content: '中马共建东海岸铁路项目完成50%工程量，预计2027年全线通车。',
        domain: ['基础设施建设'],
        region: ['马来西亚', '中国'],
        emotion: '积极',
        link: '',
      },
    ],
    reports: [
      {
        title: '马来西亚数字经济2025年发展趋势报告',
        tags: '数字经济',
        source: '马来西亚数字部',
        time: '2025年7月',
        link: '',
      },
      {
        title: '中马经贸合作2024年度评估',
        tags: '经贸合作',
        source: '马来西亚投资发展局',
        time: '2025年2月',
        link: '',
      },
    ],
    chinaRelations: [],
    usRelations: [],
    thinkTanks: [
      {
        title: '马来西亚战略与国际问题研究所',
        domain: ['外交政策', '区域经济'],
        focus: '东盟一体化与马来西亚角色',
      },
      {
        title: '马来西亚数字经济研究中心',
        domain: ['数字经济', '人工智能'],
        focus: '马来西亚数字化转型路径',
      },
    ],
  },
  缅甸: {
    name: '缅甸',
    englishName: 'Myanmar',
    flag: '/placeholder.svg?height=80&width=120&text=🇸🇬',
    capital: '内比都（Nay Pyi Taw））', // 首都
    population: '5,574.6万',
    area: '67.66万平方公里',
    gdp: '14.0亿美元',
    currency: '缅元（Kyat）',
    economy: '农业和资源出口为主，金融市场不发达，受政治动荡影响外资减少 ',
    government: {
      type: '共和制（军政府主导）',
      president: '总统敏昂莱（军方领导人）',
      primeMinister: '由总统兼任',
      structure: ['国家元首', '立法委员会（立法机构）', '内阁（行政机构）', '最高法院（司法机构）'],
      legislative: {
        name: '联邦议会',
        description: '由人民院和民族院组成',
      },
      executive: {
        name: '国务资政府',
        description: '国家行政管理机构',
      },
      judicial: {
        name: '最高法院',
        description: '缅甸联邦共和国最高司法机关',
      },
      electoral: {
        system: '总统制共和国',
        description: '多党选举制',
      },
      party: {
        system: '多党制',
        description: '军方影响较大',
      },
      local: {
        system: '多级行政',
        description: '邦、省、县、镇多级行政',
      },
    },
    companies: [
      {
        title: '缅甸石油天然气公司（MOGE）',
        type: '国有企业',
        sector: '能源',
      },
      {
        title: '缅甸经济控股有限公司（MEHL）',
        type: '军方关联企业',
        sector: '综合产业',
      },
      {
        title: '缅甸国家电力公司',
        type: '国有企业',
        sector: '电力',
      },
      {
        title: '缅甸电信（MPT）',
        type: '合资企业',
        sector: '通信',
      },
    ],
    news: [
      {
        title: '欧盟宣布对缅甸22名个人和4家实体实施制裁',
        titleCn: '欧盟第四轮制裁涉及军方核心企业',
        source: '海峡网',
        time: '2022-02-23',
        content: '缅甸石油天然气公司等被列入制裁名单，冻结在欧资产并限制出入境[4](@ref)',
        domain: ['国际制裁'],
        region: ['缅甸', '欧盟'],
        emotion: '负面',
      },
      {
        title: '缅甸7.9级地震造成重大伤亡',
        titleCn: '地震致1700人死亡，卫星识别483处受灾点',
        source: '央视新闻',
        time: '2025-03-31',
        content: '震中曼德勒周边建筑损毁率超80%，中缅联合开展救援[7](@ref)',
        domain: ['自然灾害'],
        region: ['缅甸'],
        emotion: '负面',
      },
      {
        title: '缅甸犯罪率全球最高',
        titleCn: '犯罪指数达8.15，涉电诈与人口贩卖',
        source: '缅甸中文君',
        time: '2024-07-20',
        content: '全球犯罪指数研究显示缅甸位列榜首，电信诈骗和器官贩卖猖獗[6](@ref)',
        domain: ['社会治安'],
        region: ['缅甸'],
        emotion: '负面',
      },
    ],
    reports: [
      {
        title: '缅甸2023-24财年经济白皮书',
        tags: '宏观经济',
        source: '缅甸计划与财政部',
        time: '2024年3月',
        link: '',
      },
      {
        title: '中缅经济走廊投资风险评估',
        tags: '基础设施',
        source: '缅甸投资委员会',
        time: '2025年1月',
        link: '',
      },
    ],
    chinaRelations: [],
    usRelations: [],
    thinkTanks: [
      {
        title: '缅甸投资法研究院',
        domain: ['外商投资'],
        focus: '《缅甸投资法》实施细则解读[1](@ref)',
      },
      {
        title: '缅甸能源战略中心',
        domain: ['油气开发'],
        focus: '天然气出口占外汇收入50%的可持续性[4](@ref)',
      },
    ],
  },
  菲律宾: {
    name: '菲律宾',
    englishName: 'Philippines',
    flag: '/placeholder.svg?height=80&width=120&text=🇸🇬',
    capital: '马尼拉（Manila）', // 首都
    population: '1.08亿',
    area: '30万平方公里',
    gdp: '14.0亿美元',
    currency: '比索（Peso）',
    economy: '服务业（外包产业）和制造业为主，家族财团控制私有银行，与中国存在海洋争端',
    government: {
      type: '总统制共和国',
      president: '总统费迪南德·马科斯',
      primeMinister: '由总统兼任',
      structure: ['国家元首', '立法委员会（立法机构）', '内阁（行政机构）', '最高法院（司法机构）'],
      legislative: {
        name: '国会',
        description: '由参议院和众议院组成的两院制',
      },
      executive: {
        name: '行政部门',
        description: '由总统领导的政府机构',
      },
      judicial: {
        name: '最高法院',
        description: '菲律宾共和国最高司法机构',
      },
      electoral: {
        system: '总统制共和国',
        description: '直接选举制',
      },
      party: {
        system: '多党制',
        description: '政党竞争激烈',
      },
      local: {
        system: '四级地方政府',
        description: '省、市、镇、村四级地方政府',
      },
    },
    companies: [
      {
        title: '菲律宾国家石油公司',
        type: '国有企业',
        sector: '能源',
      },
      {
        title: '国家电力公司',
        type: '国有企业',
        sector: '能源',
      },
      {
        title: '菲律宾土地银行',
        type: '国有企业',
        sector: '金融',
      },
      {
        title: '菲律宾邮政公司',
        type: '国有企业',
        sector: '通信／物流',
      },
      {
        title: '菲律宾国家建设公司',
        type: '国有企业',
        sector: '基础设施／建设',
      },
    ],
    news: [
      {
        ai: '2025年5月26日，菲律宾外长表示乐于与中国签署更多协议以维护南海和平。',
        domain: [
          '菲律宾',
          '中国',
          '外交',
          '南海',
        ],
        emotion: '中性',
        link: '',
        region: [
          '菲律宾',
          '中国',
        ],
        source: '路透社',
        author: '',
        time: '2025-5-26',
        title: 'Philippines open to more agreements with China to keep peace in South China Sea',
        titleCn: '菲律宾愿与中国签署更多协议以维护南海和平',
        content: '菲律宾外长称愿意接受任何能帮助维护南海和平的附加协议，尽管双方在争议岛礁问题上持续对峙。',
      },
      {
        ai: '2025年6月15日，美国CBS“60分钟”报道菲律宾与中国在南海发生严重海上对峙，美军可能介入。',
        domain: [
          '菲律宾',
          '中国',
          '美国',
          '冲突',
        ],
        emotion: '紧张',
        link: '',
        region: [
          '菲律宾',
          '中国',
          '美国',
        ],
        source: 'CBS News',
        author: 'Cecilia Vega',
        time: '2025-6-15',
        title: 'Conflict between China, Philippines could involve U.S. and lead to a clash of superpowers',
        titleCn: '菲中冲突可能牵扯美国并引发超级大国对抗',
        content: '报道称菲律宾海岸警卫队船只被中国船只撞击，造成紧张局势升温，美菲共同防御条约可能触发干预。',
      },
      {
        ai: '2025年7月14日，有研究者估算中国海军编制锚阻行动导致 Pag‑asa Reef 珊瑚礁损害约 1,110 万披索。',
        domain: [
          '菲律宾',
          '中国',
          '环境',
          '损害',
        ],
        emotion: '负面',
        link: '',
        region: [
          '菲律宾',
          '中国',
        ],
        source: '维基百科摘要',
        author: '',
        time: '2025-7-14',
        title: 'Coral damage at Pag‑asa Reef from Chinese vessel parachute anchor',
        titleCn: '中国船只降锚导致 Pag‑asa 礁珊瑚毁损约 ₱11.1 M',
        content: '研究者称中国海事民兵渔船在 Pag‑asa Reef 使用伞锚造成珊瑚下层生态严重破坏。',
      },
      {
        ai: '2025年2月28日，菲律宾指控四名中国人涉嫌间谍活动，向当地警方赠送现金与车辆收买支持。',
        domain: [
          '菲律宾',
          '中国',
          '安全',
          '间谍',
        ],
        emotion: '负面',
        link: '',
        region: [
          '菲律宾',
          '中国',
        ],
        source: '路透社',
        author: '',
        time: '2025-2-28',
        title: 'Alleged Chinese spies gave Philippine city and police cash and motorbikes',
        titleCn: '涉嫌中国间谍向菲律宾城市和警方赠送现金、摩托车',
        content: '四名中国籍被控在菲律宾使用无人机获取海军基地图像，并通过赠送礼品影响当地机构。',
      },
      {
        ai: '2025年7月18日，中国正式向菲律宾提出外交抗议，涵盖台湾与南海安全等问题。',
        domain: [
          '中国',
          '菲律宾',
          '外交',
          '台湾',
        ],
        emotion: '紧张',
        link: '',
        region: [
          '中国',
          '菲律宾',
        ],
        source: '路透社',
        author: '',
        time: '2025-7-18',
        title: 'China protests to Philippines over Taiwan, maritime and security issues',
        titleCn: '中国就台湾、海洋与安全问题向菲律宾提出抗议',
        content: '中国外长对菲律宾在台湾、南海及安全方面的动作表示强烈不满并提出严正交涉。',
      },
    ],
    reports: [
      {
        title: '警惕菲律宾扩大美军基地部署带来的战略风险',
        tags: 'Vanguard Think Tank',
        source: 'Vanguard Think Tank',
        time: '2025年4月',
        link: '',
      },
      {
        title: '菲中南海对峙与国家安全评估',
        tags: 'Foundation for the National Interest',
        source: 'Foundation for the National Interest',
        time: '2025年2月',
        link: '',
      },
      {
        title: '菲美战略同盟新时代下的外交政策转型',
        tags: 'Renato Cruz De Castro（学术论文）',
        source: 'SAGE Publications',
        time: '2024年',
        link: '',
      },
      {
        title: '菲律宾在美中竞争格局中的立场与舆情分析',
        tags: 'GIS Reports',
        source: 'GIS Reports',
        time: '2025年上半年',
        link: '',
      },
      {
        title: '菲律宾数字主权与网络安全威胁研判',
        tags: 'Risk and Threat Perception in the Indo‑Pacific',
        source: 'Fund. for the National Interest',
        time: '2025年',
        link: '',
      },
    ],
    chinaRelations: [
      {
        ai: '2025年7月18日，中国教育部对在菲中国学生发布安全提醒，涉及绑架与治安风险。',
        domain: [
          '中国',
          '菲律宾',
          '安全',
          '留学生',
        ],
        emotion: '负面',
        link: '',
        region: [
          '中国',
          '菲律宾',
        ],
        source: '美联社',
        author: '',
        time: '2025-7-18',
        title: 'China issues safety warning for its nationals studying in the Philippines',
        titleCn: '中国发布赴菲留学生安全提醒',
        content: '中国教育部因菲律宾发生涉及中国留学生的绑架事件发布安全警示，反映中菲关系紧张。',
      },
      {
        ai: '2025年2月28日，四名中国人涉嫌间谍活动并收买菲律宾警方，披露影响行动链条。',
        domain: [
          '中国',
          '菲律宾',
          '间谍',
          '安全',
        ],
        emotion: '负面',
        link: '',
        region: [
          '菲律宾',
        ],
        source: '路透社',
        author: '',
        time: '2025-2-28',
        title: 'Alleged Chinese spies gave Philippine city and police cash and motorbikes',
        titleCn: '中国间谍向菲律宾城市与警方赠礼涉嫌影响',
        content: '被指为中共统战网络成员的中国人在菲律宾从事情报搜集及社交影响活动。',
      },
      {
        ai: '2025年3月初，中国社交媒体在微博、RedNote 上主张巴拉望（Palawan）曾属中国，引发菲律宾舆论反弹。',
        domain: [
          '中国',
          '菲律宾',
          '舆论',
          '领土',
        ],
        emotion: '争议',
        link: '',
        region: [
          '菲律宾',
          '中国',
        ],
        source: '维基百科摘要',
        author: '',
        time: '2025-3-1',
        title: 'Chinese social media claims on Palawan',
        titleCn: '中国社交媒体称巴拉望属中国引发菲方反驳',
        content: '菲律宾国家海事机构称此类内容为认知战，国内舆论普遍反对。',
      },
      {
        ai: '2025年4月24日，菲律宾国家安全委员会称中国干预 2025 年选举，包括通过 troll‑farm 扩散分裂性言论。',
        domain: [
          '中国',
          '菲律宾',
          '选举',
          '影响',
        ],
        emotion: '警惕',
        link: '',
        region: [
          '菲律宾',
        ],
        source: '维基百科摘要',
        author: '',
        time: '2025-4-24',
        title: 'China interfering with PH\'s May 2025 polls, says NSC\'s Malaya',
        titleCn: '中国涉嫌干预菲律宾 2025 年选举，菲国家安全委员会称',
        content: 'NSC 指中国大使馆与本地公司合作操控社交媒体放大分裂性政治言论。',
      },
      {
        ai: '2025年7月3周，菲律宾召见中国大使，就北京制裁批评中国南海政策的菲律宾前参议员表达不满。',
        domain: [
          '中国',
          '菲律宾',
          '外交',
          '制裁',
        ],
        emotion: '负面',
        link: '',
        region: [
          '菲律宾',
          '中国',
        ],
        source: '美联社',
        author: '',
        time: '2025-7-3',
        title: 'Philippines summons China\'s ambassador after Beijing sanctions critical former senator',
        titleCn: '菲律宾召见中国大使抗议中国制裁前参议员',
        content: '菲律宾强调制裁行动损害相互尊重原则，重申言论自由权利。',
      },
    ],
    usRelations: [
      {
        ai: '菲律宾经济部门对美国拟定于2025年8月1日实施的20%出口关税表达担忧，并计划继续谈判以争取降低关税水平。',
        domain: ['贸易', '经济政策'],
        emotion: '中性',
        link: 'https://www.reuters.com/world/asia-pacific/philippines-plans-negotiate-with-us-lower-tariffs-envoy-washington-says-2025-07-10/',
        region: ['菲律宾', '美国'],
        source: 'Reuters',
        author: 'Reuters',
        time: '2025-07-10',
        title: 'Philippines concerned over higher U.S. tariffs, will continue to negotiate, minister says',
        titleCn: '菲律宾对美国提高关税表示关切，将继续谈判，部长表示',
        content: '菲律宾经济事务部长 Frederick Go 对美国宣布对菲出口征收 20% 关税表达关切，强调计划与美国进行广泛谈判以争取更优惠的双边经济协议，可能包括自由贸易协定。同时指出美国仍为重要出口市场，贸易总额占比近 16%。',
      },
      {
        ai: '特朗普与菲律宾总统举行会晤，达成新的贸易协议，菲律宾同意承担19%进口关税，同时扩大军事合作。',
        domain: ['外交', '贸易', '军事合作'],
        emotion: '积极',
        link: 'https://apnews.com/article/3138d1d7f8368c022ab9242bebb4a2e2',
        region: ['菲律宾', '美国'],
        source: 'AP News',
        author: 'AP',
        time: '2025-07-22',
        title: 'Trump says US will impose 19% tariff on imports from Philippines in deal struck with leader Marcos',
        titleCn: '特朗普称美菲达成交易，对菲律宾进口征收19%关税',
        content: '特朗普与菲律宾总统 Marcos Jr. 在白宫会晤后宣布新贸易和军事协议，菲律宾将接受 19% 的关税（较此前威胁的 20% 降低），同时加强美菲军事合作与投资。',
      },
      {
        ai: '菲律宾外长马纳洛和美国国务卿卢比奥承诺加强合作，应对中国在南海的“破坏性行动”，并深化经济合作。',
        domain: ['外交', '安全', '经济'],
        emotion: '中性',
        link: 'https://www.philstar.com/headlines/2025/06/10/2449527/philippines-us-cooperate-beijings-destabilizing-actions-south-china-sea',
        region: ['菲律宾', '美国', '南海'],
        source: 'Philstar.com',
        author: 'Cristina Chi',
        time: '2025-06-10',
        title: 'Philippines, US to cooperate on Beijing\'s \'destabilizing actions\' in South China Sea',
        titleCn: '菲律宾与美国合作应对中国在南海的“破坏性行为”',
        content: '在华盛顿举行的会谈中，美菲双方承诺加强合作，应对中国在南海的“破坏性行动”并扩大经济合作，以实现互惠互利。双方还讨论加强三边与区域伙伴（尤其日本）的合作。',
      },
      {
        ai: '菲律宾强调无论美国总统选举结果如何，美菲安全关系将保持强劲，国防部确认对南海事务的合作将持续。',
        domain: ['外交', '国防', '政治稳定'],
        emotion: '中性',
        link: 'https://www.reuters.com/world/asia-pacific/philippines-confident-us-security-policy-continuity-regardless-election-2025-03-04/',
        region: ['菲律宾', '美国'],
        source: 'Reuters',
        author: 'Karen Lema, David Lague',
        time: '2025-03-04',
        title: 'Philippines Confident on US Alliance Despite Trump Disruptions, Ambassador Says',
        titleCn: '菲律宾大使表示尽管特朗普带来冲击，美菲联盟仍将坚固',
        content: '菲律宾驻美大使 Romualdez 表示，尽管特朗普上任带来政策动荡，美菲现行防务安排将继续维系，包括联合军演与在南海的安全合作。国务卿卢比奥也重申对合作的热情。',
      },
      {
        ai: '美菲年度“Balikatan”联合军演于2025年4月举办，约9,000美军参与，对南海及台湾地区安全威慑意味显著。',
        domain: ['军事演习', '区域安全'],
        emotion: '中性',
        link: 'https://www.abs-cbn.com/news/nation/2025/4/21/-shared-adversity-new-weapons-as-philippine-us-forces-launch-balikatan-1135',
        region: ['菲律宾', '美国', '南海', '台湾附近海域'],
        source: 'ABS-CBN / AFP',
        author: 'Agence France‑Presse',
        time: '2025-04-21',
        title: '‘Shared adversity’, new weapons as Philippine, US forces launch \'Balikatan\'',
        titleCn: '菲律宾与美军启动“Balikatan”联合演习，展现新武器与共御挑战',
        content: '2025年4月，菲律宾与美国启动年度“Balikatan”联合军演，共动员约9,000名美军与5,000名菲军，使用包括 NMESIS“杀舰导弹”在内的新型武器，训练内容涵盖海上防御、特种作战与空海巡逻。',
      },
    ],
    thinkTanks: [
      {
        title: 'Foundation for the National Interest',
        domain: [
          '外交政策',
          '安全',
          '南海',
        ],
        focus: '菲律宾国家安全与南海对峙',
      },
      {
        title: 'Vanguard Think Tank',
        domain: [
          '战略政策',
          '美菲同盟',
        ],
        focus: '菲律宾–美国安全合作与政策研判',
      },
      {
        title: 'GIS Reports',
        domain: [
          '外交政策',
          '经济与贸易',
        ],
        focus: '美中竞争下菲律宾外交与经济策略',
      },
      {
        title: 'ISEAS‑Yusof Ishak Institute',
        domain: [
          '舆情调查',
          '东盟战略态度',
        ],
        focus: '区域国家信任度及对美中偏好调查',
      },
      {
        title: 'Risk and Threat Perception in the Indo‑Pacific',
        domain: [
          '网络安全',
          '科技依赖',
        ],
        focus: '东海与南海地区菲律宾面临的数字安全威胁',
      },
    ],
  },
  // 可以继续添加其他国家数据...
}

// 计算属性
const currentCountry = computed(() => {
  console.log('selectedCountry.value', countryData[currentNav.value])

  return countryData[currentNav.value]
})

// 政府架构相关函数
function getInstitutionDescription(institution: string): string {
  const descriptions: Record<string, string> = {
    '国会（立法机关）': '国家最高立法机构，负责制定法律和监督政府',
    '国家主席（国家元首）': '国家象征和最高代表，行使国家元首职权',
    '政府（行政机关）': '国家行政权力机构，负责执行法律和管理国家事务',
    '最高人民法院（司法机关）': '国家最高司法机构，负责审理重大案件',
    '国王（国家元首）': '君主制国家的世袭元首，象征国家统一',
    '总理（政府首脑）': '政府首脑，负责政府日常运作和政策制定',
    '最高法院（司法机关）': '最高司法权威，维护法律权威和司法公正',
    '总统（国家元首）': '共和制国家元首，通常由选举产生',
    '立法委员会（立法机构）': '立法权力机构，参与法律制定和政策审议',
    '内阁（行政机构）': '政府核心执行机构，协助首脑管理国家',
    '最高法院（司法机构）': '司法体系顶层，确保法律正确执行',
  }
  return descriptions[institution] || '重要政府机构，参与国家治理'
}

// 政企清单相关函数
function getCompanyTypeColor(type: string): string {
  const colorMap: Record<string, string> = {
    国有企业: 'blue',
    私营企业: 'green',
    跨国企业: 'orange',
    合资企业: 'purple',
    外资企业: 'red',
    民营企业: 'cyan',
  }
  return colorMap[type] || 'default'
}

function getCompanyIcon(type: string): string {
  const iconMap: Record<string, string> = {
    国有企业: '🏛️',
    私营企业: '🏭',
    跨国企业: '🌐',
    合资企业: '🤝',
    外资企业: '🌍',
    民营企业: '💼',
  }
  return iconMap[type] || '🏢'
}

// 智库研究相关函数
function getDomainColor(domain: string): string {
  const colorMap: Record<string, string> = {
    本土智库: 'blue',
    国际智库: 'green',
    学术机构: 'orange',
    政府智库: 'purple',
    民间智库: 'cyan',
    企业智库: 'red',
  }
  return colorMap[domain] || 'default'
}
</script>

<style lang="less">
.ch2-tabs-card {
  .ant-tabs-nav-list {
    width: 100%;
  }

  .ant-tabs-tab {
    flex: 1;
    display: flex;
    justify-content: center;
  }
}
</style>
