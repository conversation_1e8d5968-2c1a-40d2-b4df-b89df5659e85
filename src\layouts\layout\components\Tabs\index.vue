<template>
  <div>
    <a-tabs
      ref="tabsDropRef" v-model:active-key="tabStore.activeKey" type="editable-card" hide-add
      style="margin-right: 10px; border-bottom: 2px black" @change="changeUpdate" @edit="onEdit"
    >
      <a-tab-pane v-for="(tab, index) in tabsMenuList" :key="tab.path" :closable="tab.close" data-test="test">
        <template #tab>
          <Icon v-if="tab.icon" v-show="tab.icon && appStore.themeConfig.tabsIcon" :icon="tab.icon || ''" />

          <a-dropdown :trigger="['contextmenu']">
            <span><c-ellipsis :value="tab.title" :max-length="20" /></span>
            <template #overlay>
              <a-menu @click="onMenuClose($event, index)">
                <a-menu-item v-if="route.fullPath === tab.path" key="redo">
                  <SyncOutlined />
                  重新加载
                </a-menu-item>
                <template v-if="tabsMenuList?.length > 1">
                  <a-menu-item key="close-that">
                    <CloseCircleOutlined />
                    关闭当前标签
                  </a-menu-item>
                  <a-menu-item key="close-right">
                    <VerticalLeftOutlined />
                    关闭右侧
                  </a-menu-item>
                  <a-menu-item v-if="tabsMenuList?.length > 2" key="close-left">
                    <VerticalRightOutlined />
                    关闭左侧
                  </a-menu-item>
                  <a-menu-item key="close-other">
                    <CloseOutlined />
                    关闭其它
                  </a-menu-item>
                </template>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </a-tab-pane>

      <template #rightExtra>
        <MoreButton />
      </template>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import type { TabsMenuProps } from '@/types/interfaces'
import Icon from '@/components/ICON'
import { useAppStore, useTabsStore } from '@/stores'
import { useKeepAliveStore } from '@/stores/modules/keepAlive'
import {
  CloseCircleOutlined,
  CloseOutlined,
  SyncOutlined,
  VerticalLeftOutlined,
  VerticalRightOutlined,
} from '@ant-design/icons-vue'
import { sortBy, uniqWith } from 'lodash-es'
import Sortable from 'sortablejs'
import { computed, inject, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import MoreButton from './components/MoreButton.vue'

const tabStore = useTabsStore()

const appStore = useAppStore()

const tabsMenuList = computed(() => tabStore.tabsMenuList.filter(p => p.name))

const route = useRoute()

const router = useRouter()

router.beforeEach((route) => {
  if (route.meta.hiddenBreadcrumb)
    return
  if (!tabStore.tabsMenuList.some(e => route.fullPath === e.path)) {
    const tabsParams = {
      icon: route.meta?.icon,
      title: [route.meta?.title, (route.params as any)?.ch2Title as string].filter(Boolean).join('-'),
      path: route.fullPath,
      name: route.meta?.title,
      close: !route.meta?.isAffix,
      cached: false,
    } as any
    tabStore.addTabs(tabsParams, tabsParams.path)
  }
  else {
    tabStore.activeKey = route.fullPath
  }
})

router.afterEach((route) => {
  const currentTab = tabStore.tabsMenuList.find(e => route.fullPath === e.path)
  if (currentTab)
    currentTab.cached = true

  useKeepAliveStore().setKeepAliveName(
    tabStore.tabsMenuList.flatMap((e) => {
      if (!e.cached)
        return []
      return {
        name: e.name as string,
        path: e.path || '',
      }
    }),
  )
})

function changeUpdate(e: string) {
  tabStore.activeKey = e
  router.push(e)
}

// Remove Tab
function tabRemove(fullPath: string) {
  const index = Number.parseInt(fullPath, 10)
  let currentTag: TabsMenuProps = {
    path: '',
    close: false,
  }
  if (index > 0)
    currentTag = tabStore.tabsMenuList[index]!
  else
    [currentTag] = tabStore.tabsMenuList.filter((item: { path: string }) => item.path === fullPath)

  tabStore.removeTabs(currentTag.path, router, currentTag.path === route.fullPath)
}

function onEdit(e: MouseEvent | KeyboardEvent | any, action: 'add' | 'remove') {
  if (action === 'remove')
    tabRemove(e.toString())
}

// 初始化需要固定的标签
function initTabs() {
  useKeepAliveStore().setKeepAliveName([])
  if (tabStore.tabsMenuList.length) {
    tabStore.tabsMenuList.forEach((v) => {
      if (route.fullPath === v.path) {
        v.cached = true
        useKeepAliveStore().setKeepAliveName([{
          name: v.name as string,
          path: v.path || '',
        }])
      }
      else {
        v.cached = false
      }
    })
    return
  }
  const routeTab = sortBy(
    uniqWith(router.getRoutes(), (a, b) => a.path === b.path).flatMap<any>((item) => {
      if ((item?.meta?.isAffix && !item.meta.hidden && !item.meta.isFull) || item.path === route.path) {
        return {
          icon: item.meta.icon,
          title: item.meta.title,
          path: item.path,
          name: item.name,
          close: !item.meta.isAffix,
          cached: false,
        }
      }
      return []
    }),
    o => o.close,
  )

  tabStore.setTabs(routeTab)
  tabStore.activeKey = route.path
}

const refresh = inject('refresh') as any

function onMenuClose({ key }: { key: string, item: any, domEvent: any }, index: number) {
  switch (key) {
    case 'close-other':
      closeOtherTag(index)
      break
    case 'close-right':
      closeRightTag(index)
      break
    case 'close-that':
      closeCurrentTag(index)
      break
    case 'close-left':
      closeLeftTag(index)
      break
    case 'redo':
      refresh()
      break
    default:
  }
}

function closeCurrentTag(index: number) {
  tabStore.removeTabs(tabStore.tabsMenuList[index]!.path, router)
  if (tabStore.tabsMenuList[index])
    router.push(tabStore.tabsMenuList[index].path)
  else router.push(tabStore.tabsMenuList[index - 1]!.path)
}

function closeOtherTag(index: number) {
  tabStore.closeMultipleTab(tabStore.tabsMenuList[index]!.path)
}

function closeRightTag(index: number) {
  const { path } = tabStore.tabsMenuList[index]!
  tabStore.tabsMenuList = tabStore.tabsMenuList.filter(
    (item, idx) => (idx <= index && idx > 0) || item.close === false,
  )
  router.push(path)
}

function closeLeftTag(index: number) {
  const { path } = tabStore.tabsMenuList[index]!
  tabStore.tabsMenuList = tabStore.tabsMenuList.filter((item, idx) => idx >= index || item.close === false)
  router.push(path)
}

// #region 拖拽
const tabsDropRef = ref()

// #endregion

onMounted(() => {
  initTabs()
  const el = tabsDropRef.value.$el.querySelector('.ant-tabs-nav-list') as HTMLElement
  // eslint-disable-next-line no-new
  new Sortable(el, {
    animation: 150,
    fallbackTolerance: 3,
    filter: (_evt: any, target: any) => target.classList.contains('ant-tabs-tab-with-remove') === false,
    onEnd: ({ newIndex, oldIndex }: any) => {
      const draggedTab = tabStore.tabsMenuList.splice(oldIndex, 1)[0]
      tabStore.tabsMenuList.splice(newIndex, 0, draggedTab as any)
    },
  })
})
</script>

<style scoped lang="less">
.ant-tabs {
  .ant-tabs-nav-wrap {
    position: absolute;
    width: calc(100% - 110px);
  }

  .ant-tabs-extra-content {
    margin-right: 10px;
  }
}

.ant-tabs-extra-content {
  margin-right: 10px;
}

:deep(.ant-tabs > .ant-tabs-nav) {
  margin: 0;
}
</style>
