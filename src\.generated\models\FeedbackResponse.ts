import { UserFeedback } from "./UserFeedback";
import { User } from "./User";
/**反馈处理记录*/
export class FeedbackResponse {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  /**关联的反馈*/
  userFeedbackId: GUID = "00000000-0000-0000-0000-000000000000";
  /**用户反馈模型*/
  userFeedback?: UserFeedback | null | undefined = null;
  /**处理人*/
  responseUserId: GUID = "00000000-0000-0000-0000-000000000000";
  /**用户*/
  responseUser?: User | null | undefined = null;
  /**回复内容*/
  response?: string | null | undefined = null;
  /**回复时间*/
  responseTime: Dayjs = dayjs();
  /**查看时间（自动更新）*/
  readTime?: Dayjs | null | undefined = null;
}
