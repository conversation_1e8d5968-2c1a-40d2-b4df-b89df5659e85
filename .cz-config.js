/*
 * @Description: cz-customizable
 * @Author: sharebravery
 * @Date: 2022-08-18 08:44:07
 */
module.exports = {
  types: [
    { value: 'feat', name: '✨ Features | 新特性' },
    { value: 'fix', name: '🐛 Bug Fixes | Bug 修复' },
    { value: 'init', name: '🎉 Init | 初始化' },
    { value: 'docs', name: '✏️ Documentation | 文档' },
    { value: 'style', name: '💄 Styles | 风格' },
    { value: 'refactor', name: '♻️ Code Refactoring | 代码重构' },
    { value: 'perf', name: '⚡ Performance Improvements | 性能优化' },
    { value: 'test', name: '✅ Tests | 测试' },
    { value: 'revert', name: '⏪ Revert | 回滚' },
    { value: 'build', name: '📦‍ Build System | 打包构建' },
    { value: 'chore', name: '🚀 Chore | 构建/工程依赖/工具' },
    { value: 'ci', name: '👷 Continuous Integration | CI 配置' },
  ],
  scopes: [], // { name: "具体业务" }, { name: "组件" }, { name: "系统管理" }
  messages: {
    type: '请选择提交类型:',
    scope: '请输入修改范围(可选):',
    // used if allowCustomScopes is true
    customScope: 'Denote the SCOPE of this change:',
    subject: '请简要描述提交(必填):\n',
    body: '请输入详细描述，使用"|"换行(可选)：\n',
    footer: '请输入要关闭的issue，例如：#31, #34(可选):\n',
    confirmCommit: '确认以上信息提交?(y/n)',
  },
  allowCustomScopes: true,
  allowBreakingChanges: ['特性', '修复'],
  // 跳过问题
  skipQuestion: ['scope', 'footer'],
  // limit subject length
  subjectLimit: 100,
}
