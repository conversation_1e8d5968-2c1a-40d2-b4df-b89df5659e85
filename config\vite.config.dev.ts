/*
 * @Description: ^_^
 * @Author: sharebravery
 * @Date: 2022-12-08 17:37:45
 */
import { mergeConfig } from 'vite'
import VueRouter from 'unplugin-vue-router/vite'
import vueDevTools from 'vite-plugin-vue-devtools'
import baseConfig from './vite.config.base'
import proxy from './utils/proxy'
import getEnv from './utils/getEnv'
import useFileRouter from './utils/useFileLayoutRouter'
import preventFullReloadPlugin from './utils/preventFullReloadPlugin'
import globalDirectivesTypes from './utils/global-directives-types'

const mode = 'dev'
const env = getEnv(mode)
const proxyTarget = env.VITE_APP_PROXY_TARGET

export default mergeConfig(
  {
    mode,
    server: {
      open: true,
      fs: {
        strict: true,
      },
      proxy: proxyTarget ? proxy(env) : undefined,
    },

    plugins: [
      preventFullReloadPlugin(),
      globalDirectivesTypes({ dir: 'src/directives', out: 'src' }),
      // https://uvr.esm.is/guide/file-based-routing.html
      VueRouter({

        /* options */
        dts: 'src/typed-router.d.ts',

        routesFolder: {
          src: 'src/views',
        },
        exclude: ['**/components/*.vue', 'components/**/*.vue', '**/*/components/**/*.vue'],
        watch: false,
      }),

      useFileRouter(),
      vueDevTools({
        // launchEditor: 'cursor'
      }),
    ],
    optimizeDeps: {
      exclude: ['ch2-components']
    }
  },
  baseConfig,
)
