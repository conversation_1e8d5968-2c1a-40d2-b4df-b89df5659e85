<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2025-04-16 11:31:22
 * @LastEditors: 景 彡
-->
<template>
  <div class="">
    <!-- Search Bar -->
    <div class="mx-auto mt-8 px-4 container">
      <a-input-search v-model:value="keyword" class="c2-search-input" size="large" placeholder="搜索智库文章、新闻、政策等内容..." enter-button :loading="searchLoding" @search="onSearch" />
    </div>

    <!-- Stats Cards -->
    <div class="mx-auto mt-8 px-4 container">
      <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
        <!-- Data Total Card -->
        <div class="border border-gray-100 rounded-xl bg-white p-6 shadow-md" @click="goto('/dept-client/overseas')">
          <div class="flex justify-between">
            <div class="font-medium">数据总量</div>
            <div class="rounded-md bg-blue-400 p-1">
              <div class="h-5 w-5 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-full w-full"><circle cx="11" cy="11" r="8" /><path d="m21 21-4.3-4.3" /></svg>
              </div>
            </div>
          </div>
          <div class="mt-4">
            <div class="text-3xl font-semibold">5500w+</div>
            <div class="mt-2 text-sm text-gray-400">近7天增长178w+ 篇</div>
          </div>
        </div>

        <!-- Involved Information Card -->
        <div class="border border-gray-100 rounded-xl bg-white p-6 shadow-md" @click="goto('/dept-client/news-related-me')">
          <div class="flex justify-between">
            <div class="font-medium">涉我信息</div>
            <div class="rounded-md bg-green-400 p-1">
              <div class="h-5 w-5 flex items-center justify-center text-white">i</div>
            </div>
          </div>
          <div class="mt-4">
            <div class="text-3xl font-semibold">16481</div>
            <div class="mt-2 text-sm text-gray-400">近7天增长 {{ countObj.global.swIncrement }} 篇</div>
          </div>
        </div>

        <!-- Today's Updates Card -->
        <div class="border border-gray-100 rounded-xl bg-white p-6 shadow-md" @click="goto('/dept-client/overseas')">
          <div class="flex justify-between">
            <div class="font-medium">今日更新</div>
            <div class="rounded-md bg-orange-400 p-1">
              <div class="h-5 w-5 flex items-center justify-center text-white">⏱</div>
            </div>
          </div>
          <div class="mt-4">
            <div class="text-3xl font-semibold">2088</div>
            <div class="mt-2 text-sm text-gray-400">较昨日 {{ countObj.global.growthRate }}%</div>
          </div>
        </div>

        <!-- Forecast Information Card -->
        <div class="border border-gray-100 rounded-xl bg-white p-6 shadow-md" @click="goto('dept-client/risk')">
          <div class="flex justify-between">
            <div class="font-medium">预警信息</div>
            <div class="rounded-md bg-red-400 p-1">
              <div class="h-5 w-5 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-full w-full"><path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9" /><path d="M10.3 21a1.94 1.94 0 0 0 3.4 0" /></svg>
              </div>
            </div>
          </div>
          <div class="mt-4">
            <div class="text-3xl font-semibold">{{ countObj.warning.total }}</div>
            <div class="mt-2 text-sm text-gray-400">较昨日 <span :class="countObj.warning.yesterdayDiff < 0 ? 'c-#ff0000' : ''">{{ countObj.warning.yesterdayDiff }}</span></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Feature Cards -->
    <div class="mx-auto mt-8 px-4 container">
      <div class="grid grid-cols-1 gap-4 md:grid-cols-5">
        <!-- Overseas Think Tank -->
        <div class="rounded-xl from-blue-500 to-blue-600 bg-gradient-to-r p-6 text-white shadow-lg" @click="goto('/dept-client/overseas')">
          <div class="flex justify-between">
            <div>
              <h3 class="text-xl font-medium">海外智库</h3>
              <p class="mt-1 text-sm opacity-80"> 518696 智库文章采集与分析</p>
            </div>
            <div class="opacity-50">
              <div class="h-16 w-16">
                <div class="relative h-full w-full">
                  <div class="absolute bottom-0 left-0 right-0 w-16 border-t-2 border-white" />
                  <div class="absolute bottom-0 left-1/2 h-10 transform border-l-2 border-white -translate-x-1/2" />
                  <div class="absolute bottom-10 left-1/2 h-8 w-8 transform border-2 border-white rounded-full -translate-x-1/2" />
                </div>
              </div>
            </div>
          </div>
          <div class="mt-16 text-sm">今日更新: {{ countObj.global.hwzk?.value2 }} 篇</div>
        </div>

        <!-- East Alliance Media -->
        <div class="rounded-xl from-red-400 to-red-500 bg-gradient-to-r p-6 text-white shadow-lg" @click="goto('/dept-client/news')">
          <div class="flex justify-between">
            <div>
              <h3 class="text-xl font-medium">东盟新闻媒体</h3>
              <p class="mt-1 text-sm opacity-80">125527 媒体新闻采集与分析</p>
            </div>
            <div class="opacity-50">
              <div class="h-16 w-16">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-full w-full"><circle cx="12" cy="12" r="10" /><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20" /><path d="M2 12h20" /></svg>
              </div>
            </div>
          </div>
          <div class="mt-16 text-sm">今日更新: {{ countObj.global.dmxw?.value2 }} 篇</div>
        </div>

        <!-- East Alliance AI Development -->
        <div class="rounded-xl from-green-400 to-green-500 bg-gradient-to-r p-6 text-white shadow-lg" @click="goto('/dept-client/ai')">
          <div class="flex justify-between">
            <div>
              <h3 class="text-xl font-medium">{{ countObj.global.ai?.table }}</h3>
              <p class="mt-1 text-sm opacity-80"> {{ countObj.global.ai?.value1 }}  东盟人工智能政策与动态</p>
            </div>
            <div class="opacity-50">
              <div class="h-16 w-16">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-full w-full"><circle cx="12" cy="12" r="10" /><path d="M12 2a14.5 14.5 0 0 0 0 20a14.5 14.5 0 0 0 0-20" /><path d="M2 12h20" /></svg>
              </div>
            </div>
          </div>
          <div class="mt-16 text-sm">今日更新: {{ countObj.global.ai?.value2 }} 篇</div>
        </div>

        <!-- Risk Forecast Research -->
        <div class="rounded-xl from-orange-400 to-orange-500 bg-gradient-to-r p-6 text-white shadow-lg" @click="goto('/dept-client/risk')">
          <div class="flex justify-between">
            <div>
              <h3 class="text-xl font-medium">风险预警研判</h3>
              <p class="mt-1 text-sm opacity-80">东盟新闻与社会舆情分析</p>
            </div>
            <div class="opacity-50">
              <div class="h-16 w-16">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-full w-full"><circle cx="12" cy="12" r="10" /><path d="M12 2a14.5 14.5 0 0 0 0 20a14.5 14.5 0 0 0 0-20" /><path d="M2 12h20" /></svg>
              </div>
            </div>
          </div>
          <div class="mt-16 text-sm">今日更新: {{ countObj.warning.today }} 篇</div>
        </div>

        <!-- Risk Forecast Research -->
        <div class="rounded-xl from-#c084fc to-#c084fc bg-gradient-to-r p-6 text-white shadow-lg" @click="goto('/dept-client/google-event-news')">
          <div class="flex justify-between">
            <div>
              <h3 class="text-xl font-medium">谷歌数据库</h3>
              <p class="mt-1 text-sm opacity-80">5200万+</p>
            </div>
            <div class="opacity-50">
              <div class="h-16 w-16">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-full w-full"><circle cx="12" cy="12" r="10" /><path d="M12 2a14.5 14.5 0 0 0 0 20a14.5 14.5 0 0 0 0-20" /><path d="M2 12h20" /></svg>
              </div>
            </div>
          </div>
          <div class="mt-16 text-sm">近期新增 178w+ </div>
        </div>
      </div>
    </div>

    <!-- Recent Key Information -->
    <div class="mx-auto mt-8 px-4 container">
      <div class="flex items-center justify-between">
        <h2 class="border-l-4 border-blue-500 pl-2 text-lg font-medium">近期关键信息</h2>
        <a class="cursor-pointer text-sm text-blue-500" @click="goto('/dept-client/key-information')">
          查看全部 &gt;
        </a>
      </div>

      <div v-if="countObj.hotData.length > 0" class="grid grid-cols-1 mt-4 gap-4 md:grid-cols-4">
        <div v-for="item in countObj.hotData" :key="item.id" class="relative border border-gray-100 rounded-2xl bg-white p-4 shadow-md">
          <div v-show="item.isChina" class="absolute right-0 top-0 rounded-bl-2xl rounded-tr-2xl bg-red-100 px-2 py-1 text-xs text-red-500">涉我</div>

          <div class="text-xs text-gray-500">{{ item.sourceCountry }} <span class="ml-2">{{ item.source }}</span> </div>
          <h3 class="mt-2 text-sm font-medium">{{ item.titleCn }}</h3>
          <p class="line-clamp-3 mt-2 text-xs text-gray-500">
            {{ item.contentCn }}
          </p>
          <div class="mt-3 flex items-center justify-between">
            <div class="text-xs text-gray-400">{{ dateTime(item.time, 'YYYY-MM-DD') }}</div>
            <div class="rounded-2xl bg-red-500 px-2 py-1 text-xs text-white">热门</div>
          </div>
        </div>
      </div>
      <a-empty v-else />
    </div>

    <!-- Bottom Section -->
    <div class="mx-auto mb-8 mt-8 px-4 container">
      <div class="grid grid-cols-1 gap-8 md:grid-cols-4">
        <!-- Hot Topics -->
        <div>
          <h2 class="mb-4 border-l-4 border-blue-500 pl-2 text-lg font-medium">热点话题</h2>
          <div class="h-64 border border-gray-100 rounded-xl bg-white p-6 shadow-md">
            <div ref="wordCloudChartRef" class="h-full w-full" />
          </div>
        </div>

        <!-- Active Think Tanks -->
        <div>
          <h2 class="mb-4 border-l-4 border-blue-500 pl-2 text-lg font-medium">活跃智库</h2>
          <div class="h-64 border border-gray-100 rounded-xl bg-white p-6 shadow-md">
            <div class="space-y-4">
              <div class="flex items-center">
                <div class="w-6 text-center">1.</div>
                <div class="flex-1">
                  <div class="text-sm font-medium">布鲁金斯学会</div>
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>美国 | 北美地区 | 28篇</span>
                    <span class="text-blue-500">国际事务</span>
                  </div>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-6 text-center">2.</div>
                <div class="flex-1">
                  <div class="text-sm font-medium">兰德公司</div>
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>美国 | 北美地区 | 18篇</span>
                    <div class="text-blue-500">国防安全</div>
                  </div>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-6 text-center">3.</div>
                <div class="flex-1">
                  <div class="text-sm font-medium">国际战略研究所</div>
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>英国 | 欧洲地区 | 17篇</span>
                    <div class="text-blue-500">国际关系</div>
                  </div>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-6 text-center">4.</div>
                <div class="flex-1">
                  <div class="text-sm font-medium">德国国际经济研究所</div>
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>德国 | 欧洲地区 | 12篇</span>
                    <div class="text-blue-500">全球经济</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- News Distribution Analysis -->
        <div class="md:col-span-2">
          <h2 class="mb-4 border-l-4 border-blue-500 pl-2 text-lg font-medium">东盟涉我新闻分布与情感分析</h2>
          <div class="h-64 border border-gray-100 rounded-xl bg-white p-6 shadow-md">
            <div class="h-full flex items-center">
              <div class="h-full w-1/2">
                <div ref="pieChartRef" class="h-full w-full" />
              </div>
              <div class="h-full w-1/2 flex flex-col justify-around py4">
                <div v-for="(item, index) in countObj.global.qgfx" :key="index" class="flex items-center space-y-2">
                  <div class="w-12 text-xs">{{ item.table }}</div>
                  <div class="relative ml-1 flex-1">
                    <div class="h-6 bg-gray-200">
                      <div v-if="item.table === '负面'" class="absolute h-6 flex items-center bg-red-500" style="width: 7%">
                        <span class="absolute left-1 text-xs text-white">{{ item.value1 }}</span>
                      </div>
                      <div v-else-if="item.table === '中性'" class="absolute h-6 flex items-center bg-yellow-500" style="width: 7%">
                        <span class="absolute left-1 text-xs text-white">{{ item.value1 }}</span>
                      </div>
                      <div v-else class="absolute h-6 flex items-center bg-green-500" style="width: 7%">
                        <span class="absolute left-1 text-xs text-white">{{ item.value1 }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Chart, DataManageModelHotPageModel } from '@/api/models'
import type { RouteNamedMap } from 'vue-router/auto-routes'
import * as api from '@/api'

import { GlobalStatistics, RiskWarningStatsDto } from '@/api/models'
import { message } from 'ant-design-vue'
import { PieChart } from 'echarts/charts'
import { LegendComponent, TooltipComponent } from 'echarts/components'
import * as echarts from 'echarts/core'
import { LabelLayout } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { useRouter } from 'vue-router'
import 'echarts-wordcloud' // 引入词云图

definePage({
  meta: { hiddenBreadcrumb: true },
})

echarts.use([TooltipComponent, LegendComponent, PieChart, CanvasRenderer, LabelLayout])

const pieChartRef = useTemplateRef('pieChartRef')
const wordCloudChartRef = useTemplateRef('wordCloudChartRef') // 新增 ref

const countObj = ref<{ global: GlobalStatistics, hotData: DataManageModelHotPageModel[], hotTopic: Chart[], warning: RiskWarningStatsDto }>({
  global: new GlobalStatistics(),
  hotData: [],
  hotTopic: [],
  warning: new RiskWarningStatsDto(),
})

// 词云图数据
const wordCloudData = ref<{ name: string, value: number }[]>([])

function initPieChart() {
  const color = ['#3B82F6', '#8B5CF6', '#F97316', '#6B7280']

  if (pieChartRef.value) {
    const chart = echarts.init(pieChartRef.value)

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        type: 'scroll',
        orient: 'horizontal',
        bottom: 'bottom',
        width: '80%',
        data: countObj.value.global.swfb?.map(item => item.table),
        formatter(name: string | any[]) {
          return name.length > 6 ? `${name.slice(0, 6)}...` : name
        },
        tooltip: {
          show: true,
          formatter(param: { name: any }) {
            return param.name
          },
        },
        // 设置为两行显示
        height: 50,
        padding: [0, 0, 0, 0],
        itemGap: 10,
        itemWidth: 10,
        itemHeight: 10,
      },
      series: [
        {
          name: '新闻分布',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '14',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: countObj.value.global.swfb?.map((item, idx) => ({ name: item.table, value: item.value1, itemStyle: { color: color[idx] } })),

        },
      ],
    }

    chart.setOption(option)
    // 自动轮播显示tooltip
    let currentIndex = -1
    const dataLen = option.series[0].data.length
    setInterval(() => {
      // 隐藏前一个tooltip
      chart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: currentIndex,
      })
      currentIndex = (currentIndex + 1) % dataLen
      // 显示当前tooltip
      chart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: currentIndex,
      })
      // 显示tooltip
      chart.dispatchAction({
        type: 'showTip',
        seriesIndex: 0,
        dataIndex: currentIndex,
      })
    }, 3000)
    // 响应式调整
    window.addEventListener('resize', () => {
      chart.resize()
    })
  }
}

// 初始化词云图
function initWordCloudChart() {
  if (wordCloudChartRef.value) {
    const chart = echarts.init(wordCloudChartRef.value)
    const option = {
      tooltip: {
        show: true,
      },
      series: [{
        type: 'wordCloud',
        // shape: 'circle', // 形状可以是 'circle', 'cardioid', 'diamond', 'triangle-forward', 'triangle', 'pentagon', 'star'
        left: 'center',
        top: 'center',
        width: '100%',
        height: '100%',
        right: null,
        bottom: null,
        sizeRange: [12, 25], // 字体大小范围
        rotationRange: [-45, 45], // 旋转角度范围
        rotationStep: 45,
        gridSize: 8, // 词间距
        drawOutOfBound: false, // 是否允许绘制超出边界的文字
        textStyle: {
          fontFamily: 'sans-serif',
          fontWeight: 'bold',
          // 随机颜色
          color() {
            // Random color
            return `rgb(${[
              Math.round(Math.random() * 160),
              Math.round(Math.random() * 160),
              Math.round(Math.random() * 160),
            ].join(',')})`
          },
        },
        emphasis: {
          focus: 'self',
          textStyle: {
            textShadowBlur: 10,
            textShadowColor: '#333',
          },
        },
        data: wordCloudData.value,
      }],
    }
    chart.setOption(option)

    window.addEventListener('resize', () => {
      chart.resize()
    })
  }
}

async function getCount() {
  try {
    const res = await api.Statistices.Statistics_GetAsync({})
    const res1 = await api.Statistices.GetHotDataManageAsync({})
    const res2 = await api.Statistices.GetHotTopicAsync({ pageIndex: 10 })
    countObj.value.warning = await api.Statistices.RiskWarningStatistics_GetAsync()
    wordCloudData.value = res2.map(item => ({ name: item.table, value: item.value1 }))
    countObj.value.global = res
    countObj.value.hotData = res1
    countObj.value.hotTopic = res2
    initWordCloudChart()
  }
  catch (error: any) {
    message.error(error.message)
  }
}

const keyword = ref('')

const searchLoding = ref(false)

const router = useRouter()

function onSearch() {
  if (!keyword.value)
    return message.warning('请输入搜索关键词')
  router.push({ path: '/dept-client/search-list', query: { keyword: keyword.value } })
}

function goto(path: keyof RouteNamedMap) {
  router.push({ path })
}

onMounted(() => {
  getCount()
  initPieChart()
})
</script>

<style lang="less">
.c2-search-input {
  :deep(.ant-input) {
    @apply rounded-l-30px h-48px overflow-hidden;
  }
  :deep(.ant-btn.ant-btn-primary.ant-btn-lg.ant-input-search-button) {
    @apply rounded-r-30px h-48px;
  }
  .ant-btn {
    @apply bg-purple-400;
  }
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced card styling */
.shadow-md {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.shadow-md:hover {
  transform: translateY(-4px); /* 悬停时向上浮动 */
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.15),
    0 4px 6px -2px rgba(0, 0, 0, 0.1); /* 增强阴影效果 */
}

.shadow-lg {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.shadow-lg:hover {
  transform: translateY(-4px); /* 悬停时向上浮动 */
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.2),
    0 10px 10px -5px rgba(0, 0, 0, 0.1); /* 增强阴影效果 */
}

/* 添加特色卡片的渐变背景过渡效果 */
.from-blue-500.to-blue-600:hover,
.from-red-400.to-red-500:hover,
.from-green-400.to-green-500:hover,
.from-orange-400.to-orange-500:hover {
  filter: brightness(1.1); /* 提高亮度 */
  cursor: pointer;
}

/* 添加新闻卡片标题悬停效果 */
.rounded-2xl h3 {
  transition: color 0.3s ease;
}

.rounded-2xl:hover h3 {
  color: #3b82f6; /* 使用蓝色突出显示 */
}

/* 添加"查看全部"链接悬停效果 */
.text-blue-500 {
  transition: all 0.3s ease;
}

.text-blue-500:hover {
  color: #2563eb;
  text-decoration: underline;
}

.rounded-xl {
  border-radius: 0.75rem;
}
</style>
