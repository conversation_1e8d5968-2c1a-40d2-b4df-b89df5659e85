<!-- 东盟国情概况 -->
<template>
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-3 md:grid-cols-2 xl:grid-cols-4">
    <a-card
      v-for="(card, index) in cards"
      :key="index"
      :hoverable="true"
      :bordered="false"
      class="card-item group transform overflow-hidden bg-white shadow-md transition-all duration-300 hover:shadow-xl hover:-translate-y-2"
      :body-style="{ padding: 0 }"
      @click="toLink(card.url)"
    >
      <!-- 图片容器 -->
      <div class="relative overflow-hidden">
        <img
          :src="card.image"
          :alt="card.title"
          class="h-56 w-full object-cover transition-transform duration-300 group-hover:scale-110"
        >
        <!-- 悬浮遮罩 -->
        <div class="absolute inset-0 bg-black/0 transition-all duration-300 group-hover:bg-black/20" />
      </div>

      <!-- 标题区域 -->
      <div class="p-6">
        <h3 class="text-lg text-gray-900 font-semibold transition-colors duration-200 group-hover:text-blue-600">
          {{ card.title }}
        </h3>
        <div class="mt-2 h-1 w-0 from-blue-500 to-purple-500 bg-gradient-to-r transition-all duration-300 group-hover:w-full" />
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import image2 from './images/gk_印度尼西亚.png'
import image7 from './images/gk_文莱.png'
import image5 from './images/gk_新加坡.png'
import image10 from './images/gk_柬埔寨.png'
import image6 from './images/gk_泰王国.png'
import image8 from './images/gk_缅甸.png'
import image9 from './images/gk_老挝.png'
import image4 from './images/gk_菲律宾.png'
import image1 from './images/gk_越南.png'
import image3 from './images/gk_马来西亚.png'

const cards = ref([
  {
    title: '东盟国别大数据精准画像-越南',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view/complete?name=越南',
    image: image1,
  },
  {
    title: '东盟国别大数据精准画像-印度尼西亚',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view/complete?name=印度尼西亚',
    image: image2,
  },
  {
    title: '东盟国别大数据精准画像-马来西亚',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view/complete?name=马来西亚',
    image: image3,
  },
  {
    title: '东盟国别大数据精准画像-菲律宾',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view/complete?name=越南',
    image: image4,
  },
  {
    title: '东盟国别大数据精准画像-新加坡',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view/complete?name=新加坡',
    image: image5,
  },
  {
    title: '东盟国别大数据精准画像-泰王国',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view/complete?name=泰王国',
    image: image6,
  },
  {
    title: '东盟国别大数据精准画像-文莱',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view/complete?name=文莱',
    image: image7,
  },
  {
    title: '东盟国别大数据精准画像-缅甸',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view/complete?name=缅甸',
    image: image8,
  },
  {
    title: '东盟国别大数据精准画像-老挝',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view/complete?name=老挝',
    image: image9,
  },
  {
    title: '东盟国别大数据精准画像-柬埔寨',
    url: 'https://157.an.ch2lab.cn/#/asean-countries/data-view/complete?name=柬埔寨',
    image: image10,
  },
])

function toLink(url: string) {
  window.open(url, '_blank')
}
</script>
