/*
 * @Description:
 * @Author: 景 彡
 * @Date: 2023-06-02 10:42:29
 * @LastEditors: luckymiaow
 */

import { UserService } from '@/api/user'
import { message } from 'ant-design-vue'
import { nextTick, onMounted, reactive, ref } from 'vue'

import { useRouter } from 'vue-router'

export const WeChatCode = 'code'

interface FormState {
  username: string
  password: string
  id: string
  code: string
}

export function useLogin() {
  const loading = ref<boolean>(false)
  const remember = ref<boolean>(false)
  const title = import.meta.env.VITE_APP_TITLE

  const formState = reactive<FormState>({
    username: '',
    password: '',
    id: '',
    code: '',
  })

  const base64 = ref()

  const getCaptcha = () => { }

  const rememberPassword = () => {
    if (remember.value) {
      const rememberData = {
        username: btoa(formState.username),
        password: btoa(formState.password),
        date: new Date(),
      }
      localStorage.setItem('rememberData', JSON.stringify(rememberData))
    }
    else { localStorage.removeItem('rememberData') }
  }

  const login = async () => {
    loginApi(() => UserService.login(formState), loading)
    rememberPassword()
  }

  const onFinish = async () => {
    await login()
  }

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo)
  }

  const getRememberData = () => {
    const localItem = localStorage.getItem('rememberData')
    if (localItem) {
      const localObj = JSON.parse(localItem)
      const date = new Date(localObj.date)
      date.setDate(date.getDate() + 7)
      if (date < new Date()) {
        localStorage.removeItem('rememberData')
        remember.value = false
      }
      else {
        formState.username = atob(localObj.username)
        formState.password = atob(localObj.password)
        remember.value = true
      }
    }
  }

  onMounted(() => {
    getCaptcha()
    getRememberData()
  })

  return {
    title,
    formState,
    remember,
    base64,
    onFinish,
    onFinishFailed,
    getCaptcha,
    loading,
  }
}

export function useWeChatLogin() {
  const iframeURL = ref<string>('')
  const router = useRouter()
  const locationUrl = `${location.origin}/${router.resolve({ path: '' }).href}`
  const redirect_uri = encodeURIComponent(`${import.meta.env.VITE_WEIXIN}/r/${locationUrl}`)
  const url = `https://open.weixin.qq.com/connect/qrconnect?appid=wx550d535a691b82fb&redirect_uri=${redirect_uri}&response_type=${WeChatCode}&scope=snsapi_login&state=3d6be0a4035d839573b04816624a415e#wechat_redirect`
  const updated = async () => {
    iframeURL.value = ''
    await nextTick()
    iframeURL.value = url
  }
  return {
    iframeURL,
    updated,
  }
}

export async function weChatLogin() {
  let searchParams = location.search
  if (location.hash)
    searchParams = location.hash.substring(location.hash.indexOf('?'))

  const params = new URLSearchParams(searchParams)
  const code = params.get(WeChatCode)

  if (code) {
    history.pushState({}, '', location.href.replace(`${WeChatCode}=${code}`, ''))
    await loginApi(() => UserService.mpLogin({ code }))
    message.success('微信登录成功')
    return true
  }
  else {
    return false
  }
}
