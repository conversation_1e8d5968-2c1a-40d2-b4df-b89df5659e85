import directives from '@/directives/index'
import pinia from '@/stores'
import request from '@/utils/remote-request'
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { setupRouter } from './router/setupRouter'
import 'core-js/es/promise/all-settled'
import 'virtual:uno.css'
import '@/assets/default.less'
import './utils/eventBus'
import 'core-js/features/global-this'
import 'core-js/features/object'

const app = createApp(App)
app.use(request)
app.use(pinia)
setupRouter(app)
app.use(directives)

router.isReady().then(() => app.mount('#app'))
