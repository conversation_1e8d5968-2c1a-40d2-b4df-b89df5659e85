import type { Component, VNode } from 'vue'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import { useAppStore, useKeepAliveStore } from '@/stores'
import { getComponentName } from '@/utils/util'
import { computed, createVNode, nextTick, provide, ref } from 'vue'
import { useRoute } from 'vue-router'

export function useKeepAliveAndRefreshPage() {
  const keepAliveStore = useKeepAliveStore()

  const showPage = ref(true)
  const route = useRoute()
  const globalStore = useAppStore()

  function refreshPage() {
    globalStore.routerLoading = true
    showPage.value = false
    keepAliveStore.removeKeepAliveName({ name: route.name?.toString(), path: route.fullPath })
    nextTick(() => {
      showPage.value = true
      globalStore.routerLoading = false
      keepAliveStore.addKeepAliveName({ name: route.name?.toString(), path: route.fullPath })
    })
  }

  provide('refresh', refreshPage)

  // 将创建的 VNode 缓存起来，必须配合 keep-alive 不可单独使用，否则每次是新的
  const cacheList = new Map<string, VNode | undefined>()

  const cacheIncludes = computed(() => keepAliveStore.list.map(e => getComponentName(e.path)))

  function getComponent(component: Component, router: RouteLocationNormalizedLoaded) {
    const name = getComponentName(router.fullPath)

    if (cacheIncludes.value.length === 0 || !cacheIncludes.value.includes(name))
      return component

    if (cacheList.has(name))
      return cacheList.get(name)

    const comp = createVNode({
      name,
      render: () => component,
    })
    cacheList.set(name, comp)
    return comp
  }
  return {
    cacheIncludes,
    getComponent,
    showPage,
  }
}
