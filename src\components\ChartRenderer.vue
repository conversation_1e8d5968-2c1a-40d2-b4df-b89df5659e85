<template>
  <div ref="containerRef" class="h-full w-full">
    <!-- HTML 渲染模式 -->
    <HtmlRender v-if="isHtml" :key="props.config || 'empty'" :html="props.config!" />

    <!-- 图表渲染模式 -->
    <div v-else-if="isChart" class="chart-container h-full w-full" />

    <!-- 无配置状态 -->
    <div v-else class="h-full flex items-center justify-center text-gray-400">
      暂无配置
    </div>
  </div>
</template>

<script lang="ts" setup>
import * as echarts from 'echarts'
import { computed, nextTick, onMounted, onUnmounted, watch } from 'vue'
import HtmlRender from './HtmlRender.vue'

interface Props {
  /** 图表配置对象 可能是html代码或者json字符串 */
  config?: string | null
  /** 图表主题 */
  theme?: string | object
  /** 图表初始化选项 */
  initOptions?: any
}

const props = withDefaults(defineProps<Props>(), {
  config: null,
  theme: undefined,
  initOptions: undefined,
})

const containerRef = useTemplateRef('containerRef')
let chartInstance: any = null

// 判断是否为 HTML 内容
const isHtml = computed(() => {
  return props.config ? isHtmlContent(props.config) : false
})

// 判断是否为图表内容
const isChart = computed(() => {
  return props.config && !isHtmlContent(props.config)
})

// 判断是否为 HTML 内容
function isHtmlContent(content: string): boolean {
  const trimmed = content.trim()
  return trimmed.startsWith('<') && trimmed.includes('>')
}

// 解析 JSON 配置
function parseJsonConfig(jsonString: string) {
  try {
    return JSON.parse(jsonString)
  }
  catch (error) {
    console.error('JSON 配置解析失败:', error)
    return null
  }
}

// 监听图表模式变化
watch(isChart, async (shouldShowChart) => {
  if (shouldShowChart && props.config) {
    await nextTick()
    initChart()
    renderChart(props.config)
  }
  else {
    disposeChart()
  }
}, { immediate: true })

// 监听配置变化（仅在图表模式下）
watch(() => props.config, async (newConfig) => {
  if (isChart.value && newConfig) {
    await nextTick()
    renderChart(newConfig)
  }
})

// 监听主题变化
watch(() => props.theme, async () => {
  if (isChart.value && props.config) {
    await nextTick()
    initChart()
    renderChart(props.config)
  }
})

// 初始化图表实例
function initChart() {
  if (!containerRef.value) {
    console.warn('容器引用不存在')
    return
  }

  const chartContainer = containerRef.value.querySelector('.chart-container')
  if (!chartContainer) {
    console.warn('图表容器不存在')
    return
  }

  // 清理之前的实例
  disposeChart()

  try {
    // 创建新实例
    chartInstance = echarts.init(chartContainer as HTMLElement, props.theme, props.initOptions)
    console.log('图表实例创建成功:', chartInstance)

    // 监听窗口大小变化
    const resizeHandler = () => {
      chartInstance?.resize()
    }
    window.addEventListener('resize', resizeHandler)

    // 保存resize处理器引用，用于清理
    chartInstance._resizeHandler = resizeHandler
  }
  catch (error) {
    console.error('图表初始化失败:', error)
  }
}

// 渲染图表
function renderChart(jsonConfig: string) {
  console.log('开始渲染图表:', jsonConfig)

  if (!jsonConfig) {
    console.warn('配置为空')
    return
  }

  const config = parseJsonConfig(jsonConfig)
  if (!config) {
    console.warn('配置解析失败')
    return
  }

  console.log('解析后的配置:', config)

  // 确保图表实例存在
  if (!chartInstance) {
    console.log('图表实例不存在，重新初始化')
    initChart()
  }

  if (!chartInstance) {
    console.error('图表实例初始化失败')
    return
  }

  try {
    chartInstance.setOption(config, true) // true 表示不合并，直接替换
    console.log('图表渲染成功')
  }
  catch (error) {
    console.error('图表渲染失败:', error)
  }
}

// 清理图表实例
function disposeChart() {
  if (chartInstance) {
    // 移除resize监听器
    if (chartInstance._resizeHandler) {
      window.removeEventListener('resize', chartInstance._resizeHandler)
    }
    chartInstance.dispose()
    chartInstance = null
  }
}

// 调整图表大小
function resize() {
  chartInstance?.resize()
}

// 获取图表实例
function getChartInstance() {
  return chartInstance
}

// 组件挂载时不需要额外初始化，watch 会自动处理
onMounted(() => {
  console.log('ChartRenderer 组件已挂载')
})

// 组件卸载时清理
onUnmounted(() => {
  disposeChart()
})

// 暴露方法给父组件
defineExpose({
  /** 渲染图表 */
  renderChart,
  /** 获取图表实例 */
  getChartInstance,
  /** 调整图表大小 */
  resize,
  /** 清理图表实例 */
  disposeChart,
  /** 重新初始化图表 */
  initChart,
})
</script>

<style scoped>
/* 确保容器有合适的尺寸 */
div {
  min-height: 100px;
  min-width: 100px;
}
</style>
