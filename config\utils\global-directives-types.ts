// export { }

// declare module 'vue' {

//   export interface ComponentCustomProperties {
//     vAuth: typeof import('./directives/auth')['default']
//   }
// }

import fs from 'node:fs'
import { exec } from 'node:child_process'
import path from 'node:path'

function readDirectory({ dir, out }: Options) {
  fs.readdir(dir, (err, files) => {
    if (err)
      return

    const target = files.filter(v => v !== 'index.ts')

    let str = `export { }
 declare module 'vue' {
    export interface ComponentCustomProperties {
 `
    target.forEach((name) => {
      const nat = name.split('.')[0]
      str += `  v${nat.charAt(0).toUpperCase() + nat.slice(1)}: typeof import('./directives/${name}')['default'] \n`
    })
    str += `    }\n}`
    fs.writeFileSync(out, str)
    exec(`eslint ${out} --fix`)
  })
}
function watchDirectory(options: Options) {
  fs.watch(options.dir, (eventType, filename) => {
    if (filename)
      readDirectory(options)
  })
}

interface Options { dir: string, out: string }

export default function globalDirectivesTypes(options: Options) {
  return {
    name: 'global-directives-types',
    buildStart() {
      // 读取指定目录下的文件名称
      options.dir = path.resolve(process.cwd(), options.dir)
      options.out = path.resolve(process.cwd(), options.out, 'global-directives-types.d.ts')

      readDirectory(options)
      watchDirectory(options)
    },

  }
}
