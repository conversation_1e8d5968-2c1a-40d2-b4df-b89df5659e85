<!-- 数字经济 -->
<template>
  <div class="grid grid-cols-1 gap-6 lg:grid-cols-3 md:grid-cols-2 xl:grid-cols-4">
    <a-card
      v-for="(card, index) in cards"
      :key="index"
      :hoverable="true"
      :bordered="false"
      class="card-item group transform overflow-hidden bg-white shadow-md transition-all duration-300 hover:shadow-xl hover:-translate-y-2"
      :body-style="{ padding: 0 }"
      @click="toLink(card.url)"
    >
      <!-- 图片容器 -->
      <div class="relative overflow-hidden">
        <img
          :src="card.image"
          :alt="card.title"
          class="h-56 w-full object-cover transition-transform duration-300 group-hover:scale-110"
        >
        <!-- 悬浮遮罩 -->
        <div class="absolute inset-0 bg-black/0 transition-all duration-300 group-hover:bg-black/20" />
      </div>

      <!-- 标题区域 -->
      <div class="p-6">
        <h3 class="text-lg text-gray-900 font-semibold transition-colors duration-200 group-hover:text-blue-600">
          {{ card.title }}
        </h3>
        <div class="mt-2 h-1 w-0 from-blue-500 to-purple-500 bg-gradient-to-r transition-all duration-300 group-hover:w-full" />
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import image2 from './images/sz_印度尼西亚.png'
import image7 from './images/sz_文莱.png'
import image5 from './images/sz_新加坡.png'
import image10 from './images/sz_柬埔寨.png'
import image6 from './images/sz_泰王国.png'
import image8 from './images/sz_缅甸.png'
import image9 from './images/sz_老挝.png'
import image4 from './images/sz_菲律宾.png'
import image1 from './images/sz_越南.png'
import image3 from './images/sz_马来西亚.png'

const cards = ref([
  {
    title: '中国-越南合作数据分析',
    url: 'https://139.an.ch2lab.cn/#/data-view/ASEAN?country=VietNam',
    image: image1,
  },
  {
    title: '中国-印度尼西亚合作数据分析',
    url: 'https://139.an.ch2lab.cn/#/data-view/ASEAN?country=Indonesia',
    image: image2,
  },
  {
    title: '中国-马来西亚合作数据分析',
    url: 'https://139.an.ch2lab.cn/#/data-view/ASEAN?country=Malaysia',
    image: image3,
  },
  {
    title: '中国-菲律宾合作数据分析',
    url: 'https://139.an.ch2lab.cn/#/data-view/ASEAN?country=Philippines',
    image: image4,
  },
  {
    title: '中国-新加坡合作数据分析',
    url: 'https://139.an.ch2lab.cn/#/data-view/ASEAN?country=Singapore',
    image: image5,
  },
  {
    title: '中国-泰王国合作数据分析',
    url: 'https://139.an.ch2lab.cn/#/data-view/ASEAN?country=Thailand',
    image: image6,
  },
  {
    title: '中国-文莱合作数据分析',
    url: 'https://139.an.ch2lab.cn/#/data-view/ASEAN?country=Brunei',
    image: image7,
  },
  {
    title: '中国-缅甸合作数据分析',
    url: 'https://139.an.ch2lab.cn/#/data-view/ASEAN?country=Myanmar',
    image: image8,
  },
  {
    title: '中国-老挝合作数据分析',
    url: 'https://139.an.ch2lab.cn/#/data-view/ASEAN?country=LaoPDR',
    image: image9,
  },
  {
    title: '中国-柬埔寨合作数据分析',
    url: 'https://139.an.ch2lab.cn/#/data-view/ASEAN?country=Cambodia',
    image: image10,
  },
])

function toLink(url: string) {
  window.open(url, '_blank')
}
</script>
