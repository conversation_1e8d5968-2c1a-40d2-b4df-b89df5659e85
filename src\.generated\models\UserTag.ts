import { User } from "./User";
import { FavoriteData } from "./FavoriteData";
export class UserTag {
  /**所属用户*/
  clientUserId: GUID = "00000000-0000-0000-0000-000000000000";
  /**用户*/
  clientUser?: User | null | undefined = null;
  name?: string | null | undefined = null;
  /**使用此标签的文章关联*/
  articleTags?: FavoriteData[] | null | undefined = [];
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
