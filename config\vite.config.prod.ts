/*
 * @Description: ^_^
 * @Author: sharebravery
 * @Date: 2022-12-08 11:58:47
 */
import { mergeConfig } from 'vite'
import compressPlugin from 'vite-plugin-compression'
import VueRouter from 'unplugin-vue-router/vite'
import baseConfig from './vite.config.base'
import virtualModulePlugin from './utils/useFileLayoutRouter'
import preventFullReloadPlugin from './utils/preventFullReloadPlugin'
import getEnv from './utils/getEnv'
import proxy from './utils/proxy'
import { manualChunks } from './utils/manualChunks'

const mode = 'prod'
const env = getEnv(mode)
const proxyTarget = env.VITE_APP_PROXY_TARGET

export default mergeConfig(
  {
    mode,
    server: {
      proxy: proxyTarget ? proxy(env) : undefined,
    },
    plugins: [
      preventFullReloadPlugin(),
      // https://uvr.esm.is/guide/file-based-routing.html
      VueRouter({

        /* options */
        dts: 'src/typed-router.d.ts',

        routesFolder: {
          src: 'src/views',
        },
        exclude: ['**/components/*.vue', 'components/**/*.vue', '**/*/components/**/*.vue'],
        watch: false,
      }),

      virtualModulePlugin(),
      compressPlugin({
        deleteOriginFile: false,
        algorithm: 'brotliCompress',
        ext: '.br',
      }),
    ],
    build: {
      target: 'es2015',
      outDir: 'dist',
      sourcemap: true,
      rollupOptions: {
        output: {
          manualChunks,
          chunkFileNames: 'js/[name]-[hash].js',  // 引入文件名的名称
          entryFileNames: 'js/[name]-[hash].js',  // 包的入口文件名称
          assetFileNames: '[ext]/[name]-[hash].[ext]', // 资源文件像 字体，图片等
        }
      }
    },
  },
  baseConfig,
)
