/**
 * 该文件仅能由main.ts引入，避免路由形成循环依赖
 */
import type { App } from 'vue'
import { UserService } from '@/api/user'
import { message } from 'ant-design-vue'
import { useFileRouter } from 'virtual:file-layout-router-module'
import router, { dynamicRouter } from '.'
import createRouterGuards from './utils/routerPermission'
import { useDynamicRouter } from './utils/useDynamicRouter'
// import { useFileRouter } from './utils/useFileRouter'
import { whiteListTest } from './utils/whiteList'

const { allRoutes, localRoutes, allRoutesNamedMap, localRoutesNamedMap } = useFileRouter()

const { dynamicRoutes, asyncRouteRegister, delUserRouter } = useDynamicRouter(router, allRoutes, allRoutesNamedMap, localRoutes, localRoutesNamedMap)

dynamicRouter.Delete = delUserRouter
dynamicRouter.Register = asyncRouteRegister

watch(dynamicRoutes, () => {
  dynamicRouter.setRoutes(dynamicRoutes.value, allRoutes, localRoutes)
}, {
  immediate: true,
})

/**
 * @description: 页面初始化
 * @param undefined
 * @return {*}
 * @author: luckymiaow
 */

export async function setupRouter(app: App<Element>) {
  console.log('%c [ localRoutes ]-36', 'font-size:13px; background:pink; color:#bf2c9f;', localRoutes)
  localRoutes.map(router.addRoute)
  const { token: getToken } = useUserStore()

  if (whiteListTest(location.pathname?.split('?')?.[0] || '') === false) {
    try {
      if (getToken) {
        await UserService.updateUserInfo()
        // signalR.init('/api/msgHub');
        await asyncRouteRegister()
      }
    }
    catch (err: any) {
      message.error(err.message)
    }
  }
  createRouterGuards(router)
  app.use(router)
}
