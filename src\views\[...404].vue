<!--
 * @Author: joyet
 * @Date: 2021-10-26 09:22:34
 * @LastEditTime: 2023-07-06 18:03:36
 * @LastEditors: luckymiaow
 * @Description:
 * @FilePath: \nanning-jiwei-xuncha-vue\src\views\404.vue
 *
-->

<template>
  <a-result status="404" title="404" sub-title="对不起，你所访问的页面不存在！">
    <template #extra>
      <router-link :to="HomeRoute.path">
        <a-button type="primary">
          回到首页
        </a-button>
      </router-link>
      <router-link to="/logoutReload">
        <a-button type="primary">
          退出登录
        </a-button>
      </router-link>
    </template>
  </a-result>
</template>

<script lang="ts" setup>
import { HomeRoute } from '@/router/utils/useRouteConfig'

definePage({
  meta: {
    local: true,
    layoutRoute: {
      meta: { hidden: true, authorize: [] },
    },
    authorize: [],
  },
})
</script>
