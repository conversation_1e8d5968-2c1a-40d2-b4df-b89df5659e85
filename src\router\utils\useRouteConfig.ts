/*
 * @Description: 当前访问路由持久化
 * @Author: luckymiaow
 * @Date: 2022-09-02 10:08:45
 * @LastEditors: luckymiaow
 */

import type { UserRoleViewModel } from '@/api/models'
import type { RouteNamedMap } from 'vue-router/auto-routes'
import { _Role } from '@/permission/RoleName'
import { reactive } from 'vue'

/* 首页 */
export const HomeRoute = reactive<{ path: keyof RouteNamedMap }>({
  path: '/dept-client/',
})

export const LoginRoute = reactive<{ path: keyof RouteNamedMap }>({
  path: '/login/',
})

export function setHomeRoute(data: { path: keyof RouteNamedMap }) {
  HomeRoute.path = data.path
}

export function setHomeRouteByRoles(_roles: UserRoleViewModel[]) {
  if (_roles.some(p => p.name === _Role.超级管理员 || p.name === _Role.监测研判人员)) {
    HomeRoute.path = '/home'
  }
  else {
    HomeRoute.path = '/dept-client/'
  }
}

(function isMobileDevice() {
  // const screenWidth = window.innerWidth;
  // if (!/Mobi|Android|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop/i.test(navigator.userAgent) && screenWidth > 500)
})()
