/*
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-11-07 14:38:30
 * @LastEditors: 景 彡
 */
export function enumToObject<T>(enumData: T): { label: string | number, value: keyof T }[] {
  const data: { label: string | number, value: keyof T }[] = []
  const regPos = /^(?:0|[1-9]\d*|-[1-9]\d*)$/
  for (const key in enumData) {
    if (!regPos.test(key))
      data.push({ value: enumData[key] as any, label: key })
  }

  data.sort((a, b) => {
    return (a.value as number) - (b.value as number)
  })
  return data
}

export function enumToString<T>(enumData: T): Record<keyof T, string> {
  const temp: any = {}

  enumToObject(enumData).forEach((item) => {
    temp[item.label] = item.label
  })

  return temp as Record<keyof T, string>
}
