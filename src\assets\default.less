@import 'ant-design-vue/dist/reset.css';

// 定义变量
@scrollbar-width: 6px;
@scrollbar-track-color: var(--ch2-color-border);
@scrollbar-thumb-color: var(--ch2-color-primary-bg-hover);
@scrollbar-thumb-radius: 8px;

// 定义混合
.scrollbar-style(@width, @trackColor, @thumbColor, @thumbRadius) {
  & {
    width: @width;
  }
  &-track {
    background: @trackColor;
  }
  &-thumb {
    background: @thumbColor;
    border-radius: @thumbRadius;
  }
}

/* Webkit (Safari/Chrome) */
::-webkit-scrollbar {
  .scrollbar-style(@scrollbar-width, @scrollbar-track-color, @scrollbar-thumb-color, @scrollbar-thumb-radius);
}

/* Firefox */
::-moz-scrollbar {
  .scrollbar-style(@scrollbar-width, @scrollbar-track-color, @scrollbar-thumb-color, @scrollbar-thumb-radius);
}

/* IE/Edge */
::-ms-scrollbar {
  .scrollbar-style(@scrollbar-width, @scrollbar-track-color, @scrollbar-thumb-color, @scrollbar-thumb-radius);
}

.dp-text-ellipsis-wrapper .btn {
  color: var(--ch2-color-primary-text) !important;
}

body {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  background-color: #f9fafb;
}
