[{"name": "缅甸", "details": {"counts": {"产业规模": "未公开具体数值（政府计划投入3.5亿美元发展AI产业）", "AI企业相关数量": "10+家（含国际合作项目）", "AI专利数量": "未公开（依赖技术引进）"}, "invest": [{"name": "2021", "value": "0.02"}, {"name": "2022", "value": "0.04"}, {"name": "2023", "value": "0.08"}, {"name": "2024", "value": "0.15"}, {"name": "2025（预测）", "value": "0.25"}], "industryAccountedFor": [{"name": "农业与防灾", "value": 40, "note": "AI用于气候适应型农业和地震预警系统"}, {"name": "医疗健康", "value": 20, "note": "远程医疗试点（如中缅英三语AI翻译系统）[[29][38]]"}, {"name": "能源与矿业", "value": 25, "note": "智能矿山技术合作（如中国“愚公”系统）"}, {"name": "政府服务", "value": 10, "note": "应急管理和多语言公共服务"}, {"name": "国际合作", "value": 5, "note": "中缅AI联合研究项目占比"}]}}, {"name": "文莱", "details": {"counts": {"产业规模": "（政府推动《数字经济总体规划2025》，AI作为重点领域）", "AI企业相关数量": "10+家（含初创企业及国际合作项目）", "AI专利数量": "未公开（依赖技术引进与国际合作）"}, "invest": [{"name": "2021", "value": "0.03"}, {"name": "2022", "value": "0.05"}, {"name": "2023", "value": "0.10"}, {"name": "2024", "value": "0.20"}, {"name": "2025（预测）", "value": "0.35"}], "industryAccountedFor": [{"name": "能源与工业", "value": 40, "note": "石油经济转型中AI应用（如智能油田监测）"}, {"name": "医疗健康", "value": 25, "note": "远程医疗和AI诊断试点"}, {"name": "政府与公共服务", "value": 30, "note": "智慧城市和数字政务优先"}, {"name": "教育", "value": 20, "note": "AI人才培养和研究中心建设"}, {"name": "其他（含国际合作）", "value": 35, "note": "如东盟联合项目及中国技术合作"}]}}, {"name": "老挝", "details": {"counts": {"产业规模": "0.7~1.2亿美元", "AI企业相关数量": "15+家", "AI专利数量": "30+"}, "invest": [{"name": "2021", "value": "0.05"}, {"name": "2022", "value": "0.08"}, {"name": "2023", "value": "0.15"}, {"name": "2024", "value": "0.27"}], "industryAccountedFor": [{"name": "电子行业", "value": 137}, {"name": "教育行业", "value": 311}, {"name": "金融行业", "value": 31}, {"name": "人工智能", "value": 121}, {"name": "其他", "value": 214}]}}, {"name": "菲律宾", "details": {"counts": {"产业规模": "10.3 亿美元", "AI企业相关数量": "98家", "AI专利数量": 2183}, "invest": [{"name": "2021", "value": "1.5"}, {"name": "2022", "value": "2.3"}, {"name": "2023", "value": "5.2"}, {"name": "2024", "value": "9.5"}], "industryAccountedFor": [{"name": "电子行业", "value": 1021}, {"name": "教育行业", "value": 1376}, {"name": "金融行业", "value": 103}, {"name": "人工智能", "value": 857}, {"name": "其他", "value": 690}]}}, {"name": "柬埔寨", "details": {"counts": {"产业规模": "4.16 亿美元", "AI企业相关数量": "500+", "AI专利数量": 23}, "invest": [{"name": "2021", "value": "0.08"}, {"name": "2022", "value": "0.1"}, {"name": "2023", "value": "0.25"}, {"name": "2024", "value": "0.65"}], "industryAccountedFor": [{"name": "电子行业", "value": 277}, {"name": "教育行业", "value": 478}, {"name": "金融行业", "value": 40}, {"name": "人工智能", "value": 245}, {"name": "其他", "value": 300}]}}, {"name": "泰国", "details": {"counts": {"产业规模": "12.6 亿美元", "AI企业相关数量": "188家", "AI专利数量": 989}, "invest": [{"name": "2021", "value": "15"}, {"name": "2022", "value": "65"}, {"name": "2023", "value": "100"}, {"name": "2024", "value": "180"}], "policy": [{"name": "《国家人工智能伦理指南》", "time": "2020年7月"}, {"name": "《国家人工智能行动计划（2022-2027）》", "time": "2022年3月"}, {"name": "《人工智能合作谅解备忘录》", "time": "2023年6月"}, {"name": "《PDPA修正案（AI数据条款）》", "time": "2023年7月"}, {"name": "《关于个人数据安全数据安全的规则和要求》", "time": "2023年12月"}, {"name": "《AI行业税收优惠法案》", "time": "2024年6月"}], "enterprises": [{"name": "Drvr", "time": "（从车队管理到运动 AI，聚焦物联网与数据价值）"}, {"name": "Gopomelo", "time": "（云服务 + 本地化 AI，助力东南亚企业数字化）"}, {"name": "Techland Group", "time": "（电力电子支撑数字基建，绑定全球算力需求）"}, {"name": "Thai Telecommunications", "time": "（通信基建 + 生态整合，驱动泰国 5G 与行业数字化）"}, {"name": "instinctools", "time": "（创意驱动的数字化专家，主打高端品牌体验）"}, {"name": "Geniusee", "time": "（全球化 IT 外包，以敏捷开发和行业适配性见长）"}, {"name": "Innowise Group", "time": "（高增长领域全栈服务商，Web3 与 AI 双轮驱动）"}, {"name": "AE Studio", "time": "（国防级仿真龙头，技术壁垒显著，延伸至工业数字化）"}], "industryAccountedFor": [{"name": "电子行业", "value": 1071}, {"name": "教育行业", "value": 996}, {"name": "金融行业", "value": 88}, {"name": "人工智能", "value": 1479}, {"name": "其他", "value": 663}], "word": [{"name": "AI", "value": 574}, {"name": "OpenAI", "value": 328}, {"name": "ChatGPT", "value": 278}, {"name": "人工智能", "value": 234}, {"name": "Gemini", "value": 126}, {"name": "เทคโนโลยี", "value": 111}, {"name": "DeepSeek", "value": 110}, {"name": "ปัญญาประดิษฐ์", "value": 102}, {"name": "自然语言处理", "value": 93}, {"name": "Google", "value": 84}, {"name": "<PERSON>", "value": 82}, {"name": "机器学习", "value": 81}, {"name": "深度学习", "value": 68}, {"name": "泰国", "value": 68}, {"name": "<PERSON><PERSON><PERSON>", "value": 68}, {"name": "Llama", "value": 65}, {"name": "Grok", "value": 62}, {"name": "GPT-4", "value": 61}, {"name": "<PERSON>", "value": 58}, {"name": "โมเดล", "value": 57}, {"name": "Meta", "value": 55}, {"name": "Ai", "value": 50}, {"name": "<PERSON><PERSON>", "value": 49}, {"name": "技术", "value": 48}, {"name": "<PERSON><PERSON>", "value": 48}, {"name": "Do<PERSON><PERSON>", "value": 48}, {"name": "语言模型", "value": 46}, {"name": "Anthropic", "value": 44}, {"name": "GPT", "value": 44}, {"name": "LLM", "value": 44}, {"name": "教育", "value": 43}, {"name": "xAI", "value": 42}, {"name": "中国", "value": 42}, {"name": "创新", "value": 41}, {"name": "ByteDance", "value": 40}, {"name": "จีน", "value": 38}, {"name": "แชทบอท", "value": 37}, {"name": "บริษัท", "value": 36}, {"name": "<PERSON><PERSON><PERSON>", "value": 35}, {"name": "<PERSON>", "value": 35}]}}, {"name": "印度尼西亚", "details": {"counts": {"产业规模": "2.13 亿美元", "AI企业相关数量": "2500+", "AI专利数量": 713}, "invest": [{"name": "2021", "value": "8"}, {"name": "2022", "value": "12"}, {"name": "2023", "value": "18"}, {"name": "2024", "value": "25"}], "policy": [{"name": "《2020–2045国家人工智能战略》（Stranas KA）", "time": "2020年"}, {"name": "《个人数据保护法》", "time": "2022年10月"}, {"name": "《关于AI伦理使用的通函》", "time": "2023年12月"}, {"name": "《人工智能法规》（拟定中）", "time": "2025年1月"}], "enterprises": [{"name": "Gojek", "time": "（通过整合AI技术优化了用户体验和运营效率）"}, {"name": "Traveloka", "time": "（是印尼领先的在线旅游预订平台，利用AI技术提供个性化推荐和服务）"}, {"name": "Kata.ai", "time": "（专注于对话式AI，应用于客户服务系统）"}, {"name": "<PERSON><PERSON>", "time": "（将生成式AI用于企业会计和客户管理）"}, {"name": "Indosat Ooredoo <PERSON>", "time": "（与英伟达合作，计划于2024年在中爪哇的梭罗市（苏拉卡尔塔）建立一座价值2亿美元的人工智能中心）"}], "industryAccountedFor": [{"name": "电子行业", "value": 1286}, {"name": "教育行业", "value": 1373}, {"name": "金融行业", "value": 91}, {"name": "人工智能", "value": 1393}, {"name": "其他", "value": 591}], "word": [{"name": "美国", "value": 602}, {"name": "中国", "value": 586}, {"name": "AI", "value": 473}, {"name": "人工智能", "value": 347}, {"name": "ChatGPT", "value": 342}, {"name": "OpenAI", "value": 315}, {"name": "Gemini", "value": 155}, {"name": "DeepSeek", "value": 147}, {"name": "<PERSON><PERSON><PERSON>", "value": 141}, {"name": "Google", "value": 103}, {"name": "自然语言处理", "value": 102}, {"name": "<PERSON>", "value": 97}, {"name": "Llama", "value": 89}, {"name": "<PERSON><PERSON><PERSON>", "value": 86}, {"name": "深度学习", "value": 86}, {"name": "机器学习", "value": 85}, {"name": "Indonesia", "value": 84}, {"name": "技术", "value": 83}, {"name": "<PERSON>", "value": 83}, {"name": "教育", "value": 77}, {"name": "Grok", "value": 70}, {"name": "创新", "value": 65}, {"name": "Teknologi", "value": 64}, {"name": "语言模型", "value": 63}, {"name": "<PERSON>ee", "value": 62}, {"name": "<PERSON>", "value": 60}, {"name": "Cohere", "value": 58}, {"name": "Alibaba", "value": 57}, {"name": "文本分析", "value": 55}, {"name": "China", "value": 51}, {"name": "印度尼西亚", "value": 50}, {"name": "聊天机器人", "value": 44}, {"name": "GPT", "value": 44}, {"name": "<PERSON><PERSON>", "value": 44}, {"name": "Meta", "value": 43}, {"name": "ByteDance", "value": 43}, {"name": "设计", "value": 42}, {"name": "<PERSON><PERSON>", "value": 42}, {"name": "价格", "value": 40}]}}, {"name": "新加坡", "details": {"counts": {"产业规模": "112.38亿美元", "AI企业相关数量": "1100+", "AI专利数量": 4536}, "invest": [{"name": "2021", "value": "15"}, {"name": "2022", "value": "22"}, {"name": "2023", "value": "35"}, {"name": "2024", "value": "50"}], "policy": [{"name": "《全国人工智能策略》（NAIS 1.0）", "time": "2019年"}, {"name": "《全国人工智能策略2.0》（NAIS 2.0）", "time": "2023年12月"}, {"name": "《全球首个生成式人工智能治理框架草案》", "time": "2024年1月"}, {"name": "《用于生成式人工智能的人工智能模型管理框架》", "time": "2024年5月30日"}], "enterprises": [{"name": "ADVANCE.AI", "time": "（总部位于新加坡,致力于利用al、大数据和云计算技术提供数字化解决方案。"}, {"name": "Pand.AI", "time": "（成立于2016年,总部位于新加坡,专注于提供AI聊天机器人,AI数字人、AI数字员工等智能服务）"}, {"name": "AI Rudder", "time": "（主要聚焦语音识别、语音合成、自然语言处理和语义理解相关技术和应用）"}, {"name": "ViSenze", "time": "（由新加坡国立大学(NUS)的研究人员创立于2012年,专注于为电商和零售行业提供视觉搜索和图像识别技术）"}, {"name": "Wiz.ai", "time": "（专注于提供智能语音助手和对话式AI解决方案）"}, {"name": "Sentient.io", "time": "（为用户提供了包括计算机视觉、自然语言处理、语音识别和预测分析等AI应用）"}, {"name": "Cosmose AI", "time": "（使用AI分析来跟踪店内客流量并与在线购物者互动,并利用数字广告预测消费者在线下购物的消费行为）"}], "industryAccountedFor": [{"name": "电子行业", "value": 1354}, {"name": "教育行业", "value": 949}, {"name": "金融行业", "value": 152}, {"name": "人工智能", "value": 1972}, {"name": "其他", "value": 448}], "word": [{"name": "美国", "value": 578}, {"name": "人工智能", "value": 572}, {"name": "中国", "value": 549}, {"name": "AI", "value": 518}, {"name": "OpenAI", "value": 347}, {"name": "ChatGPT", "value": 334}, {"name": "<PERSON><PERSON><PERSON>", "value": 190}, {"name": "语言模型", "value": 169}, {"name": "自然语言处理", "value": 168}, {"name": "机器学习", "value": 149}, {"name": "Gemini", "value": 147}, {"name": "深度学习", "value": 141}, {"name": "技术", "value": 128}, {"name": "模型", "value": 128}, {"name": "Singapore", "value": 121}, {"name": "新加坡", "value": 117}, {"name": "风扇", "value": 105}, {"name": "DeepSeek", "value": 99}, {"name": "创新", "value": 94}, {"name": "聊天机器人", "value": 88}, {"name": "Meta", "value": 77}, {"name": "ChatGLM", "value": 74}, {"name": "Llama", "value": 73}, {"name": "Cohere", "value": 71}, {"name": "命令", "value": 70}, {"name": "文本分析", "value": 69}, {"name": "Google", "value": 67}, {"name": "谷歌", "value": 65}, {"name": "远程控制", "value": 60}, {"name": "<PERSON>", "value": 59}, {"name": "GPT-4", "value": 58}, {"name": "LLM", "value": 57}, {"name": "腾讯", "value": 57}, {"name": "GPT", "value": 52}, {"name": "开源", "value": 51}, {"name": "MISTRAL", "value": 51}, {"name": "价格", "value": 50}, {"name": "<PERSON><PERSON><PERSON>", "value": 48}, {"name": "投资", "value": 47}, {"name": "教育", "value": 47}]}}, {"name": "越南", "details": {"counts": {"产业规模": "7.53亿美元", "AI企业相关数量": "270+", "AI专利数量": 341}, "invest": [{"name": "2021", "value": "6"}, {"name": "2022", "value": "10"}, {"name": "2023", "value": "15"}, {"name": "2024", "value": "22"}], "policy": [{"name": "《到2030年人工智能研究、发展和应用国家战略》", "time": "2021年9月"}, {"name": " 《第13号法令（AI数据保护规范）》", "time": "2023年"}, {"name": "《负责任AI研发的九项原则》", "time": "2024年"}, {"name": "《数字技术产业法》（计划出台）", "time": "2024年6月"}, {"name": "《第182号决议（激励政策）》", "time": "2025年"}], "enterprises": [{"name": "FPT集团", "time": "（AI+云计算+芯片设计，构建越南全球科技竞争力）"}, {"name": "军队工业电信集团（Viettel）", "time": "（5G基建+智慧生态，驱动国家数字化转型）"}, {"name": "越南最大私营集团Vingroup", "time": "（AI研发+电动汽车生态，引领智能出行与绿色能源）"}, {"name": "<PERSON><PERSON>（VNG旗下）", "time": "（社交+通讯+支付，打造越南本土数字生活入口）"}], "industryAccountedFor": [{"name": "教育行业", "value": 902}, {"name": "电子行业", "value": 1307}, {"name": "金融行业", "value": 57}, {"name": "人工智能", "value": 1661}, {"name": "其他", "value": 453}], "word": [{"name": "AI", "value": 478}, {"name": "OpenAI", "value": 400}, {"name": "ChatGPT", "value": 368}, {"name": "DeepSeek", "value": 181}, {"name": "人工智能", "value": 157}, {"name": "<PERSON><PERSON>", "value": 152}, {"name": "<PERSON>r<PERSON> tuệ nhân tạo", "value": 148}, {"name": "Gemini", "value": 136}, {"name": "Google", "value": 126}, {"name": "<PERSON><PERSON><PERSON>", "value": 119}, {"name": "<PERSON><PERSON><PERSON>", "value": 104}, {"name": "<PERSON><PERSON>", "value": 94}, {"name": "trí tuệ nhân tạo", "value": 83}, {"name": "Alibaba", "value": 76}, {"name": "chatbot", "value": 72}, {"name": "Tencent", "value": 72}, {"name": "<PERSON><PERSON><PERSON> nghệ", "value": 68}, {"name": "Grok", "value": 67}, {"name": "GPT-4", "value": 66}, {"name": "Llama", "value": 65}, {"name": "<PERSON><PERSON>", "value": 65}, {"name": "Cohere", "value": 61}, {"name": "Meta", "value": 59}, {"name": "越南", "value": 59}, {"name": "Anthropic", "value": 59}, {"name": "Chat GPT", "value": 58}, {"name": "Baidu", "value": 58}, {"name": "<PERSON>", "value": 57}, {"name": "Việt Nam", "value": 55}, {"name": "<PERSON><PERSON><PERSON>", "value": 55}, {"name": "<PERSON>", "value": 53}, {"name": "<PERSON><PERSON><PERSON><PERSON> tri<PERSON>n", "value": 51}, {"name": "xAI", "value": 51}, {"name": "GPT", "value": 51}, {"name": "Ai", "value": 50}, {"name": "Do<PERSON><PERSON>", "value": 45}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "value": 44}, {"name": "机器学习", "value": 44}, {"name": "命令", "value": 44}, {"name": "自然语言处理", "value": 41}]}}, {"name": "马来西亚", "details": {"counts": {"产业规模": "10.6亿美元", "AI企业相关数量": "140 家", "AI专利数量": 132}, "invest": [{"name": "2023", "value": "43"}, {"name": "2024", "value": "86"}, {"name": "2025", "value": "120"}], "policy": [{"name": "《国家人工智能路线图（2021-2025）》", "time": "20121年"}, {"name": "《人工智能治理与伦理指南》", "time": "2024年"}, {"name": "国家人工智能办公室成立", "time": "2024年"}, {"name": "《人工智能治理和伦理国家指南》", "time": "2024年9月20日"}, {"name": "数字企业 AI 领域税收优惠", "time": "2025年5月31日"}, {"name": "AI 基础设施加速计划", "time": "2024 年"}], "enterprises": [{"name": "云顶杨忠礼集团（YTL）", "time": ""}, {"name": "Telekom Malaysia（TM）", "time": ""}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>（原 Prestariang）", "time": ""}, {"name": "微软（Microsoft Azure）", "time": ""}, {"name": "阿里云", "time": ""}], "industryAccountedFor": [{"name": "教育行业", "value": 959}, {"name": "电子行业", "value": 1529}, {"name": "金融行业", "value": 79}, {"name": "人工智能", "value": 1074}, {"name": "其他", "value": 573}], "word": [{"name": "人工智能", "value": 365}, {"name": "OpenAI", "value": 318}, {"name": "AI", "value": 267}, {"name": "ChatGPT", "value": 251}, {"name": "<PERSON><PERSON><PERSON>", "value": 168}, {"name": "DeepSeek", "value": 132}, {"name": "马来西亚", "value": 120}, {"name": "Gemini", "value": 108}, {"name": "中国", "value": 99}, {"name": "自然语言处理", "value": 97}, {"name": "机器学习", "value": 91}, {"name": "美国", "value": 80}, {"name": "<PERSON>", "value": 80}, {"name": "深度学习", "value": 78}, {"name": "Llama", "value": 72}, {"name": "技术", "value": 68}, {"name": "聊天机器人", "value": 63}, {"name": "语言模型", "value": 61}, {"name": "<PERSON>", "value": 59}, {"name": "Malaysia", "value": 57}, {"name": "<PERSON><PERSON>", "value": 56}, {"name": "<PERSON>", "value": 55}, {"name": "创新", "value": 53}, {"name": "GPT", "value": 51}, {"name": "模型", "value": 46}, {"name": "Grok", "value": 46}, {"name": "<PERSON><PERSON><PERSON>", "value": 41}, {"name": "教育", "value": 41}, {"name": "Ai", "value": 40}, {"name": "AI模型", "value": 39}, {"name": "机器人", "value": 39}, {"name": "xAI", "value": 39}, {"name": "Google", "value": 37}, {"name": "Facebook", "value": 36}]}}]