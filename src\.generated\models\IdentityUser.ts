export class IdentityUser<TKey> {
  id?: TKey | null | undefined = null;
  name?: string | null | undefined = null;
  userName?: string | null | undefined = null;
  email?: string | null | undefined = null;
  emailConfirmed: boolean = false;
  phoneNumber?: string | null | undefined = null;
  phoneNumberConfirmed: boolean = false;
  twoFactorEnabled: boolean = false;
  passwordHash?: string | null | undefined = null;
  securityStamp?: string | null | undefined = null;
  loginSecurityStamp?: string | null | undefined = null;
  lockoutEnd?: Dayjs | null | undefined = null;
  lockoutEnabled: boolean = false;
  modifyPasswordEnabled: boolean = false;
  modifyPasswordEnd?: Dayjs | null | undefined = null;
  expiration?: Dayjs | null | undefined = null;
}
